import WindowService from "../services/window.service"
import { agreementService, loadingService, transactionService } from "../services/_singletons"
import HelperService from "../services/helper.service"
import { PDFDocument } from "pdf-lib"

export function useHelper() {
    let windowRef: Window = null
    const downloadTransactionPdf = async (transaction_code: number, type: string) => {
        await downloadApiCall(
            await transactionService.downloadTransaction(transaction_code, [
                {
                    key: "type",
                    value: type
                }
            ])
        )
    }

    async function convertBase64ToPDF(base64) {
        // Clean Base64 string if necessary

        const cleanBase64 = base64.replace(/^data:application\/pdf;base64,/, "")

        // Decode Base64 to binary data
        const binaryString = window.atob(cleanBase64)
        const len = binaryString.length
        const bytes = new Uint8Array(len)

        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i)
        }

        try {
            // Load the PDF document from binary data
            const pdfDoc = await PDFDocument.load(bytes)

            // Save the PDF document to a new file
            const pdfBytes = await pdfDoc.save()

            console.log("PDF created successfully!")
        } catch (error) {
            console.error("Error creating PDF:", error.message)
        }
    }

    const downloadMonthlyInvoicePdf = async invoice_id => {
        const response = await transactionService.downloadMonthlyInvoice(invoice_id)
        const filePaths = [response.file, response.file1]
        console.log("File paths ", filePaths)

        const mergedPdf = await PDFDocument.create()

        for (const filePath of filePaths) {
            if (filePath != undefined && filePath != "") {
                const cleanBase64 = filePath.replace(/^data:application\/pdf;base64,/, "")
                const binaryString = window.atob(cleanBase64)
                const len = binaryString.length
                const bytes = new Uint8Array(len)
                for (let i = 0; i < len; i++) {
                    bytes[i] = binaryString.charCodeAt(i)
                }
                console.log("Bytes ", bytes)
                const pdfDoc = await PDFDocument.load(bytes)
                console.log("test")
                const pages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices())
                pages.forEach(page => mergedPdf.addPage(page))
                console.log("here####")
            }
        }

        const url = URL.createObjectURL(await HelperService.toBlob(await mergedPdf.saveAsBase64({ dataUri: true })))
        const windowRef = WindowService.openWindow("Monthly Invoice Preview")
        windowRef.location = url
    }

    /*  const downloadMonthlyInvoicePdf = async (invoice_id) => {
        const response = await transactionService.downloadMonthlyInvoice(invoice_id);
        const filePaths = [response.file, response.file1];
        console.log("File paths ",filePaths);
        await (async () => {
            const mergedPdf = await PDFDocument.create();
            for (const filePath of filePaths) {
                if (filePath != undefined) {
                    const pdfDoc = await PDFDocument.load(filePath as string);
                    const pages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
                    pages.forEach((page) => mergedPdf.addPage(page));
                }
            }
            const url = URL.createObjectURL(
                await HelperService.toBlob(await mergedPdf.saveAsBase64({dataUri: true})),
            );
            const windowRef = WindowService.openWindow('Monthly Invoice Preview');
            windowRef.location = url;
        })();
        //  await downloadApiCall(await transactionService.downloadMonthlyInvoice(invoice_id))
    } */
    const downloadApiCall = async asyncCall => {
        windowRef = WindowService.openWindow("Pdf Preview")
        try {
            loadingService.startLoading("main-loader:login")
            const response = await asyncCall
            const blob = await HelperService.toBlob(`data:application/pdf;base64,${encodeURI(response.file)}`)
            windowRef.location = URL.createObjectURL(blob)
        } catch (error) {
            windowRef.close()
        } finally {
            loadingService.stopLoading("main-loader:login")
            windowRef = null
        }
    }
    const downloadAgreementPdf = async (company_id: number) => {
        await downloadApiCall(await agreementService.downloadAgreement(company_id))
    }
    const downloadTemplateAgreementPdf = async () => {
        await downloadApiCall(await agreementService.getTemplateAgreementPdf())
    }
    return { downloadTransactionPdf, downloadMonthlyInvoicePdf, downloadAgreementPdf, downloadTemplateAgreementPdf }
}
