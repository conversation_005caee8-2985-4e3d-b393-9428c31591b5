import { create<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>N<PERSON><PERSON>,
    NavigationGuardWithThis,
    RouteLocationNormalized,
    Router,
    RouteRecordRaw,
    createWebHistory } from "vue-router"

//apps
import Dashboard from "../views/apps/Dashboard.vue"
import CryptoDashboard from "../views/apps/CryptoDashboard.vue"
import EcommerceDashboard from "../views/apps/ecommerce/Dashboard.vue"
import Calendar from "../views/apps/Calendar.vue"
import Contacts from "../views/apps/Contacts.vue"
import Gallery from "../views/apps/Gallery.vue"
import Cards from "../views/apps/Cards.vue"
import Mail from "../views/apps/Mail.vue"
import Ecommerce from "./ecommerce"
/*

//pages
*/
// import Login from "../views/pages/authentication/Login.vue"
import AddOpt from "../views/pages/authentication/Opt.vue"
import NewPassword from "../views/pages/authentication/NewPassword.vue"
import ForgotPassword from "../views/pages/authentication/ForgotPassword.vue"
import Profile from "../views/pages/Profile.vue"
import NotFound from "../views/pages/NotFound.vue"
import Invoice from "../views/pages/Invoice.vue"

// Merchant
import MerchantList from "../views/pages/merchant/MerchantList.vue"
import AddMerchant from "../views/pages/merchant/AddMerchant.vue"
import AddCustomerAgreement from "../views/pages/merchant/AddCustomerAgreement.vue"

// Agreement
import AgreementList from "../views/pages/agreement/AgreementList.vue"
import AddAgreement from "../views/pages/agreement/AddAgreement.vue"
import CreateAgreementTemplate from "../views/pages/agreement/CreateAgreementTemplate.vue"

// Ubsend
import UbsendList from "../views/pages/ubsend/UbsendList.vue"
import AddUbsend from "../views/pages/ubsend/AddUbsend.vue"
import ViewUbsend from "../views/pages/ubsend/UbsendView.vue"
import UploadUbsendFile from "../views/pages/ubsend/UploadUbsendFile.vue"

// PriceGroup
import PriceGroup from "../views/pages/priceGroup/PriceGroupList.vue"
import AddPriceGroup from "../views/pages/priceGroup/AddPriceGroup.vue"

// Video
import VideoList from "../views/pages/video/VideoList.vue"

// Carrier
import CarrierList from "../views/pages/carrier/CarrierList.vue"

//ui
import layout from "./layout"
import Themes from "../views/ui/Themes.vue"
import Icons from "../views/ui/Icons/Icons.vue"
import MdIcons from "../views/ui/Icons/MdIcons.vue"
import FlagIcons from "../views/ui/Icons/FlagIcons.vue"
import MultiLanguage from "../views/ui/MultiLanguage.vue"
import HelperClasses from "../views/ui/HelperClasses.vue"
import Typography from "../views/ui/Typography.vue"
import element from "./element"
import tables from "./tables"
import maps from "./maps"
import editors from "./editors"
import charts from "./charts"

import layouts from "../layout"
import { useMainStore } from "@/stores/main"
import type { StateLayout } from "@/types"


// const AuthGuard: NavigationGuardWithThis<undefined> = (
//     to: RouteLocationNormalized,
//     _from: RouteLocationNormalized,
//     next: NavigationGuardNext,
//   ): void => {
//     const authLocal: string | null = localStorage.getItem('token');
//     const authSession: string | null = sessionStorage.getItem('token');
  
//     const isAuthorized = Boolean(authLocal || authSession);
  
//     if (isAuthorized && (to.path === '/login' || to.path === '/sign')) {
//       return next('/');
//     }
  
//     if (!isAuthorized && to.path !== '/login' && to.path !== '/sign') {
//       return next('/login');
//     }
  
//     return next();
//   };


const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: "/",
            alias: "/dashboard",
            name: "dashboard",
            // component: Dashboard,
            // beforeEnter: [AuthGuard],
            redirect: '/ubsend/list', 
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app"]
            }
        },
        // {
        //     path: "/",
        //     alias: "/dashboard",
        //     name: "dashboard",
        //     component: Dashboard,
        //     meta: {
        //         auth: true,
        //         layout: layouts.navLeft,
        //         searchable: true,
        //         tags: ["app"]
        //     }
        // },
        {
            path: "/ecommerce-dashboard",
            name: "ecommerce-admin-dashboard",
            component: EcommerceDashboard,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "eCommerce admin dashboard",
                tags: ["app", "Ecommerce"]
            }
        },
        {
            path: "/crypto-dashboard",
            alias: "/dashboards",
            name: "crypto-dashboard",
            component: CryptoDashboard,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app", "Crypto"]
            }
        },
        {
            path: "/calendar",
            name: "calendar",
            component: Calendar,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app"]
            }
        },
        {
            path: "/contacts",
            name: "contacts",
            component: Contacts,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["users", "address", "book", "app"]
            }
        },
        {
            path: "/gallery",
            name: "gallery",
            component: Gallery,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["photo", "app"]
            }
        },
        {
            path: "/cards",
            name: "cards",
            component: Cards,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app", "todo"]
            }
        },
        {
            path: "/mail",
            name: "mail",
            component: Mail,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "Mail",
                tags: ["app", "email", "inbox"]
            }
        },
        Ecommerce,
        layout,
        {
            path: "/themes",
            name: "themes",
            component: Themes,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["ui"]
            }
        },
        {
            path: "/icons",
            name: "icons",
            component: Icons,
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "md-icons",
                    name: "md-icons",
                    component: MdIcons,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Material Design Icons",
                        tags: ["material design"]
                    }
                },
                {
                    path: "flag-icons",
                    name: "flag-icons",
                    component: FlagIcons,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Flag Icons",
                        tags: ["list", "ui"]
                    }
                }
            ]
        },
        {
            path: "/multi-language",
            name: "multi-language",
            component: MultiLanguage,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["ui", "translate"]
            }
        },
        {
            path: "/helper-classes",
            name: "helper-classes",
            component: HelperClasses,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "Helper Classes",
                tags: ["ui"]
            }
        },
        {
            path: "/typography",
            name: "typography",
            component: Typography,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "Typography",
                tags: ["ui"]
            }
        },
        {
            path: "/customer",
            name: "customer",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "customer-list",
                    name: "customer-list",
                    component: MerchantList,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Customer List",
                        tags: ["customer-list"]
                    }
                },
                {
                    path: "add-customer",
                    name: "add-customer",
                    component: AddMerchant,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Customer",
                        tags: ["add-customer"]
                    }
                },
                {
                    path: "add-customer-agreement",
                    name: "add-customer",
                    component: AddCustomerAgreement,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Customer Agreement",
                        tags: ["add-customer-agreement"]
                    }
                },
                {
                    path: "edit-customer/:id",
                    name: "edit-customer",
                    component: AddMerchant,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Edit Customer",
                        tags: ["edit-customer"]
                    }
                }
            ]
        },
        {
            path: "/agreement",
            name: "agreement",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "agreement-list",
                    name: "agreement-list",
                    component: AgreementList,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Agreement List",
                        tags: ["agreement","agreement-list"]
                    }
                },
                {
                    path: "add-agreement",
                    name: "add-agreement",
                    component: AddAgreement,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Agreement",
                        tags: ["agreement, add-agreement"]
                    }
                },
                {
                    path: "create-agreement-template",
                    name: "create-agreement-template",
                    component: CreateAgreementTemplate,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Create Agreement Template",
                        tags: ["agreement, create-agreement"]
                    }
                }
            ]
        },
        {
            path: "/video",
            name: "video",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "video-list",
                    name: "video-list",
                    component: VideoList,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Video List",
                        tags: ["vides","video-list"]
                    }
                },
              
              
            ]
        },
        {
            path: "/carrier",
            name: "carrier",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "carrier-list",
                    name: "carrier-list",
                    component: CarrierList,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Carrier List",
                        tags: ["Carrier","carrier-list"]
                    }
                },
              
              
            ]
        },
        {
            path: "/ubsend",
            name: "ubsend",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "list",
                    name: "udsend-list",
                    component: UbsendList,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Ubsend List",
                        tags: ["ubsend"]
                    }
                },
                {
                    path: "add-ubsend",
                    name: "add-ubsend",
                    component: AddUbsend,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Ubsend",
                        tags: ["ubsend, add-ubsend"]
                    }
                },
                {
                    path: "view-ubsend/:id",
                    name: "view-ubsend",
                    component: ViewUbsend,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "View Ubsend",
                        tags: ["ubsend,view-ubsend"]
                    }
                },
                {
                    path: "edit-ubsend/:id",
                    name: "edit-ubsend",
                    component: AddUbsend,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Ubsend",
                        tags: ["ubsend, edit-ubsend"]
                    }
                },
                {
                    path: "upload-file",
                    name: "upload-file",
                    component: UploadUbsendFile,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Upload Ubsend File",
                        tags: ["ubsend, upload-ubsend-file"]
                    }
                }
            ]
        }, 
        {
            path: "/price-group",
            name: "price-group",
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "list",
                    name: "price-group-list",
                    component: PriceGroup,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "PriceGroup List",
                        tags: ["price-group"]
                    }
                },
                {
                    path: "add",
                    name: "add-price-group",
                    component: AddPriceGroup,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add PriceGroup",
                        tags: ["price-group, add-price-group"]
                    }
                },
                {
                    path: "edit/:id",
                    name: "edit-price-group",
                    component: AddPriceGroup,
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Price-group",
                        tags: ["price-group, edit-price-group"]
                    }
                }
            ]
        },
        element,
        tables,
        maps,
        editors,
        charts,
        {
            path: "/profile",
            name: "profile",
            component: Profile,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["pages"]
            }
        },
        {
            path: "/invoice",
            name: "invoice",
            component: Invoice,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["pages"]
            }
        },
        {
            path: "/login",
            name: "login",
            component:() => import ("../views/pages/authentication/Login.vue"),
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/new-password",
            name: "newPassword",
            component: NewPassword,
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/add-opt",
            name: "add-opt",
            component: AddOpt,
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/forgot-password",
            name: "forgot-password",
            component: ForgotPassword,
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/logout",
            redirect: to => {
                auth.logout()
                return "/login"
            }
        },
        {
            path: "/:pathMatch(.*)*",
            name: "not-found",
            component: NotFound,
            meta: {
                layout: layouts.contenOnly
            }
        }
    ]
})

const l = {
    contenOnly() {
        useMainStore().setLayout(layouts.contenOnly)
    },
    navLeft() {
        useMainStore().setLayout(layouts.navLeft)
    },
    navRight() {
        useMainStore().setLayout(layouts.navRight)
    },
    navTop() {
        useMainStore().setLayout(layouts.navTop)
    },
    navBottom() {
        useMainStore().setLayout(layouts.navBottom)
    },
    set(layout: Partial<StateLayout>) {
        useMainStore().setLayout(layout)
    }
}

//insert here login logic
const auth = {
    loggedIn() {
        return useMainStore().isLogged
    },
    logout() {
        useMainStore().setLogout()
    }
}

router.beforeEach((to, from,next) => {
    let authrequired = false
    if (to && to.meta && to.meta.auth) authrequired = true

    //console.log('authrequired', authrequired, to.name)
    const authLocal: string | null = localStorage.getItem('token');
    const authSession: string | null = sessionStorage.getItem('token');
  
    const isAuthorized = Boolean(authLocal || authSession);

    if (to && to.meta && to.meta.layout) {
        l.set(to.meta.layout)
    }
    if (isAuthorized && (to.path === '/login' || to.path === '/forgot-password'|| to.path === '/add-opt'|| to.path ==='/new-password')) {
        window.location.href = "/"
    }
    if (!isAuthorized && (to.path === '/login' || to.path === '/forgot-password' || to.path === '/add-opt'|| to.path ==='/new-password')) {
        return next();
    }

    if (!isAuthorized) {
      return next('/login');
    }

  
    return next();
    
})

router.afterEach((to, from) => {
    setTimeout(() => {
        useMainStore().setSplashScreen(false)
    }, 500)
})

export default router
