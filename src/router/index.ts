import {
    create<PERSON><PERSON><PERSON>,
    NavigationGuardNext,
    NavigationGuardWithThis,
    RouteLocationNormalized,
    Router,
    RouteRecordRaw,
    createWebHistory
} from "vue-router"

//apps
import Dashboard from "../views/apps/Dashboard.vue"
import Calendar from "../views/apps/Calendar.vue"
import Contacts from "../views/apps/Contacts.vue"
import Gallery from "../views/apps/Gallery.vue"
import Cards from "../views/apps/Cards.vue"
import Mail from "../views/apps/Mail.vue"
import Ecommerce from "./ecommerce"

// import Login from "../views/pages/authentication/Login.vue"
import AddOpt from "../views/pages/authentication/Opt.vue"
import NewPassword from "../views/pages/authentication/NewPassword.vue"
import ForgotPassword from "../views/pages/authentication/ForgotPassword.vue"
import Profile from "../views/pages/Profile.vue"
import NotFound from "../views/pages/NotFound.vue"
import Invoice from "../views/pages/Invoice.vue"

// Merchant
import MerchantList from "../views/pages/merchant/MerchantList.vue"
import DetailPage from "../views/pages/merchant/Detail.vue"
import AddMerchant from "../views/pages/merchant/AddMerchant.vue"

// Agreement
import AgreementList from "../views/pages/agreement/AgreementList.vue"
import AddAgreement from "../views/pages/agreement/AddAgreement.vue"
import AddCustomerAgreement from "../views/pages/agreement/AddCustomerAgreement.vue"
import UpdateAgreement from "../views/pages/agreement/UpdateAgreement.vue"
import CreateAgreementTemplate from "../views/pages/agreement/CreateAgreementTemplate.vue"

// Ubsend
import UbsendList from "../views/pages/ubsend/UbsendList.vue"
import AddUbsend from "../views/pages/ubsend/AddUbsend.vue"
import ViewUbsend from "../views/pages/ubsend/UbsendView.vue"
import UploadUbsendFile from "../views/pages/ubsend/UploadUbsendFile.vue"

// PriceGroup
import PriceGroup from "../views/pages/priceGroup/PriceGroupList.vue"
import AddPriceGroup from "../views/pages/priceGroup/AddPriceGroup.vue"

// Video
import VideoList from "../views/pages/video/VideoList.vue"

// Carrier
import CarrierList from "../views/pages/carrier/CarrierList.vue"
import CarrierProductList from "../views/pages/carrier/CarrierProductList.vue"

// Weight Classes   (Majid)
import WeightClassesList from "../views/pages/weightClasses/WeightClassesList.vue"

import DashboardList from "../views/pages/dashboardList/DashboardList.vue"
import CreditRecords from "../views/pages/CreditRecords.vue"


//ui
import layout from "./layout"
import Themes from "../views/ui/Themes.vue"
import Icons from "../views/ui/Icons/Icons.vue"
import MdIcons from "../views/ui/Icons/MdIcons.vue"
import FlagIcons from "../views/ui/Icons/FlagIcons.vue"
import MultiLanguage from "../views/ui/MultiLanguage.vue"
import HelperClasses from "../views/ui/HelperClasses.vue"
import Typography from "../views/ui/Typography.vue"
import element from "./element"
import tables from "./tables"
import maps from "./maps"
import editors from "./editors"
import charts from "./charts"

import layouts from "../layout"
import {useMainStore} from "../stores/main"
import type {StateLayout} from "../types"
import {authService} from "../services/_singletons";


const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: "/",
            alias: "/dashboard",
            name: "dashboard",
            component: DashboardList,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app"],
                permissions: ['dashboard-view']
            }
        },
        {
            path: "/credit-records",
            alias: "/credit-records",
            name: "credit-records",
            component: CreditRecords,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app"],
                //permissions: ['dashboard-view']
            }
        },
        {
            path: "/calendar",
            name: "calendar",
            component: () => import("../views/apps/Calendar.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app"]
            }
        },
        {
            path: "/detail/:user_code/:company_code",
            name: "detail",
            props: true,
            component: DetailPage,
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "detail",
                tags: ["app", "detail"]
            }
        },
        {
            path: "/contacts",
            name: "contacts",
            component: () => import("../views/apps/Contacts.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["users", "address", "book", "app"]
            }
        },
        {
            path: "/gallery",
            name: "gallery",
            component: () => import("../views/apps/Gallery.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["photo", "app"]
            }
        },
        {
            path: "/cards",
            name: "cards",
            component: () => import("../views/apps/Cards.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["app", "todo"]
            }
        },
        {
            path: "/mail",
            name: "mail",
            component: () => import("../views/apps/Mail.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "Mail",
                tags: ["app", "email", "inbox"]
            }
        },

        layout,
        {
            path: "/themes",
            name: "themes",
            component: () => import("../views/ui/Themes.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["ui"]
            }
        },
        {
            path: "/icons",
            name: "icons",
            component: () => import("../views/ui/Icons/Icons.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "md-icons",
                    name: "md-icons",
                    component: () => import("../views/ui/Icons/MdIcons.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Material Design Icons",
                        tags: ["material design"]
                    }
                },
                {
                    path: "flag-icons",
                    name: "flag-icons",
                    component: () => import("../views/ui/Icons/FlagIcons.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Flag Icons",
                        tags: ["list", "ui"]
                    }
                }
            ]
        },
        {
            path: "/multi-language",
            name: "multi-language",
            component: () => import("../views/ui/MultiLanguage.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["ui", "translate"]
            }
        },
        {
            path: "/helper-classes",
            name: "helper-classes",
            component: () => import("../views/ui/HelperClasses.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "Helper Classes",
                tags: ["ui"]
            }
        },
        {
            path: "/typography",
            name: "typography",
            component: () => import("../views/ui/Typography.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                title: "Typography",
                tags: ["ui"]
            }
        },
        {
            path: "/customer",
            name: "customer",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "customer-list",
                    name: "customer-list",
                    component: () => import("../views/pages/merchant/MerchantList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Customer List",
                        tags: ["customer-list"]
                    }
                },
                {
                    path: "add-customer",
                    name: "AddMerchantPage",
                    component: () => import("../views/pages/merchant/AddMerchant.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Customer",
                        tags: ["add-customer"]
                    }
                },
                {
                    path: "add-customer-agreement",
                    name: "add-customer",
                    component: () => import("../views/pages/agreement/AddCustomerAgreement.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Customer Agreement",
                        tags: ["add-customer-agreement"]
                    }
                },
                /*{
                    path: "update-customer-agreement",
                    name: "update-customer-agreement",
                    component: () => import("../views/pages/agreement/AddCustomerAgreement.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Update Customer Agreement",
                        tags: ["update-customer-agreement"]
                    }
                },*/
                {
                    path: "update-customer-agreement/:agreementId",
                    name: "update-customer-agreementAgreementId",
                    component: () => import("../views/pages/agreement/AddCustomerAgreement.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Update Customer Agreement",
                        tags: ["update-customer-agreement"]
                    }
                },

                {
                    path: "customer-email/:merchantId/:customerId",
                    name: "CustomerEmail",
                    component: () => import("../views/pages/merchant/CustomerEmail.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Customer Email",
                        tags: ["customer-email"]
                    }
                },

                {
                    path: "edit-customer/:id",
                    name: "edit-customer",
                    component: () => import("../views/pages/merchant/AddMerchant.vue"),
                    // props:(route) => ({
                    //     editData : data,
                    //     ...route.params
                    // }),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Edit Customer",
                        tags: ["edit-customer"]
                    }
                }
            ]
        },
        {
            path: "/agreement",
            name: "agreement",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "agreement-list",
                    name: "agreement-list",
                    component: () => import("../views/pages/agreement/AgreementList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Agreement List",
                        tags: ["agreement", "agreement-list"]
                    }
                },
                {
                    path: "add-agreement",
                    name: "add-agreement",
                    component: () => import("../views/pages/agreement/AddAgreement.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Agreement",
                        tags: ["agreement, add-agreement"]
                    }
                },
                {
                    path: "update-agreement",
                    name: "update-agreement",
                    component: () => import("../views/pages/agreement/UpdateAgreement.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Update Agreement",
                        tags: ["agreement, update-agreement"]
                    }
                },

            ]
        },
        {
            path: "/video",
            name: "video",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "video-list",
                    name: "video-list",
                    component: () => import("../views/pages/video/VideoList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Video List",
                        tags: ["vides", "video-list"]
                    }
                },


            ]
        },
        {
            path: "/carrier",
            name: "carrier",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "carrier-list",
                    name: "carrier-list",
                    component: () => import("../views/pages/carrier/CarrierList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Carrier List",
                        tags: ["Carrier", "carrier-list"]
                    }
                },
                {
                    path: "carrier-products",
                    name: "carrier-products",
                    component: () => import("../views/pages/carrier/CarrierProductList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Carrier Product",
                        tags: ["Carrier", "carrier-products"]
                    }
                },
            ]
        },
        {
            path: "/settings",
            name: "settings",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "carrier-validations-settings",
                    name: "carrier-validations-settings",
                    component: () => import("../views/pages/settings/CarrierValidationsPage.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Carrier Validations Settings",
                        tags: ["Settings", "Carrier Validations Settings"]
                    }
                },
                {
                    path: "dimension-settings",
                    name: "dimension-settings",
                    component: () => import("../views/pages/settings/DimensionSettingsPage.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Dimension Settings",
                        tags: ["Settings", "Dimension Settings"]
                    }
                },
                {
                    path: 'address-settings',
                    name: 'address-settings',
                    component: () => import("../views/pages/settings/AddressSettingsPage.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Address Settings",
                        tags: ["Settings", "Dimension Settings"]
                    }
                },
            ]
        },
        {
            path: "/reports",
            name: "reports",
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "ub-comparison",
                    name: "ub-comparison",
                    component: () => import("../views/pages/reports/UbComparisonReportPage.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Ub-Comparison Reports",
                        tags: ["Reports", "Ub-Comparison Reports"]
                    }
                },
                {
                    path: 'revenue',
                    name: 'revenue',
                    component: () => import("../views/pages/reports/RevenueReportPage.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Revenue Reports",
                        tags: ["reports", "Revenue Reports"]
                    }
                },
                {
                    path: 'balance',
                    name: 'balance',
                    component: () => import("../views/pages/reports/BalanceReportPage.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Balance Reports",
                        tags: ["reports", "Balance Reports"]
                    }
                },
            ]
        },
        {
            path: "/weight-classes",
            name: "weight-classes",
            component: () => import("../views/pages/weightClasses/WeightClassesList.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["weight-classes"]
            }
        },
        {
            path: "/dashboard/list",
            name: "dashboard-list",
            component: () => import("../views/pages/dashboardList/DashboardList.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["dashboard-list"]
            }
        },
        {
            path: "/template",
            name: "template",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "create-template",
                    name: "create-template",
                    component: () => import("../views/pages/template/index.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Create Agreement Template",
                        tags: ["agreement, create-agreement"],
                    }
                },
                {
                    path: "create-email-template",
                    name: "email-template",
                    component: () => import("../views/pages/template/emailTemplate/index.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Create Email Template",
                        tags: ["template, create-email-template"]
                    }
                },
                {
                    path: "create-add-funds",
                    name: "add-funds-template",
                    component: () => import("../views/pages/template/AddFunds.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Funds Template",
                        tags: ["template, create-add-funds"]
                    }
                },
                {
                    path: "create-order-template",
                    name: "create-order-template",
                    component: () => import("../views/pages/template/CreateOrder.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Create Order Template",
                        tags: ["template, create-order-template"]
                    }
                },
                {
                    path: "create-footer-template",
                    name: "footer-template",
                    component: () => import("../views/pages/template/Footer.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Create Footer Template",
                        tags: ["template, create-footer-template"]
                    }
                },
                {
                    path: "create-shipping-email-template",
                    name: "shipping-email-template",
                    component: () => import("../views/pages/template/CreateShipping.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Create Shipping Email Template",
                        tags: ["template, create-shipping-email-template"]
                    }
                },
            ]
        },

        {
            path: "/onboarding",
            name: "onboarding",
            // beforeEnter: [AuthGuard],
            meta: {
                layout: layouts.contenOnly
            },
            children: [
                {
                    path: "signup-process/:merchantId",
                    name: "onboarding",
                    component: () => import("../views/pages/onboardingProcess/index.vue"),
                    meta: {
                        layout: layouts.contenOnly
                    }
                }
            ]
        },
        {
            path: "/ubsend",
            name: "ubsend",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "list",
                    name: "udsend-list",
                    component: () => import("../views/pages/ubsend/UbsendList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Ubsend List",
                        tags: ["ubsend"]
                    }
                },
                {
                    path: "add-ubsend",
                    name: "add-ubsend",
                    component: () => import("../views/pages/ubsend/AddUbsend.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Ubsend",
                        tags: ["ubsend, add-ubsend"]
                    }
                },
                {
                    path: "edit-ubsend/:id",
                    name: "edit-ubsend",
                    component: () => import("../views/pages/ubsend/AddUbsend.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Ubsend",
                        tags: ["ubsend, edit-ubsend"]
                    }
                },
                {
                    path: "upload-file",
                    name: "upload-file",
                    component: () => import("../views/pages/ubsend/UploadUbsendFile.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Upload Ubsend File",
                        tags: ["ubsend, upload-ubsend-file"]
                    }
                }
            ]
        },
        {
            path: "/error",
            name: "error",
            // beforeEnter: [AuthGuard],
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "logs",
                    name: "error-logs-list",
                    component: () => import("../views/pages/errorLogs/ErrorsList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Error Logs List",
                    }
                },
                {
                    path: "logs/:id/details",
                    name: "error-log-details",
                    component: () => import("../views/pages/errorLogs/ErrorDetail.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Error Log Detail",
                    }
                },
            ]
        },
        {
            path: "/price-group",
            name: "price-group",
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "list",
                    name: "price-group-list",
                    component: () => import("../views/pages/priceGroup/PriceGroupList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "PriceGroup List",
                        tags: ["price-group"],
                        permissions: ['pricegroup-list']
                    }
                },
                {
                    path: "add",
                    name: "add-price-group",
                    component: () => import("../views/pages/priceGroup/AddPriceGroup.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add PriceGroup",
                        tags: ["price-group, add-price-group"]
                    }
                },
                {
                    path: "edit/:id",
                    name: "edit-price-group",
                    component: () => import("../views/pages/priceGroup/AddPriceGroup.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Add Price-group",
                        tags: ["price-group, edit-price-group"]
                    }
                }
            ]
        },
        {
            path: "/shipping-countries",
            name: "shipping-countries",
            component: () => import("../views/pages/ShippingCountriesPage.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["shipping-countries"]
            }
        },
        {
            path: "/permission",
            name: "permission",
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "permission-list",
                    name: "permission-list",
                    component: () => import("../views/pages/permission/PermissionList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Permission List",
                        tags: ["permission"]
                    }
                },
            ]
        },
        {
            path: "/system-user",
            name: "systemUser",
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "list",
                    name: "systemUserList",
                    component: () => import("../views/pages/systemUser/SystemUserList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "systemUser List",
                        tags: ["systemUser"]
                    }
                },
            ]
        },
        {
            path: "/role",
            name: "role",
            meta: {
                auth: true,
                layout: layouts.navLeft
            },
            children: [
                {
                    path: "role-list",
                    name: "role-list",
                    component: () => import("../views/pages/role/RoleList.vue"),
                    meta: {
                        auth: true,
                        layout: layouts.navLeft,
                        searchable: true,
                        title: "Role List",
                        tags: ["role"]
                    }
                },
            ]
        },
        element,
        tables,
        // maps,
        editors,
        // charts,
        {
            path: "/profile",
            name: "profile",
            component: () => import("../views/pages/Profile.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["pages"]
            }
        },
        {
            path: "/invoice",
            name: "invoice",
            component: () => import("../views/pages/Invoice.vue"),
            meta: {
                auth: true,
                layout: layouts.navLeft,
                searchable: true,
                tags: ["pages"]
            }
        },
        {
            path: "/login",
            name: "login",
            component: () => import("../views/pages/authentication/Login.vue"),
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/new-password",
            name: "newPassword",
            component: () => import("../views/pages/authentication/NewPassword.vue"),
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/add-opt",
            name: "add-opt",
            component: () => import("../views/pages/authentication/Opt.vue"),
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/forgot-password",
            name: "forgot-password",
            component: () => import("../views/pages/authentication/ForgotPassword.vue"),
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/logout",
            redirect: to => {
                auth.logout()
                return "/login"
            }
        },
        {
            path: "/:pathMatch(.*)*",
            name: "not-found",
            component: () => import("../views/pages/NotFound.vue"),
            meta: {
                layout: layouts.contenOnly
            }
        },
        {
            path: "/403",
            name: '403',
            component: () => import("../views/pages/403.vue"),
        },
    ]
})

const l = {
    contenOnly() {
        useMainStore().setLayout(layouts.contenOnly)
    },
    navLeft() {
        useMainStore().setLayout(layouts.navLeft)
    },
    navRight() {
        useMainStore().setLayout(layouts.navRight)
    },
    navTop() {
        useMainStore().setLayout(layouts.navTop)
    },
    navBottom() {
        useMainStore().setLayout(layouts.navBottom)
    },
    set(layout: Partial<StateLayout>) {
        useMainStore().setLayout(layout)
    }
}

//insert here login logic
const auth = {
    loggedIn() {
        return useMainStore().isLogged
    },
    logout() {
        useMainStore().setLogout()
    }
}

router.beforeEach((to, from, next) => {
    let authRequired = false
    if (to && to.meta && to.meta.auth) authRequired = true

    //console.log('authrequired', authrequired, to.name)
    const authLocal: string | null = localStorage.getItem('token');
    const authSession: string | null = sessionStorage.getItem('token');

    const isAuthorized = Boolean(authLocal || authSession);

    if (to && to.meta && to.meta.layout) {
        l.set(to.meta.layout)
    }
    if (isAuthorized && (to.path === '/login' || to.path === '/forgot-password' || to.path === '/add-opt' || to.path === '/new-password')) {
        window.location.href = "/"
    }
    if (to.meta && to.meta.permissions && !authService.isAssigned(to.meta.permissions as string[])) {
        return next('/403')
    }
    if (!isAuthorized && (to.path === '/login' || to.path === '/forgot-password' || to.path === '/add-opt' || to.path === '/new-password')) {
        return next();
    }
    if (!isAuthorized && to.path.indexOf('/onboarding/signup-process') === 0) {
        return next();
    }

    if (!isAuthorized) {
        return next('/login');
    }
    return next();

})

router.afterEach((to, from) => {
    setTimeout(() => {
        useMainStore().setSplashScreen(false)
    }, 500)
})
export default router
