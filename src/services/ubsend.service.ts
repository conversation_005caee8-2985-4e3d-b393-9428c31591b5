import { IUploadUbsendRequest, IFileUploadResponse, IUbsendPricingRequest } from './../model/ubsend.model';
import { IUbsend,IAddUbsendRequest,IDeleteUbsendRequest,IUbsendResponse,IUbsendDelteResponse} from '../model/ubsend.model'; 
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';
import { AxiosError } from 'axios';

export default class UbsendService {
  constructor(private readonly httpService: HttpService) {}

  protected readonly routes = {
    GETLIST: 'admin/ubsend/list',
    EDIT: 'admin/ubsend/list',
    CREATEORUPDATE: 'admin/ubsend/createOrUpdate',
    DELETE: 'admin/ubsend/delete',
    UPLOADFILE: 'admin/ubsend/uploadFile',
    PRICING: 'admin/ubsend/pricing',
  };

  public async getUbsentList(params: IRequestParam[] = []): Promise<IUbsend[]> {
    return this.httpService.get<IUbsend[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }
  public async getPricingList(payload: IUbsendPricingRequest): Promise<IFileUploadResponse> {
    const response: IFileUploadResponse = await this.httpService.post<IFileUploadResponse, IUbsendPricingRequest>(
      this.routes.PRICING,
      payload,
    );
    return response;
  }
  
  public async getUbsentById(params: IRequestParam[] = []): Promise<IUbsend[]> {
    return this.httpService.get<IUbsend[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }

  public async uploadFile(payload: IUploadUbsendRequest): Promise<IFileUploadResponse> {
    const response: IFileUploadResponse = await this.httpService.post<IFileUploadResponse, IUploadUbsendRequest>(
      this.routes.UPLOADFILE,
      payload,
    );
    return response;
  }

  public async addUbsend(payload: IAddUbsendRequest): Promise<IUbsendResponse> {
    const response: IAddUbsendRequest = await this.httpService.post<IAddUbsendRequest, IAddUbsendRequest>(
      this.routes.CREATEORUPDATE,
      payload,
    );
    return response;
  }

  public async deleteUbsend(payload: IDeleteUbsendRequest): Promise<IUbsendDelteResponse> {
    const response: IDeleteUbsendRequest = await this.httpService.post<IDeleteUbsendRequest, IDeleteUbsendRequest>(
      this.routes.DELETE,
      payload,
    );
    return response;
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
      //await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }
}
