import {ITemplateRequest, ITemplateResponse} from '../model/template.model';
import HttpService from './http.service';
import {AxiosError} from 'axios';

export default class TemplateService {
  constructor(private readonly httpService: HttpService) {}

  protected readonly routes = {
    GET: 'admin/template/get',
    SAVE: 'admin/template/save',
  };

  public async getTemplateList(payload: ITemplateRequest): Promise<ITemplateResponse> {
    const response: ITemplateResponse = await this.httpService.post<ITemplateResponse, ITemplateRequest>(
      this.routes.GET,
      payload,
    );
    return response;
  }

  public async getTemplateSummary(payload: ITemplateRequest): Promise<ITemplateResponse> {
    const response: ITemplateResponse = await this.httpService.post<ITemplateResponse, ITemplateRequest>(
      this.routes.GET,
      payload,
    );
    return response;
  }

  public async saveTemplate(payload: ITemplateRequest): Promise<ITemplateResponse> {
    console.log(payload)

    return await this.httpService.post<ITemplateResponse, ITemplateRequest>(
        this.routes.SAVE,
        payload,
    );
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
   //   await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }
}
