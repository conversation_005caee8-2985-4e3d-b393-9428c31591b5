import HttpService from "./http.service";

interface ISettingsCarrierValidations {
    ['key']?: string
}

interface ISettingsCarrierValidationsResponse {
    [key: string]: any
}

class CarrierValidationService {
    constructor(private readonly httpService: HttpService) {
    }

    private readonly ROUTES = {
        'GET_CARRIER_VALIDATIONS_LIST': 'admin/settings/carrier-validations/list',
        'CREATE_CARRIER_VALIDATION_LIST': 'admin/settings/carrier-validations',
        'UPDATE_CARRIER_VALIDATION_LIST': 'admin/settings/carrier-validations/:id',
        'DELETE_CARRIER_VALIDATION_LIST': 'admin/settings/carrier-validations/:id',
    }

    public async getCarrierValidationsList(): Promise<[]> {
        return await this.httpService.get<[]>(`${this.ROUTES.GET_CARRIER_VALIDATIONS_LIST}`, []);
    }


    public async createCarrierValidations(payload: ISettingsCarrierValidations): Promise<ISettingsCarrierValidationsResponse> {
        return await this.httpService.post<ISettingsCarrierValidationsResponse, ISettingsCarrierValidations>(
            this.ROUTES.CREATE_CARRIER_VALIDATION_LIST,
            payload
        );
    }

    public async updateCarrierValidations(payload: ISettingsCarrierValidations, id: number | string): Promise<ISettingsCarrierValidationsResponse> {
        const url = this.ROUTES.UPDATE_CARRIER_VALIDATION_LIST.replace(':id', id.toString());
        return await this.httpService.put<ISettingsCarrierValidationsResponse, ISettingsCarrierValidations>(
            url,
            payload
        );
    }

    public async deleteCarrierValidation(id: string | number): Promise<ISettingsCarrierValidationsResponse> {
        const url = this.ROUTES.DELETE_CARRIER_VALIDATION_LIST.replace(':id', id.toString())
        return await this.httpService.delete<ISettingsCarrierValidationsResponse>(
            url
        );
    }

}

export default CarrierValidationService;