import HttpService from "./http.service";

interface ISettingsAddresses {
    ['key']?: string
}

interface ISettingsAddressesResponse {
    ['key']?: string
}

class DimensionService {
    constructor(private readonly httpService: HttpService) {
    }

    private readonly ROUTES = {
        'GET_ADDRESSES_LIST': 'admin/settings/addresses/list',
        'UPDATE_DIMENSION_LIST': 'admin/settings/addresses/update',
        'DELETE_DIMENSION_LIST': 'admin/settings/addresses/delete/:id',
    }

    public async getAddressesList(): Promise<[]> {
        return await this.httpService.get<[]>(`${this.ROUTES.GET_ADDRESSES_LIST}`, []);
    }


    public async updateAddresses(payload: ISettingsAddresses): Promise<ISettingsAddressesResponse> {
        return await this.httpService.post<ISettingsAddressesResponse, ISettingsAddresses>(
            this.ROUTES.UPDATE_DIMENSION_LIST,
            payload
        );
    }

    public async deleteAddress(id: string | number): Promise<ISettingsAddressesResponse> {
        const url = this.ROUTES.DELETE_DIMENSION_LIST.replace(':id', id.toString())
        return await this.httpService.delete<ISettingsAddressesResponse>(
            url
        );
    }

}

export default DimensionService;