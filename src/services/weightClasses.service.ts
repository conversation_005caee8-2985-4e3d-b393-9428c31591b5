import {
    IWeightClass,
    IWeightClassRequest,
    IWeightClassCreateResponse,
    IDeleteWeightClassRequest,
    IWeightClassDelteResponse,
  
  } from '../model/weightClasses.model';

  import { IRequestParam } from '../model/http.model';
  import HttpService from './http.service';
  import { AxiosError } from 'axios';


export default class WeightClassesService {
    constructor(private readonly httpService: HttpService) {}

    protected readonly routes = {
        GETLIST: 'admin/weight_class/list',
        CREATE_WEIGHT_CLASS:'admin/weight_class/create',
        DELETE_WEIGHT_CLASS:'admin/weight_class/delete',
        UPDATE_WEIGHT_CLASS:'admin/weight_class/update',
    };

    public async getWeightClassesList(params: IRequestParam[] = []): Promise<IWeightClass[]> {
        return this.httpService.get<IWeightClass[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
    }

    public async addWeightClass(payload: IWeightClassRequest): Promise<IWeightClassCreateResponse> {
        const response: IWeightClassCreateResponse = await this.httpService.post<IWeightClassCreateResponse, IWeightClassRequest>(
            this.routes.CREATE_WEIGHT_CLASS,
            payload
    
        );
        return response;
    }

    public async deleteWeightClass(payload: IDeleteWeightClassRequest): Promise<IWeightClassDelteResponse> {
        const response: IWeightClassDelteResponse = await this.httpService.post<IWeightClassDelteResponse, IDeleteWeightClassRequest>(
            this.routes.DELETE_WEIGHT_CLASS,
            payload,
        );
        return response;
    }

    public async updateWeightClass(payload: IWeightClassRequest): Promise<IWeightClassCreateResponse> {
        const response: IWeightClassCreateResponse = await this.httpService.post<IWeightClassCreateResponse, IWeightClassRequest>(
            this.routes.UPDATE_WEIGHT_CLASS,
            payload,
        );
        return response;
    }
}