import {
    IMerchant,
    IAddMerchantRequest,
    IMerchantUpdateResponse,
    IMerchantResponse,
    IMerchantDelteResponse,
    IMerchantRequestDetail,
    IMerchantDetailResponse,
    IMerchantResetPasswordRequest,
    IMerchantResetPasswordResponse,
    IMerchantUpdatePasswordRequest,
    IMerchantUpdatePasswordResponse,
    IMerchantUpdateStatusRequest,
    UpdateCreditStatusRequest
} from "../model/merchant.model"
import { IRequestParam } from "../model/http.model"
import HttpService from "./http.service"
import { AxiosError } from "axios"

interface IPayload {
    [key: string]: any
}

export default class MerchantService {
    constructor(private readonly httpService: HttpService) {}

    protected readonly routes = {
        GETLIST: "admin/merchant/list",
        GETLISTFILTER: "admin/merchant/filter",
        GETMERCHENTDETAIL: "admin/merchant/summary",
        GETCOUNTRYLIST: "admin/country/list",
        GETPLATFORMLIST: "/admin/platform/list",
        CREATE: "admin/merchant/create",
        UPDATE: "admin/merchant/update",
        DELETE: "admin/merchant/delete",
        DETAIL: "admin/merchant/detail",
        RESET_PASSWORD: "admin/merchant/sendResetPasswordLink",
        UPDATE_PASSWORD: "/admin/onboarding/video/updatePassword",
        GET_MONTHLY_INVOICES: "/admin/monthly/invoice/:customer-id",
        GET_CREDIT_DETAILS: "/admin/merchant/credit/details/:company-code",
        GET_MONTHLY_INVOICE_SPECIFICATIONS: "/admin/invoice/monthly/:id/specifications"
    }

    public async getMerchantList(payload: IPayload = { page: 1, perPage: 100, search: "" }): Promise<IMerchant[]> {
        return this.httpService.get<IMerchant[]>(
            `${this.routes.GETLIST}?page=${payload.page}&perpage=${payload.perPage}&search=${payload.search}`,
            []
        )
    }

    public async getMerchantListFilter(payload: IAddMerchantRequest): Promise<IMerchantResponse> {
        console.log(payload, "payload")
        const response: IAddMerchantRequest = await this.httpService.post<IAddMerchantRequest, IAddMerchantRequest>(
            this.routes.GETLISTFILTER,
            payload
        )
        return response
    }

    public async getCountryList(params: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(this.routes.GETCOUNTRYLIST)
    }

    public async getPlatformList(params: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(this.routes.GETPLATFORMLIST)
    }

    public async getUbsentById(params: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(this.routes.GETLIST, [{ key: "limit", value: 100 }, ...params])
    }

    public async addMerchant(payload: IAddMerchantRequest): Promise<IMerchantResponse> {
        const response: IAddMerchantRequest = await this.httpService.post<IAddMerchantRequest, IMerchantResponse>(
            this.routes.CREATE,
            payload
        )
        return response
    }

    public async resetPassword(payload: IMerchantResetPasswordRequest): Promise<IMerchantResetPasswordResponse> {
        const response: IMerchantResetPasswordResponse = await this.httpService.post<
            IMerchantResetPasswordResponse,
            IMerchantResetPasswordRequest
        >(this.routes.RESET_PASSWORD, payload)
        return response
    }

    public async getMerchantDetail(payload: IMerchantRequestDetail): Promise<IMerchantDetailResponse> {
        const response: IMerchantDetailResponse = await this.httpService.post<
            IMerchantDetailResponse,
            IMerchantRequestDetail
        >(this.routes.DETAIL, payload)
        return response
    }

    public async updateMerchant(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >(this.routes.UPDATE, payload)
        console.log(response)
        return response
    }

    public async deleteMerchant(payload: IMerchantDelteResponse): Promise<IMerchantDelteResponse> {
        const response: IMerchantDelteResponse = await this.httpService.post<
            IMerchantDelteResponse,
            IMerchantDelteResponse
        >(this.routes.DELETE, payload)
        return response
    }

    public async onRejected(error: AxiosError | unknown): Promise<void> {
        if (error instanceof AxiosError && error?.response?.status && [401, 403].includes(error.response.status)) {
            // await this.logout();
            return window.location.replace("/#/login")
        }

        return Promise.reject(error)
    }

    /* tabs */
    public async getMerchantAgreement(agreementNumber: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(`admin/merchant/agreement/${agreementNumber}`, [])
    }

    public async getMerchantPriceGroup(agreementNumber: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(`admin/merchant/pricegroup/${agreementNumber}`, [])
    }

    public async getMerchantContractTab(agreementNumber: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(`admin/merchant/contacts/${agreementNumber}`, [])
    }

    /*public async getMerchantTransactions(customerId: IRequestParam[] = [],types: String[] = []): Promise<IMerchant[]> {
      alert(customerId)
      alert(types)
      const token = HttpService.getToken();
      return this.httpService.get<IMerchant[]>(`admin/merchant//${customerId}`, []);
    }*/
    public async getMerchantSummaryDetails(customerId: IRequestParam[] = []): Promise<IMerchant[]> {
        const token = HttpService.getToken()
        return this.httpService.get<IMerchant[]>(`admin/merchant/summary/${customerId}`, [])
    }

    public async postMerchantCustomerSpent(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >("admin/merchant/sales/customer_spent", payload)
        return response
    }

    public async postMerchantShipvagooEarning(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >("admin/merchant/sales/shipvagoo_earnings", payload)
        return response
    }

    public async getSalesStatistics(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >("admin/merchant/sales/sales_statistics", payload)
        return response
    }

    public async getCarrierStatistics(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >("admin/merchant/sales/carrier_statistics", payload)
        return response
    }

    public async getCustomerEmail(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >("admin/Customer/emailTemplate/getEmailTemplate", payload)
        return response
    }

    public async postCustomerEmail(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<
            IMerchantUpdateResponse,
            IAddMerchantRequest
        >("admin/summaryMail/sendMail", payload)
        return response
    }

    public async postUpdatePassword(payload: IMerchantUpdatePasswordRequest): Promise<IMerchantUpdatePasswordResponse> {
        const response: IMerchantUpdatePasswordResponse = await this.httpService.post<
            IMerchantUpdatePasswordResponse,
            IMerchantUpdatePasswordRequest
        >(this.routes.UPDATE_PASSWORD, payload)
        return response
    }

    public async getMerchantTransactions(payload): Promise<IMerchant[]> {
        return await this.httpService.get<IMerchant[]>(
            `admin/merchant/payments/${payload.types}/${payload.customerId}?page=${payload.page}&perpage=${payload.perPage}&filter=${payload.filter}`,
            []
        )
    }
    public async getMerchantBillingInvoices(payload): Promise<IMerchant[]> {
        const { customerId, page, perPage, filter } = payload || {}
        const query = `?customerId=${customerId}&page=${page}&perpage=${perPage}&filter=${encodeURIComponent(filter ?? '')}`
        return await this.httpService.get<IMerchant[]>(`admin/economic-invoices${query}`, [])
    }
    public async getEconomicMerchants(payload): Promise<IMerchant[]> {
        return await this.httpService.get<IMerchant[]>(`admin/economic-invoices/customers/list`, [])
    }

    public async getMonthlyInvoices(payload): Promise<IMerchant[]> {
        const url = this.routes.GET_MONTHLY_INVOICES.replace(":customer-id", payload.customerId.toString())
        return await this.httpService.get<IMerchant[]>(
            `${url}?page=${payload.page}&perpage=${payload.perPage}&filter=${payload.filter}`,
            []
        )
    }
    public async getMonthlyInvoiceSpecifications(payload): Promise<[]> {
        const url = this.routes.GET_MONTHLY_INVOICE_SPECIFICATIONS.replace(":id", payload.invoiceId.toString())
        return await this.httpService.get<[]>(`${url}?page=${payload.page}&per_page=${payload.perPage}`, [])
    }

    public async updateCustomerStatus(payload: IMerchantUpdateStatusRequest): Promise<IMerchant[]> {
        return await this.httpService.patch<IMerchant[], IPayload>(`admin/merchant/${payload.user_code}/statusupdate`, {
            is_active: payload.is_active
        })
    }

    public async getMerchantCreditDetails(company_code: string | number): Promise<IMerchant[]> {
        const url = this.routes.GET_CREDIT_DETAILS.replace(":company-code", company_code.toString())
        return await this.httpService.get<IMerchant[]>(`${url}`, [])
    }

    public async updateCreditStatus(payload: UpdateCreditStatusRequest): Promise<IMerchant[]> {
        console.log(payload)
        return await this.httpService.patch<IMerchant[], IPayload>(
            `admin/merchant/update/${payload.company_code}/credit/status`,
            { credit_enabled: payload.credit_enabled }
        )
    }

    public async updateCreditDetails(payload: UpdateCreditStatusRequest): Promise<IMerchant[]> {
        return await this.httpService.patch<IMerchant[], IPayload>(
            `admin/merchant/update/${payload.company_code}/credit/details`,
            payload
        )
    }

    public async createSettlement(payload: any): Promise<any> {
        return await this.httpService.post<any, any>(`admin/economic-invoice/create-settlement`, payload)
    }
}
