import HttpService from "./http.service";

interface IPayload {
    [key: string]: any,
}

class LogService {
    constructor(private readonly httpService: HttpService) {
    }

    private readonly ROUTES = {
        'GET_ERROR_LOGS_LIST': 'admin/errorLogs/list',
    }

    public async getErrorLogsList(payload: IPayload = {page: 1, perPage: 10, search: ''}): Promise<[]> {
        return await this.httpService.get<[]>(`${this.ROUTES.GET_ERROR_LOGS_LIST}?page=${payload.page}&perpage=${payload.perPage}&search=${payload.search}`, []);
    }

    public async getCustomerErrorLogsList(payload: IPayload = {page: 1, perPage: 10, search: '', company_code: ''}): Promise<[]> {
        return await this.httpService.get<[]>(`${this.ROUTES.GET_ERROR_LOGS_LIST}?company_code=${payload.company_code}&page=${payload.page}&perpage=${payload.perPage}&search=${payload.search}`, []);
    }
}

export default LogService;