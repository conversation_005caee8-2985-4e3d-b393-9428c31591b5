import {
  IAddSystemUserRequest,
  IDeleteSystemUserRequest,
  ISystemUserResponse,
  ISystemUserDeleteResponse,
   IDeleteSystemUserProductRequest,
} from '../model/system_user.model';
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';
import { AxiosError } from 'axios';

export default class SystemUserService {

  constructor(private readonly httpService: HttpService) {}
 
  protected readonly routes = {
    GETLIST: 'admin/system_user/list',
    CREATE: 'admin/system_user/create',
    UPDATE: 'admin/system_user/update',
    DELETE: 'admin/system_user/delete',
    EDIT: 'admin/system_user/'
  };

  public async getSystemUserList(params: IRequestParam[] = []): Promise<ISystemUserResponse[]> {
    return this.httpService.get<ISystemUserResponse[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }

  public async addSystemUser(payload: IAddSystemUserRequest): Promise<ISystemUserResponse> {
    const response: IAddSystemUserRequest = await this.httpService.post<ISystemUserResponse, IAddSystemUserRequest>(
      this.routes.CREATE,
      payload

    );
    return response;
  }


  public async updateSystemUser(payload: IAddSystemUserRequest): Promise<ISystemUserResponse> {
    const response: IAddSystemUserRequest = await this.httpService.post<ISystemUserResponse, IAddSystemUserRequest>(
      this.routes.UPDATE,
      payload,
    );
    return response;
  }

  public async deleteSystemUser(payload: IDeleteSystemUserRequest): Promise<ISystemUserDeleteResponse> {
    const response: IDeleteSystemUserRequest = await this.httpService.post<IDeleteSystemUserRequest, IDeleteSystemUserRequest>(
      this.routes.DELETE,
      payload,
    );
    return response;
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
      // await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }

}
