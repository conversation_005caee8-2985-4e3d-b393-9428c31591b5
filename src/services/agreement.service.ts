import {IAgreementByIdRequestParam, IAgreementPrGrpListRequestParam} from './../model/http.model';
import {IAgreementObjResponse, IPriceGroupListResponse, IUpdateAgreementRequest} from './../model/agreement.model';
import {
    IAddAgreementRequest,
    IAgreement,
    IAgreementDelteResponse,
    IAgreementResponse,
    IAgreementUpdateResponse,
    IPriceListResponse
} from '../model/agreement.model';
import {IAgreementPrListRequestParam, IRequestParam} from '../model/http.model';
import HttpService from './http.service';
import {AxiosError} from 'axios';
import {IAddMerchantRequest, IMerchantUpdateResponse} from "@/model/merchant.model";

export default class AgreementService {
    constructor(private readonly httpService: HttpService) {
    }

    protected readonly routes = {
        GETLIST: 'admin/agreement/list',
        GETBYID: 'admin/agreement/getAgreementDetail',
        GETCARRIERLIST: 'admin/agreement/getCarriers',
        GETPRICEGROUPS: 'admin/agreement/getPriceGroups',
        GETPRICELIST: 'admin/agreement/getPricing',
        GETPLATFORMLIST: '/admin/platform/list',
        CREATE: 'admin/agreement/create',
        UPDATE: 'admin/agreement/update',
        DELETE: 'admin/agreement/delete',
        DOWNLOAD_AGREEMENT_PDF: 'pdfAgreement/:company-id',
        GET_TEMPLATE_AGREEMENT_PDF: 'admin/previewAgreement',
    };

    public async getPriceGroupList(params: IRequestParam[] = []): Promise<IAgreement[]> {
        const token = HttpService.getToken();
        return this.httpService.get<IAgreement[]>(this.routes.GETLIST, [{key: 'limit', value: 100}, ...params]);
    }

//IAgreementPrListRequestParam
    public async getPriceGroups(payload: IAgreementPrGrpListRequestParam): Promise<IPriceGroupListResponse> {
        return await this.httpService.post<IAgreementPrGrpListRequestParam, IAgreementPrGrpListRequestParam>(
            this.routes.GETPRICEGROUPS,
            payload,
        );
    }

    public async getCustomerDetail(payload: IAgreementPrGrpListRequestParam): Promise<IPriceGroupListResponse> {
        const response: IAgreementPrGrpListRequestParam = await this.httpService.post<IAgreementPrGrpListRequestParam, IAgreementPrGrpListRequestParam>(
            'admin/agreement/getCustomerAgreementDetail',
            payload,
        );
        return response;
    }

    public async updateMerchantAgreement(payload: IAddMerchantRequest): Promise<IMerchantUpdateResponse> {
        const response: IMerchantUpdateResponse = await this.httpService.post<IMerchantUpdateResponse, IAddMerchantRequest>(
            'admin/agreement/update',
            payload,
        );
        return response;
    }


    public async getCarrierList(params: IRequestParam[] = []): Promise<IAgreement[]> {
        return this.httpService.get<IAgreement[]>(this.routes.GETCARRIERLIST);
    }

    public async getPriceList(payload: IAgreementPrListRequestParam): Promise<IPriceListResponse> {
        return await this.httpService.post<IPriceListResponse, IAgreementPrListRequestParam>(
            this.routes.GETPRICELIST,
            payload,
        );
    }

    public async getAgreementById(payload: IAgreementByIdRequestParam): Promise<IAgreementObjResponse> {
        console.log(payload)
        const response: IAgreementByIdRequestParam = await this.httpService.post<IAgreementObjResponse, IAgreementByIdRequestParam>(
            this.routes.GETBYID,
            payload,
        );
        return response;
    }


    public async getAgreementList(params: IRequestParam[] = []): Promise<IAgreement[]> {
        const token = HttpService.getToken();
        return this.httpService.get<IAgreement[]>(this.routes.GETLIST, [{key: 'limit', value: 100}, ...params]);
    }

    public async addAgreement(payload: IAddAgreementRequest): Promise<IAgreementResponse> {
        const response: IAddAgreementRequest = await this.httpService.post<IAgreementResponse, IAddAgreementRequest>(
            this.routes.CREATE,
            payload,
        );
        return response;
    }

    public async updateAgreement(payload: IUpdateAgreementRequest): Promise<IAgreementUpdateResponse> {
        const response: IAgreementUpdateResponse = await this.httpService.post<IAgreementUpdateResponse, IUpdateAgreementRequest>(
            this.routes.UPDATE,
            payload,
        );
        console.log(response)
        return response;
    }

    public async deleteAgreement(payload: IAgreementDelteResponse): Promise<IAgreementDelteResponse> {
        const response: IAgreementDelteResponse = await this.httpService.post<IAgreementDelteResponse, IAgreementDelteResponse>(
            this.routes.DELETE,
            payload,
        );
        return response;
    }

    public async onRejected(error: AxiosError | unknown): Promise<void> {
        if (
            error instanceof AxiosError &&
            error?.response?.status &&
            [401, 403].includes(error.response.status)
        ) {
            //await this.logout();
            return window.location.replace('/#/login');
        }

        return Promise.reject(error);
    }

    public async downloadAgreement(company_id: number): Promise<{ file: string, file1?: string }> {
        const url = this.routes.DOWNLOAD_AGREEMENT_PDF.replace(':company-id', company_id.toString());
        return this.httpService.get<{ file: string }>(url)
    }

    public async getTemplateAgreementPdf(): Promise<{ file: string }> {
        const url = this.routes.GET_TEMPLATE_AGREEMENT_PDF;
        return this.httpService.get<{ file: string }>(url)
    }
}
