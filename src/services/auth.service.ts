import {AxiosError} from 'axios';
// import { Notify } from 'quasar';
import {useMainStore} from "@/stores/main"

import {
    IForgotPasswordRequest,
    IForgotPasswordResponse,
    ILoginRequest,
    ILoginResponse,
    ILogoutRequest,
    IResetPasswordRequest,
    ISignUpRequest,
    ISignUpResponse,
    IOptResponse,
    IOptRequest, IVerifyOtp,
} from '../model/auth.model';
import HttpService from './http.service';

export default class AuthService {
    protected readonly routes = {
        LOGIN: 'admin/login',
        SIGNUP: 'register',
        LOGOUT: 'admin/logout',
        FORGOT_PASSWORD: 'admin/forgetPassword',
        RESET_PASSWORD: 'admin/changePassword',
        VerifyOPT: 'admin/verifyOTP',
    };

    constructor(protected readonly httpService: HttpService) {
        httpService.axios.interceptors.response.use((response) => response, this.onRejected.bind(this));
    }

    private static setLocalStorageToken(isRememberMe: boolean, token: string): void {
        if (isRememberMe) {
            localStorage.setItem('token', token);
        } else {
            sessionStorage.setItem('token', token);
        }
    }

    private static setPermissions(permissions: any): void {
        const permission_slugs = permissions.map((item) => {
            return item.slug;
        })
        useMainStore().setPermissions(permission_slugs)
        localStorage.setItem('permissions', JSON.stringify(permission_slugs));
    }

    public isAssigned(permission_slug?: string | string[]) {//array or string
        //let permissions = localStorage.getItem('permissions');
        let permissions = useMainStore().getPermissions as string[];
        let is_assigned: boolean = false;
        if (permissions != undefined && permissions) {
            //permissions = JSON.parse(permissions);
            if (typeof permission_slug == 'object') {
                is_assigned = permission_slug.some(r => permissions.indexOf(r) >= 0)
            } else {
                if (permissions.includes(permission_slug)) {
                    is_assigned = true;
                }
            }

        }
        return is_assigned;
    }

    public async onRejected(error: AxiosError | unknown): Promise<void> {
        if (
            error instanceof AxiosError &&
            error?.response?.status &&
            [401, 403].includes(error.response.status)
        ) {
            await this.logout();
            return window.location.replace('/#/login');
        }

        return Promise.reject(error);
    }

    public async login(email: string, password: string, isRememberMe: boolean): Promise<ILoginResponse> {
        const time_zone = Intl.DateTimeFormat().resolvedOptions().timeZone
        const response: ILoginResponse = await this.httpService.post<ILoginResponse, ILoginRequest>(this.routes.LOGIN, {
            email,
            password,
            time_zone
        });
        const token = response.data['token'];
        useMainStore().setUser(response.data['admin'])
        // useMainStore().setPermissions(response.data['admin']['permissions'])

        this.httpService.setToken(token);
        AuthService.setLocalStorageToken(isRememberMe, token);
        AuthService.setPermissions(response.data['admin']['permissions'])
        return response;
    }

    public async sendOpt(opt: string, email: string): Promise<IOptResponse> {
        const response: IOptResponse = await this.httpService.post<IOptResponse, IOptRequest>(this.routes.VerifyOPT, {
            opt,
            email
        });
        console.log(response)
        return response;
    }

    public async signUp(payload: ISignUpRequest): Promise<ISignUpResponse> {
        const response: ISignUpResponse = await this.httpService.post<ISignUpResponse, ISignUpRequest>(
            this.routes.SIGNUP,
            payload,
        );

        this.httpService.setToken(response.user.token);
        AuthService.setLocalStorageToken(true, response.user.token);

        return response;
    }

    public async logout(): Promise<void> {
        const token = HttpService.getToken();
        console.log(token)
        if (!token) {
            return;
        }
        try {
            await this.httpService.post<[false], ILogoutRequest>(this.routes.LOGOUT, {
                token,
            });
        } finally {
            sessionStorage.removeItem('token');
            localStorage.removeItem('token');
            useMainStore().setUser({})
            window.location.href = '/'
            setTimeout(() => {
                useMainStore().setLogout()
            }, 500);
        }
    }

    public async forgotPassword(email: string): Promise<IForgotPasswordResponse> {
        return this.httpService.post<IForgotPasswordResponse, IForgotPasswordRequest>(
            this.routes.FORGOT_PASSWORD,
            {email},
        );
    }

    public async resetPassword(password: string, otp: string): Promise<void> {
        return this.httpService.post<void, IResetPasswordRequest>(this.routes.RESET_PASSWORD, {
            password,
            otp,
        });
    }

    public async verifyOtp(otp: string): Promise<void> {
        return this.httpService.post<void, IVerifyOtp>(this.routes.VerifyOPT, {
            otp
        });
    }

}
