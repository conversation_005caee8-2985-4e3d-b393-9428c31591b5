import {
  IRole,
  IAddRoleRequest,
  IDeleteRoleRequest,
  IRoleResponse,
  IRoleDeleteResponse,
  IRoleProduct, IDeleteRoleProductRequest, IRoleProductCreateResponse, IRoleProductRequest
} from '../model/role.model';
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';
import { AxiosError } from 'axios';

export default class RoleService {
  constructor(private readonly httpService: HttpService) {}

  protected readonly routes = {
    GETLIST: 'admin/role/list',
    CREATE: 'admin/role/create',
    UPDATE: 'admin/role/update',
    DELETE: 'admin/role/delete',
    EDIT: 'admin/role/'
  };

  public async getRoleList(params: IRequestParam[] = []): Promise<IRole[]> {
    return this.httpService.get<IRole[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }

  public async addRole(payload: IAddRoleRequest): Promise<IRoleResponse> {
    const response: IAddRoleRequest = await this.httpService.post<IRoleResponse, IAddRoleRequest>(
      this.routes.CREATE,
      payload

    );
    return response;
  }


  public async updateRole(payload: IAddRoleRequest): Promise<IRoleResponse> {
    const response: IAddRoleRequest = await this.httpService.post<IRoleResponse, IAddRoleRequest>(
      this.routes.UPDATE,
      payload,
    );
    return response;
  }

  public async deleteRole(payload: IDeleteRoleRequest): Promise<IRoleDeleteResponse> {
    const response: IDeleteRoleRequest = await this.httpService.post<IDeleteRoleRequest, IDeleteRoleRequest>(
      this.routes.DELETE,
      payload,
    );
    return response;
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
      // await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }
}
