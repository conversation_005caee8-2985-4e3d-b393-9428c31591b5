import HttpService from "./http.service";

interface IShippingCountry {
    origin: string,
    receiver_country: string,
    is_active: boolean
}

interface IShippingCountryResponse {
    code: number,
    message: string,
    data: {
        product: IShippingCountry
    }
}

class ShippingCountriesService {
    constructor(private readonly httpService: HttpService) {
    }

    private readonly ROUTES = {
        'GET_SHIPPING_COUNTRIES': 'admin/shipping-countries',
        'CREATE_SHIPPING_COUNTRY': 'admin/shipping-countries',
        'UPDATE_SHIPPING_COUNTRY': 'admin/shipping-countries/:id',
        'DELETE_SHIPPING_COUNTRY': 'admin/shipping-countries/:id'
    }

    public async getShippingCountries(payload): Promise<[]> {
        return await this.httpService.get<[]>(`${this.ROUTES.GET_SHIPPING_COUNTRIES}?page=${payload.current_page}&perpage=${payload.per_page}&filter=${payload.filter}`, []);
    }

    public async addShippingCountry(payload: IShippingCountry): Promise<IShippingCountryResponse> {
        return await this.httpService.post<IShippingCountryResponse, IShippingCountry>(
            this.ROUTES.CREATE_SHIPPING_COUNTRY,
            payload
        );
    }

    public async updateShippingCountry(payload: IShippingCountry, id: number): Promise<IShippingCountryResponse> {
        const url = this.ROUTES.UPDATE_SHIPPING_COUNTRY.replace(':id', id.toString());
        return await this.httpService.put<IShippingCountryResponse, IShippingCountry>(
            url,
            payload
        );
    }

    public async deleteShippingCountry(id: number): Promise<IShippingCountryResponse> {
        const url = this.ROUTES.DELETE_SHIPPING_COUNTRY.replace(':id', id.toString());
        return await this.httpService.delete<IShippingCountryResponse>(url);
    }
}

export default ShippingCountriesService;