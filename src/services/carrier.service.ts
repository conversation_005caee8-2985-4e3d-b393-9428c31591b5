import {
    IAddCarrierRequest,
    ICarrier,
    ICarrierDelteResponse,
    ICarrierProduct,
    ICarrierProductCreateResponse,
    ICarrierProductRequest,
    ICarrierResponse,
    ICarrierStatusUpdateRequest,
    IDeleteCarrierProductRequest,
    IDeleteCarrierRequest,
} from '../model/carrier.model';
import {IRequestParam} from '../model/http.model';
import HttpService from './http.service';
import {AxiosError} from 'axios';

export default class CarrierService {
    constructor(private readonly httpService: HttpService) {
    }

    protected readonly routes = {
        GETLIST: 'admin/carrier/list',
        CREATE: 'admin/carrier/create',
        UPDATE: 'admin/carrier/update',
        UPDATE_STATUS: 'admin/carrier/statusupdate',
        DELETE: 'admin/carrier/delete',
        GET_CARRIER_PRODUCT_LIST: 'admin/carrier/product/list',
        DELETE_CARRIER_PRODUCT: 'admin/carrier/product/delete',
        CREATE_CARRIER_PRODUCT: 'admin/carrier/product/create',
        UPDATE_CARRIER_PRODUCT: 'admin/carrier/product/update', //Majid
        GET_PRODUCTS_BY_CARRIER: 'admin/carrier/product/:id',

    };

    public async getCarrierList(params: IRequestParam[] = []): Promise<ICarrier[]> {
        return this.httpService.get<ICarrier[]>(this.routes.GETLIST, [{key: 'limit', value: 100}, ...params]);
    }

    public async getProductsByCarrier(id: number): Promise<ICarrierProduct[]> {
        const url = this.routes.GET_PRODUCTS_BY_CARRIER.replace(":id", id.toString());
        return this.httpService.get<ICarrierProduct[]>(url);
    }

    public async getCarrierProductList(params: IRequestParam[] = []): Promise<ICarrierProduct[]> {
        return this.httpService.get<ICarrierProduct[]>(this.routes.GET_CARRIER_PRODUCT_LIST, [{
            key: 'limit',
            value: 100
        }, ...params]);
    }

    public async addCarrier(payload: IAddCarrierRequest): Promise<ICarrierResponse> {
        const response: IAddCarrierRequest = await this.httpService.post<ICarrierResponse, IAddCarrierRequest>(
            this.routes.CREATE,
            payload,
            [],
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            }
        );
        return response;
    }

    public async addCarrierProduct(payload: ICarrierProductRequest): Promise<ICarrierProductCreateResponse> {
        const response: ICarrierProductCreateResponse = await this.httpService.post<ICarrierProductCreateResponse, ICarrierProductRequest>(
            this.routes.CREATE_CARRIER_PRODUCT,
            payload
        );
        return response;
    }

    public async updateCarrier(payload: IAddCarrierRequest): Promise<ICarrierResponse> {
        const response: IAddCarrierRequest = await this.httpService.post<ICarrierResponse, IAddCarrierRequest>(
            this.routes.UPDATE,
            payload,
        );
        return response;
    }

    public async updateCarrierStatus(payload: ICarrierStatusUpdateRequest): Promise<ICarrierResponse> {
        return await this.httpService.post<ICarrierResponse, ICarrierStatusUpdateRequest>(
            this.routes.UPDATE_STATUS,
            payload,
        );
    }

    public async deleteCarrier(payload: IDeleteCarrierRequest): Promise<ICarrierDelteResponse> {
        const response: IDeleteCarrierRequest = await this.httpService.post<IDeleteCarrierRequest, IDeleteCarrierRequest>(
            this.routes.DELETE,
            payload,
        );
        return response;
    }

    public async deleteCarrierProduct(payload: IDeleteCarrierProductRequest): Promise<ICarrierDelteResponse> {
        const response: ICarrierDelteResponse = await this.httpService.post<ICarrierDelteResponse, IDeleteCarrierProductRequest>(
            this.routes.DELETE_CARRIER_PRODUCT,
            payload,
        );
        return response;
    }

    //Majid
    public async updateCarrierProduct(payload: ICarrierProductRequest): Promise<ICarrierProductCreateResponse> {
        const response: ICarrierProductCreateResponse = await this.httpService.post<ICarrierProductCreateResponse, ICarrierProductRequest>(
            this.routes.UPDATE_CARRIER_PRODUCT,
            payload,
        );
        return response;
    }

    public async onRejected(error: AxiosError | unknown): Promise<void> {
        if (
            error instanceof AxiosError &&
            error?.response?.status &&
            [401, 403].includes(error.response.status)
        ) {
            // await this.logout();
            return window.location.replace('/#/login');
        }

        return Promise.reject(error);
    }
}
