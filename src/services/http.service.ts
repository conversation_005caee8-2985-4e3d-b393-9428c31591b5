import axios, {AxiosInstance, AxiosRequestConfig} from 'axios';
import {set} from 'lodash-es';
import {IRequestParam} from '../model/http.model';
import HelperService from './helper.service';

export default class HttpService {
    /**
     * Axios instance
     */
    public readonly axios: AxiosInstance;

    constructor(baseURL: string) {
        const token: string | null = HttpService.getToken();

        const config: AxiosRequestConfig = {
            baseURL,
            headers: {
                // Apikey: import.meta.env.VITE_apiKey,
                'Accept-Language': 'en',
                'Access-Control-Max-Age': 7200,
            } as object,
        };

        if (config.headers && token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        this.axios = axios.create(config);
    }

    public static getToken(): string | null {
        if (localStorage.getItem('token')) {
            return localStorage.getItem('token') as string;
        }

        if (sessionStorage.getItem('token')) {
            return sessionStorage.getItem('token') as string;
        }

        return null;
    }

    public setToken(token: string): void {
        // console.log(token)
        set(this.axios.defaults, 'headers.Authorization', `Bearer ${token}`);
    }

    public removeToken(): void {
        set(this.axios.defaults, 'headers.Authorization', undefined);
    }

    public static generateFilter(filter: IRequestParam[]): string {
        let filterString = '';

        filter.forEach((param: IRequestParam) => {
            filterString += filterString.length === 0 ? '?' : '&';
            filterString += `${param.key}=${param.value}`;
        });

        return filterString;
    }

    public async get<T>(url: string, params?: IRequestParam[]): Promise<T> {
        let requestURI = url;

        if (params) {
            requestURI += HttpService.generateFilter(params);
        }

        return HelperService.unwrapAxiosResponse(this.axios.get<T>(requestURI));
    }

    public async post<T, P>(url: string, payload?: P, params?: IRequestParam[], headers?: any): Promise<T> {
        let requestURI = url;

        if (params) {
            requestURI += HttpService.generateFilter(params);
        }

        return HelperService.unwrapAxiosResponse(this.axios.post<T>(requestURI, payload, headers));
    }

    public async patch<T, P>(url: string, payload?: P, params?: IRequestParam[]): Promise<T> {
        let requestURI = url;

        if (params) {
            requestURI += HttpService.generateFilter(params);
        }

        return HelperService.unwrapAxiosResponse(this.axios.patch<T>(requestURI, payload));
    }

    public async put<T, P>(url: string, payload?: P, params?: IRequestParam[]): Promise<T> {
        let requestURI = url;

        if (params) {
            requestURI += HttpService.generateFilter(params);
        }

        return HelperService.unwrapAxiosResponse(this.axios.put<T>(requestURI, payload));
    }

    public async delete<T>(url: string, params?: IRequestParam[]): Promise<T> {
        let requestURI = url;

        if (params) {
            requestURI += HttpService.generateFilter(params);
        }

        return HelperService.unwrapAxiosResponse(this.axios.delete<T>(requestURI));
    }
}
