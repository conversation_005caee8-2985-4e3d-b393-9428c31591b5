import {
    IDashboardContentResponse,

} from '../model/carrier.model';
import {IRequestParam} from '../model/http.model';
import HttpService from './http.service';
import {AxiosError} from 'axios';

export default class DashboardService {
    constructor(private readonly httpService: HttpService) {
    }

    protected readonly routes = {
        GET: 'admin/dashboard/management',
    };

    public async getDashboardContent(params: IRequestParam[] = []): Promise<IDashboardContentResponse> {
        return this.httpService.get<IDashboardContentResponse>(this.routes.GET, [{
            key: 'limit',
            value: 100
        }, ...params]);
    }
}
