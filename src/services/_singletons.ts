import AuthService from './auth.service';
import CountryService from './country.service';
import HelperService from './helper.service';
import HttpService from './http.service';
import LoadingService from './loading.service';
import UserService from './user.service';
import ShippingCountryService from './shipping_country.service';
import DimensionService from './dimension.service';
import AddressService from './address.service';
// import NotificationService from './notification.service';
import FileService from './file.service';
import UbsendService from './ubsend.service';
import MerchantService from './merchant.service';
import PriceGroupService from './priceGroup.service';
import AgreementService from './agreement.service';
import VideoService from './video.service';
import CarrierService from './carrier.service';
import WeightClassesService from './weightClasses.service'; //Majid import Services
import TemplateService from './template.service';
import PermissionService from './permission.service';
import RoleService from './role.service';
import SystemUserService from './system_user.service';
import DashboardService from "./dashboard.service";
import TransactionService from "./transaction.service";
import CarrierValidationService from "./carrierValidation.service";
import LogService from "./log.service";

export const httpService: HttpService = new HttpService(import.meta.env.VITE_baseUrl);
export const loadingService: LoadingService = new LoadingService();
export const helperService: HelperService = new HelperService();
export const authService: AuthService = new AuthService(httpService);
export const userService: UserService = new UserService(httpService);
export const fileService: FileService = new FileService();
export const countryService: CountryService = new CountryService(httpService);
export const ubsendService: UbsendService = new UbsendService(httpService);
export const merchantService: MerchantService = new MerchantService(httpService);
export const priceGroupService: PriceGroupService = new PriceGroupService(httpService);
export const agreementService: AgreementService = new AgreementService(httpService);
export const videoService: VideoService = new VideoService(httpService);
export const carrierService: CarrierService = new CarrierService(httpService);
export const weightClassesService: WeightClassesService = new WeightClassesService(httpService); // Majid
export const templateService: TemplateService = new TemplateService(httpService);
export const permissionService: PermissionService = new PermissionService(httpService);
export const roleService: RoleService = new RoleService(httpService);
export const systemUserService: SystemUserService = new SystemUserService(httpService);
export const dashboardService: DashboardService = new DashboardService(httpService);
export const transactionService: TransactionService = new TransactionService(httpService);
export const shippingCountryService: ShippingCountryService = new ShippingCountryService(httpService);
export const dimensionService: DimensionService = new DimensionService(httpService);
export const carrierValidationService: CarrierValidationService = new CarrierValidationService(httpService);
export const addressService: AddressService = new AddressService(httpService);
export const logService: LogService = new LogService(httpService);
// export const notificationService: NotificationService = new NotificationService();

