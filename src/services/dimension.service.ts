import HttpService from "./http.service";

interface ISettingsDimensions {
    [key: string]: any,
}

interface ISettingsDimensionsResponse {
    [key: string]: any,
}

class DimensionService {
    constructor(private readonly httpService: HttpService) {
    }

    private readonly ROUTES = {
        'GET_DIMENSIONS_LIST': 'admin/settings/dimensions/list',
        'CREATE_DIMENSION': 'admin/settings/dimensions',
        'UPDATE_DIMENSION_LIST': 'admin/settings/dimensions/:id',
        'DELETE_DIMENSION': 'admin/settings/dimensions/:id',
    }

    public async getDimensionsList(): Promise<[]> {
        return await this.httpService.get<[]>(`${this.ROUTES.GET_DIMENSIONS_LIST}`, []);
    }


    public async updateDimension(payload: ISettingsDimensions, id: number): Promise<ISettingsDimensionsResponse> {
        const url = this.ROUTES.DELETE_DIMENSION.replace(':id', id.toString())
        return await this.httpService.put<ISettingsDimensionsResponse, ISettingsDimensions>(
            url,
            payload
        );
    }

    public async createDimension(payload: ISettingsDimensions): Promise<ISettingsDimensionsResponse> {
        return await this.httpService.post<ISettingsDimensionsResponse, ISettingsDimensions>(
            this.ROUTES.CREATE_DIMENSION,
            payload
        );
    }

    public async deleteDimension(id: string | number): Promise<ISettingsDimensionsResponse> {
        const url = this.ROUTES.DELETE_DIMENSION.replace(':id', id.toString())
        return await this.httpService.delete<ISettingsDimensionsResponse>(
            url
        );
    }
}

export default DimensionService;