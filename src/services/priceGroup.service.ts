import { IPriceEditRequestParam } from './../model/http.model';
import { IPriceListResponse } from './../model/priceGroup.model';
import { IPriceGroup,IAddPriceGroupRequest,IPriceGroupUpdateResponse,IDeletePriceGroupRequest,IPriceGroupResponse,IPriceGroupDelteResponse} from '../model/priceGroup.model'; 
import { IRequestParam ,ICarrierRequestParam,IPriceListRequestParam} from '../model/http.model';
import HttpService from './http.service';
import { AxiosError } from 'axios';
import { pa } from 'element-plus/es/locale';

export default class PriceGroupService {
  constructor(private readonly httpService: HttpService) {}

  protected readonly routes = {
    GETLIST: 'admin/price/group/list',
    GETCARRIERLIST: 'admin/price/group/getCarrierList',
    GETPRICELIST: 'admin/price/group/getPrices',
    GETPLATFORMLIST: '/admin/platform/list',
    CREATE: 'admin/price/group/create',
    EdIT: 'admin/price/group/edit',
    UPDATE: 'admin/price/group/update',
    DELETE: 'admin/price/group/delete',
  };

  public async getPriceGroupList(params: IRequestParam[] = []): Promise<IPriceGroup[]> {
    const token = HttpService.getToken();
    return this.httpService.get<IPriceGroup[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }

  public async getCarrierList(payload: ICarrierRequestParam): Promise<IPriceGroupResponse> {
    const response: ICarrierRequestParam = await this.httpService.post<ICarrierRequestParam, ICarrierRequestParam>(
      this.routes.GETCARRIERLIST,
      payload,
    );
    return response;
  }

  public async getPriceList(payload: IPriceListRequestParam): Promise<IPriceListResponse> {
    console.log(payload)
    const response: IPriceListRequestParam = await this.httpService.post<IPriceListRequestParam, IPriceListRequestParam>(
      this.routes.GETPRICELIST,
      payload,
    );
    console.log(payload)

    return response;
  }

  public async getPriceGroupById(payload: IPriceEditRequestParam): Promise<IPriceGroupUpdateResponse> {
    console.log(payload)
    const response: IPriceGroupUpdateResponse = await this.httpService.post<IPriceGroupUpdateResponse,IPriceEditRequestParam>(
      this.routes.EdIT,
      payload,
    );

    return response;
  }


  // public async getPriceGroupById(params: IRequestParam[] = []): Promise<IPriceGroup[]> {
  //   const token = HttpService.getToken();
  //   return this.httpService.get<IPriceGroup[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  // }

  public async addPriceGroup(payload: IAddPriceGroupRequest): Promise<IAddPriceGroupRequest> {
    const response: IAddPriceGroupRequest = await this.httpService.post<IAddPriceGroupRequest, IAddPriceGroupRequest>(
      this.routes.CREATE,
      payload,
    );
    return response;
  }

  public async updatePriceGroup(payload: IAddPriceGroupRequest): Promise<IPriceGroupUpdateResponse> {
    const response: IPriceGroupUpdateResponse = await this.httpService.post<IPriceGroupUpdateResponse, IAddPriceGroupRequest>(
      this.routes.UPDATE,
      payload,
    );
    console.log(response)
    return response;
  }

  public async deletePriceGroup(payload: IPriceGroupDelteResponse): Promise<IPriceGroupDelteResponse> {
    const response: IPriceGroupDelteResponse = await this.httpService.post<IPriceGroupDelteResponse, IPriceGroupDelteResponse>(
      this.routes.DELETE,
      payload,
    );
    return response;
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
      //await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }
}
