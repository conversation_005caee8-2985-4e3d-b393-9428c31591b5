import { IVideo,IAddVideoRequest,IDeleteVideoRequest,IVideoResponse,IVideoDelteResponse} from '@/model/video.model';
import { IRequestParam } from '@/model/http.model';
import HttpService from './http.service';
import { AxiosError } from 'axios';

export default class VideoService {
  constructor(protected readonly httpService: HttpService) {
    httpService.axios.interceptors.response.use((response) => response, this.onRejected.bind(this));
  }
  protected readonly routes = {
    GETLIST: 'admin/onboarding/video/list',
    CREATE: 'admin/onboarding/video/create',
    UPDATE: 'admin/onboarding/video/update',
    DELETE: 'admin/onboarding/video/delete',
  };

  public async getVideoList(params: IRequestParam[] = []): Promise<IVideo[]> {
    return this.httpService.get<IVideo[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }

  public async addVideo(payload: any): Promise<IVideoResponse> {
    const response: IAddVideoRequest = await this.httpService.post<IVideoResponse, IAddVideoRequest>(
      this.routes.CREATE,
      payload,
    );
    return response;
  }

  public async updateVideo(payload: any): Promise<IVideoResponse> {
    const response: IAddVideoRequest = await this.httpService.post<IVideoResponse, IAddVideoRequest>(
      this.routes.UPDATE,
      payload,
    );
    return response;
  }

  public async deleteVideo(payload: IDeleteVideoRequest): Promise<IVideoDelteResponse> {
    const response: IDeleteVideoRequest = await this.httpService.post<IDeleteVideoRequest, IDeleteVideoRequest>(
      this.routes.DELETE,
      payload,
    );
    return response;
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
      //await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }
}
