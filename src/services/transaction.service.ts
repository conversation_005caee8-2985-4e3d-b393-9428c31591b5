import HttpService from "./http.service";
import {IRequestParam} from "../model/http.model";

class TransactionService {
    constructor(private readonly httpService: HttpService) {
    }

    private readonly ROUTES = {
        DOWNLOAD_TRANSACTION: '/admin/merchant/payments/transactions-download/:transaction-code',
        DOWNLOAD_MONTHLY_INVOICE: '/admin/invoice/monthly/download/:invoice-id',
    }

    public async downloadTransaction(transactionCode: number, requestParams: IRequestParam[] = []): Promise<{ file: string }> {
        const url = this.ROUTES.DOWNLOAD_TRANSACTION.replace(':transaction-code', transactionCode.toString());
        return this.httpService.get<{ file: string }>(url, requestParams)
    }

    public async downloadMonthlyInvoice(invoiceId: number, requestParams: IRequestParam[] = []): Promise<{ file: string, file1?: string }> {
        const url = this.ROUTES.DOWNLOAD_MONTHLY_INVOICE.replace(':invoice-id', invoiceId.toString());
        return this.httpService.get<{ file: string }>(url, requestParams)
    }
}

export default TransactionService;