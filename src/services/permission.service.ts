import {
  IPermission,
  IAddPermissionRequest,
  IDeletePermissionRequest,
  IPermissionResponse,
  IPermissionDeleteResponse,
  IPermissionProduct, IDeletePermissionProductRequest, IPermissionProductCreateResponse, IPermissionProductRequest
} from '../model/permission.model';
import { IRequestParam } from '../model/http.model';
import HttpService from './http.service';
import { AxiosError } from 'axios';

export default class PermissionService {
  constructor(private readonly httpService: HttpService) {}

  protected readonly routes = {
    GETLIST: 'admin/permissions/list',
    CREATE: 'admin/permissions/create',
    UPDATE: 'admin/permissions/update',
    DELETE: 'admin/permissions/delete',
    EDIT: 'admin/permissions/'
  };

  public async getPermissionList(params: IRequestParam[] = []): Promise<IPermission[]> {
    return this.httpService.get<IPermission[]>(this.routes.GETLIST, [{ key: 'limit', value: 100 }, ...params]);
  }

  public async addPermission(payload: IAddPermissionRequest): Promise<IPermissionResponse> {
    const response: IAddPermissionRequest = await this.httpService.post<IPermissionResponse, IAddPermissionRequest>(
      this.routes.CREATE,
      payload

    );
    return response;
  }


  public async updatePermission(payload: IAddPermissionRequest): Promise<IPermissionResponse> {
    const response: IAddPermissionRequest = await this.httpService.post<IPermissionResponse, IAddPermissionRequest>(
      this.routes.UPDATE,
      payload,
    );
    return response;
  }

  public async deletePermission(payload: IDeletePermissionRequest): Promise<IPermissionDeleteResponse> {
    const response: IDeletePermissionRequest = await this.httpService.post<IDeletePermissionRequest, IDeletePermissionRequest>(
      this.routes.DELETE,
      payload,
    );
    return response;
  }

  public async onRejected(error: AxiosError | unknown): Promise<void> {
    if (
      error instanceof AxiosError &&
      error?.response?.status &&
      [401, 403].includes(error.response.status)
    ) {
      // await this.logout();
      return window.location.replace('/#/login');
    }

    return Promise.reject(error);
  }
}
