import axios from "axios";
import {defineStore} from "pinia"

// const BASE_URL = process.env.VITE_baseUrl;
const BASE_URL = 'https://development.adminapi.shipvagoo.com/api';
const token = sessionStorage.getItem("token");
export const useMerchantState = defineStore("merchent", {
    state: () => ({
        user: {
            company_url: "",
            person_name: "",
            phone: "",
            email: "",
            address: "",
            country: 8,
            city: "",
            zip_code: "",
            cvr: "",
            platform: null,
            platform_url: null,
            summary: null,
            agreement_summary: null,
            current_agreement: [
                {
                    id: 47,
                    user_code: 3023368305,
                    country_from: 1,
                    country_to: 2,
                    parcels: 100,
                    created_at: "2022-12-28T11:58:40.000000Z",
                    updated_at: "2022-12-28T11:58:40.000000Z"
                }
            ]
        },
        logs: [],
        splashScreen: true,
        logged: true
    }),
    actions: {
        getMerchantDetail(payload: any) {
            axios.get(`https://development.adminapi.shipvagoo.com/api/admin/merchant/summary/${payload.id}`, {
                headers: {
                    'Authorization': 'Bearer ' + sessionStorage.getItem("token")
                }
            }).then(response => {
                console.log(response.data, "hello")
                this.user = response.data.data.user ?? {};
                this.logs = response.data.data.logs ?? [];
            }).catch(err => {
                console.log(err);
            })
        },
    },
    getters: {
        getUser(state) {
            return this.user
        },
        getLogs(state) {
            return this.logs
        }
    },
    persist: {}
})

