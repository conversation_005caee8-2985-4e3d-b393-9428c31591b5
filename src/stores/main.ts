import {ENavPos, EToolbar, EViewAnimation, type State, type StateLayout} from "@/types"
import {defineStore, acceptHMRUpdate} from "pinia"

export const useMainStore = defineStore("main", {
    state: (): State => ({
        layout: {
            navPos: ENavPos.left, //top, bottom, left, right, false
            toolbar: EToolbar.top, //top, bottom, false
            footer: true, //above, below, false
            boxed: false, //true, false
            roundedCorners: false, //true, false
            viewAnimation: EViewAnimation.fadeTop,// fade-left, fade-right, fade-top, fade-top-in-out, fade-bottom, fade-bottom-in-out, fade, false
            priceGroup: null,
            ubsend: {},
            user: {},
            merchant: null,
            customer: null,
            password: null,
            c_password: null,
            countryList: [],
            carrierProduct: null,
            permissions: []

        },
        splashScreen: true,
        logged: true
    }),
    actions: {
        setLayout(payload: Partial<StateLayout>) {
            if (payload && payload.navPos !== undefined) this.layout.navPos = payload.navPos

            if (payload && payload.toolbar !== undefined) this.layout.toolbar = payload.toolbar

            if (payload && payload.footer !== undefined) this.layout.footer = payload.footer

            if (payload && payload.boxed !== undefined) this.layout.boxed = payload.boxed

            if (payload && payload.roundedCorners !== undefined) this.layout.roundedCorners = payload.roundedCorners

            if (payload && payload.viewAnimation !== undefined) this.layout.viewAnimation = payload.viewAnimation
        },
        setLogin() {
            this.logged = true
        },
        setLogout() {
            this.layout.navPos = null
            this.layout.toolbar = null
            this.logged = false
        },
        setSplashScreen(payload: boolean) {
            this.splashScreen = payload
        },
        setUbsend(payload: object) {
            this.layout.ubsend = payload
        },
        setUser(payload: object) {
            this.layout.user = payload
        },
        setPermissions(payload: object) {
            this.layout.permissions = payload
        },
        setMerchant(payload: object) {
            this.layout.merchant = payload
        },
        setCarrierProduct(payload: object) {
            this.layout.carrierProduct = payload
        },
        setPriceGroup(payload: object) {
            this.layout.priceGroup = payload
        },
        setCustomer(payload: object) {
            this.layout.customer = payload
        },
        setPassword(payload: object) {
            this.layout.password = payload
        },
        setConfirmPassword(payload: object) {
            this.layout.c_password = payload
        },
        setCountryList(payload: object) {
            this.layout.countryList = payload
        },
    },
    getters: {
        navPos(state) {
            return state.layout?.navPos
        },
        toolbar(state) {
            return state.layout?.toolbar
        },
        footer(state) {
            return state.layout?.footer
        },
        boxed(state) {
            return state.layout?.boxed
        },
        roundedCorners(state) {
            return state.layout?.roundedCorners
        },
        viewAnimation(state) {
            return state.layout?.viewAnimation
        },
        isLogged(state) {
            return state.logged
        },
        selectedUbsend(state) {
            return state.layout.ubsend
        },
        getUser(state) {
            return state.layout.user
        },
        getMerchant(state) {
            return state.layout.merchant
        },
        getCarrierProduct(state) {
            return state.layout.carrierProduct
        },
        getPassword(state) {
            return state.layout.password
        },
        getConfirmPassword(state) {
            return state.layout.c_password
        },
        getPriceGroup(state) {
            return state.layout.priceGroup
        },
        getCustomer(state) {
            return state.layout.customer
        },
        getCountryList(state) {
            return state.layout.countryList
        },
        getPermissions(state) {
            return state.layout.permissions
        },
    },
    persist: {
        storage: sessionStorage,
        paths: ["layout"]
    }
})

if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(useMainStore, import.meta.hot))
}
