@import "_variables";
@import "_mixins";
@import "element-variables";
@import "card-shadow";
@import "helpers";
@import "typography";
@import "status-indicator";
@import "color-schemes";
@import "float-label";
//@import "vuebar";
//@import "perfect-scrollbar";
@import "tables";
//@import 'vue-good-table-v1';
@import "vue-good-table-v2";
@import "ele-table";
@import "v2-table";
@import "fullcalendar";
@import "style";
@import "../fonts/Nunito-Sans.css";

html,
body {
  font-family: "Nunito Sans", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.4;

  height: 100%;
  //height: 100vh;
  width: 100%;
  //width: 100vw;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  position: fixed;

  background: darken($background-color, 30%);
  color: $text-color-primary;
}

[v-cloak] {
  display: none !important;
}

::selection {
  background-color: $text-color-accent;
  color: #fff;
}

::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: transparentize($text-color-primary, 0.5);
  opacity: 1; /* Firefox */
}

:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: transparentize($text-color-primary, 0.5);
}

::-ms-input-placeholder {
  /* Microsoft Edge */
  color: transparentize($text-color-primary, 0.5);
}

#app {
  height: 100vh;
}

.page-header {
  margin-bottom: 45px;

  h1 {
    margin: 0;
    @include text-bordered-shadow();
  }

  h4 {
    margin: 0;
    margin-top: 10px;
    opacity: 0.5;
  }

  .el-breadcrumb {
    margin-top: 10px;
    line-height: 1.3;

    .el-breadcrumb__inner,
    .el-breadcrumb__inner a {
      color: $text-color-primary;
    }

    .el-breadcrumb__separator {
      color: transparentize($text-color-primary, 0.7);
    }

    .el-breadcrumb__item:last-child .el-breadcrumb__inner,
    .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover,
    .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
    .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover {
      color: transparentize($text-color-primary, 0.4);
    }
  }

  &.card-base {
    padding: 24px;
  }

  &.header-accent {
    background: $text-color-accent !important;
  }

  &.header-primary {
    background: $text-color-primary !important;
  }

  &.header-accent-gradient {
    background: $text-color-accent;
    background: linear-gradient(45deg, $text-color-accent 0%, lighten($text-color-accent, 20%) 100%) !important;
  }

  &.header-accent,
  &.header-primary,
  &.header-accent-gradient {
    h1,
    h4 {
      color: white;
      text-shadow: none;
    }

    .el-breadcrumb {
      .el-breadcrumb__inner,
      .el-breadcrumb__inner a {
        color: white;
      }

      .el-breadcrumb__separator {
        color: transparentize(white, 0.7);
      }

      .el-breadcrumb__item:last-child .el-breadcrumb__inner,
      .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover,
      .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
      .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover {
        color: transparentize(white, 0.4);
      }
    }
  }
}

/* fix Vuetify flex problem */
.flex {
  flex: initial;
}

.container {
  padding: initial;
}

.btn-blue-bg {
  background-color: $background-color-blue3;
  color: #fff;
}

/*@media (max-width: 768px) {
	.page-header:not(.card-base) {
		padding: 0 10px;
	}
}*/
