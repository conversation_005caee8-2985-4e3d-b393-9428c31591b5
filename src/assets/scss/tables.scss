table.styled {
    width: 100%;
    border: none;
    border-collapse: collapse;
    text-align: left;

    thead {
        background: transparentize($background-color, 0.7);
        border-bottom: 2px solid transparentize($background-color, 0.5);

        tr {
            th {
                padding: 12px 20px;
                color: transparentize($text-color-primary, 0.6);
            }
        }
    }

    tbody tr td {
        padding: 12px 20px;
    }

    &.striped {
        tbody tr:nth-child(even) {
            background: transparentize($background-color, 0.6);
        }
    }

    &.fixed-header {
        width: 100%;
        height: 100%;
        table-layout: fixed;

        tr {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            --flex-grid: 12;

            th,
            td {
                word-wrap: break-word;
                overflow: hidden;
                -webkit-box-flex: 1;
                -ms-flex: 1 0;
                flex: 1 0;
            }
        }

        tbody {
            display: block;
            overflow: auto;
            width: calc(100% + 6px);
            height: 100%;
        }
    }
}
