$background-color: #f3f7fa; //#e8ecee //#F0F5FA //#f6f9fc
$text-color-white: #f3f7fa; //#e8ecee //#F0F5FA //#f6f9fc
$text-color-primary: #4a596a; //#0d2b3e //#34495e 
$text-color-accent: #5f8fdf; //#409EFF
$text-color-success: #13ce66;
$text-color-warning: #f7ba2a;
$text-color-danger: #ec205f;
$text-color-info: #909399;
$background-color-blue: #1E1E1E;
$background-color-blue2: #151526;
$background-color-blue3: #006EFB;
$navigation-color-black: #000;
$navigation-color-white: white;


/* theme-default
$background-color: #f3f7fa; 
$text-color-primary: #4a596a; 
$text-color-accent: #5f8fdf; 
*/

/* theme-a
$background-color: #ffffff; 
$text-color-primary: #000000; 
$text-color-accent: #D7195D; 
*/

/* theme-b
$background-color: #8794A3; 
$text-color-primary: #ffffff; 
$text-color-accent: #52F17E; 
*/

/* theme-c
$background-color: #191d24;
$text-color-primary: #ffffff;
$text-color-accent: #52f17e;
*/

/* theme-d
$background-color: #2B80F6; 
$text-color-primary: #ffffff; 
$text-color-accent: #1B2738; 
*/
