  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 200;
	src: local('Nunito Sans ExtraLight Italic'), local('NunitoSans-ExtraLightItalic'), url(NunitoSans-ExtraLightItalic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 300;
	src: local('Nunito Sans Light Italic'), local('NunitoSans-LightItalic'), url(NunitoSans-LightItalic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 400;
	src: local('Nunito Sans Italic'), local('NunitoSans-Italic'), url(NunitoSans-Italic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 600;
	src: local('Nunito Sans SemiBold Italic'), local('NunitoSans-SemiBoldItalic'), url(NunitoSans-SemiBoldItalic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 700;
	src: local('Nunito Sans Bold Italic'), local('NunitoSans-BoldItalic'), url(NunitoSans-BoldItalic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 800;
	src: local('Nunito Sans ExtraBold Italic'), local('NunitoSans-ExtraBoldItalic'), url(NunitoSans-ExtraBoldItalic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: italic;
	font-weight: 900;
	src: local('Nunito Sans Black Italic'), local('NunitoSans-BlackItalic'), url(NunitoSans-BlackItalic.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 200;
	src: local('Nunito Sans ExtraLight'), local('NunitoSans-ExtraLight'), url(NunitoSans-ExtraLight.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 300;
	src: local('Nunito Sans Light'), local('NunitoSans-Light'), url(NunitoSans-Light.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 400;
	src: local('Nunito Sans Regular'), local('NunitoSans-Regular'), url(NunitoSans-Regular.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 600;
	src: local('Nunito Sans SemiBold'), local('NunitoSans-SemiBold'), url(NunitoSans-SemiBold.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 700;
	src: local('Nunito Sans Bold'), local('NunitoSans-Bold'), url(NunitoSans-Bold.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 800;
	src: local('Nunito Sans ExtraBold'), local('NunitoSans-ExtraBold'), url(NunitoSans-ExtraBold.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* latin */
  @font-face {
	font-family: 'Nunito Sans';
	font-style: normal;
	font-weight: 900;
	src: local('Nunito Sans Black'), local('NunitoSans-Black'), url(NunitoSans-Black.woff2) format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }