<template>
  <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
    <div class="page-header">
      <h1>Weight Classes</h1>
      <!-- <h4>simple table</h4> -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
        <el-breadcrumb-item>Weight Classes</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="flex m-2">
      <div class="col-10"></div>
      <div class="col-2 flex justify-flex-end">
        <el-button class="btn-blue-bg" @click="dialogFormVisible = true;clearForm()">Add</el-button>
      </div>
    </div>
    <div class="toolbar-box flex align-center">
      <div class="box grow">
        <!-- <el-input placeholder="Search..." v-model="search" clearable></el-input> -->
      </div>
    </div>

    <resize-observer @notify="handleResize"/>

    <div class="table-box card-base card-shadow--medium box grow" id="table-wrapper" v-loading="!ready">
      <el-table
          v-loading="loading"
          :data="listInPage"
          style="width: 100%"
          :height="height"
          v-if="ready"
          @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
        <el-table-column label="ID" min-width="90" prop="id" :fixed="!isMobile">
          <template #default="scope">
            <span class="sel-string" v-html="$options.filters.selected(scope.$index + 1, search)"></span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="Weight Class code" prop="weight_class_code" min-width="100"></el-table-column> -->
        <el-table-column label="Min Weight" prop="min_weight" min-width="100">
          <template #default="scope">
            <span class="sel-string">{{ Number(scope.row.min_weight).toFixed(3)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="Max Weight" prop="max_weight" min-width="100">
          <template #default="scope">
            <span class="sel-string">{{ Number(scope.row.max_weight).toFixed(3)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="Unit" prop="unit" min-width="100"></el-table-column>
        <!-- <el-table-column label="Type" prop="pick_type" min-width="100"></el-table-column> -->
        <el-table-column label="Action">
          <template #default="scope">
            <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>
            <el-dropdown trigger="hover" @command="onCommand">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical pointer"></i>
                            </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="editFormVisible = true; setWeightClass(scope.row); getWeightClassesList()"
                                    divided>
                    Edit
                  </el-dropdown-item>
<!--                  <el-dropdown-item @click="deleteWeightClass(scope.row.weight_class_code)" :command="'skip'" divided>
                    Delete
                  </el-dropdown-item>-->
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
          ref="pagin"
          v-if="ready"
          :small="pagination.small"
          v-model:current-page="pagination.page"
          :page-sizes="pagination.sizes"
          v-model:page-size="pagination.size"
          :layout="pagination.layout"
          :total="total"
      ></el-pagination>
    </div>

    <el-dialog title="" v-model="dialogFormVisible" class="p-0">

      <div class="card-base card-shadow&#45;&#45;medium search-card scrollable only-y mt-2 p-10">
        <el-form ref="form" :model="form" class="mb-50 bb-br-gray" label-width="125px">
          <h4 class="ml-12">Weight Class</h4>
          <el-col class="demo-form-inline flex" :span="24">

            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Min Weight">
                <el-input type="number" @input="inputMinWeight" v-model="form.min_weight" placeholder=""></el-input>
              </el-form-item>
            </el-col>

            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Max Weight">
                <el-input type="number"
                          @input="inputMaxWeight"
                          v-model="form.max_weight" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>

          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Unit">
                <el-select
                    v-model="form.unit"
                    filterable
                    placeholder=""
                    :loading="loading"
                >
                  <el-option
                      value="KG"
                  />
                </el-select>
              </el-form-item>
            </el-col>

          </el-col>
          <div class="bt-br"></div>
          <el-form-item v-if="ready" class="mt-30 dialog-footer">
            <el-button class="btn-blue-bg" @click="onSubmit" v-loading.fullscreen.lock="fullscreenLoading">Save
            </el-button>
            <el-button class="btn" @click="closeModal()">Cancel</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-dialog>

    <!-- Majid Add Modal For Edit -->
    <el-dialog title="" v-model="editFormVisible" class="p-0">
      <div class="card-base card-shadow&#45;&#45;medium search-card scrollable only-y mt-2 p-10">
        <el-form ref="form" :model="form" class="mb-50 bb-br-gray" label-width="125px">
          <h4 class="ml-12">Carrier Product</h4>
          <el-col class="demo-form-inline flex" :span="24">

            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Min Weight">
                <el-input type="text"
                          @input="inputMinWeight"
                          v-model="form.min_weight" placeholder=""></el-input>
              </el-form-item>
            </el-col>

            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Max Weight">
                <el-input type="text"
                          @input="inputMaxWeight"
                          v-model="form.max_weight" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>

          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Unit">
                <el-select
                    v-model="form.unit"
                    filterable
                    placeholder=""
                    :loading="loading"
                >
                  <el-option
                      value="KG"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>

          <div class="bt-br"></div>
          <el-form-item v-if="ready" class="mt-30 dialog-footer">
            <el-button class="btn-blue-bg" @click="onEditSubmit" v-loading.fullscreen.lock="fullscreenLoading">Save
            </el-button>
            <el-button class="btn" @click="closeModal()">Cancel</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"

import {defineComponent} from "@vue/runtime-core"
import {AxiosError} from "axios"

import {
  weightClassesService,
  loadingService
  // loggingService,
} from "@/services/_singletons"

import {useMainStore} from "@/stores/main"

export default defineComponent({
  name: "WeightClassesList",

  data() {
    return {
      displayName: "WeightClassesList",
      isMobile: false,
      dialogFormVisible: false,
      editFormVisible: false,
      selectedRow: false,
      ready: false,
      width: 0,
      height: "auto",
      loading: false,
      search: "",
      pagination: {
        page: 1,
        size: 20,
        sizes: [10, 15, 20, 30, 50, 100],
        layout: "total, ->, prev, pager, next, jumper, sizes",
        small: false
      },
      list: [],
      editMode: false,
      itemsChecked: [],
      dialogUserVisible: false,
      currentId: 0,
      dayjs,
      checkAll: false,
      checkedOptions: [],
      weightList: [],
      pickUpList: [{
        id: 'PICK_UP',
        name: 'Pick Up'
      },
        {
          id: 'DROP_OFF',
          name: 'Drop Off'
        }],
      ckoptions: ["all", "Completed", "Pending"],
      isIndeterminate: true,
      fullscreenLoading: false,
      form: {
        min_weight: "",
        max_weight: "",
        unit: "KG",
      },
      fileList: [],
      file: null,
      content: [],
      parsed: false,
      showDropzone: true
    }
  },
  computed: {
    listFiltered() {
      return this.list.filter(obj => {
        let ctrl = false
        for (let k in obj) {
          if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
        }
        return ctrl
      })
    },
    listSorted() {
      let prop = this.sortingProp
      let order = this.sortingOrder
      return [].concat(
          this.listFiltered.sort((item1, item2) => {
            let val1 = ""
            let val2 = ""

            val1 = item1[prop]
            val2 = item2[prop]
            if (order === "descending") {
              return val2 < val1 ? -1 : 1
            }
            return val1 < val2 ? -1 : 1
          })
      )
    },
    listInPage() {
      let from = (this.currentPage - 1) * this.itemPerPage
      let to = from + this.itemPerPage * 1
      //return this.listSorted.slice(from, to)
      return this.listFiltered.slice(from, to)
    },
    total() {
      return this.listFiltered.length
    },
    currentPage: {
      get() {
        return this.pagination.page
      },
      set(val) {
        this.pagination.page = val
      }
    },
    itemPerPage() {
      return this.pagination.size
    },
    selectedItems() {
      return this.itemsChecked.length || 0
    }
  },
  watch: {
    itemPerPage(val) {
      this.ready = false
      this.currentPage = 1

      setTimeout(() => {
        this.ready = true
      }, 500)
    },
    search(val) {
      this.currentPage = 1
    }
  },
  methods: {
    inputMinWeight() {
      let min_weight = String(this.form.min_weight);
      this.form.min_weight = this.modifyFraction(min_weight)
    }, inputMaxWeight() {
      let max_weight = String(this.form.max_weight);
      this.form.max_weight = this.modifyFraction(max_weight)
    },
    modifyFraction(value) {
      const arr = value.split(".");
      let val = value;
      if (arr.length > 1 && arr[1].length > 3) {
        const after_decimal = arr[1].substring(0, 3)
        const before_decimal = arr[0];
        val = before_decimal + "." + after_decimal;
      }
      return val;
    },
    setWeightClass(item) {
      this.form = item;
      // console.log(this.form);
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.options.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
    },
    clearForm() {
      this.form.min_weight = ''
      this.form.max_weight = ''
      this.form.unit = 'KG'
    },
    async closeModal() {
      this.dialogFormVisible = false
      this.editFormVisible = false
      this.loading = true
      await this.getWeightClassesList()
      this.loading = false
      // this.clearForm()
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handleStart(file) {
      console.log('file', file, file.raw)
      this.file = file.raw
    },
    handlePreview(file) {
      this.file = file
    },
    handleExceed(files, fileList) {
      this.$message.warning(
          "The limit is 3, you selected" +
          files.length +
          " files this time, add up to " +
          files.length +
          fileList.length +
          "totally"
      )
    },
    beforeRemove(file, fileList) {
      return this.$confirm(file.name + "?")
    },
    handleUpload(file, fileList) {
      this.file = file.target.files[0]
      console.log('fileList', file.target.files[0], fileList)
      // return this.$confirm(file.name + "?")
    },

    async deleteWeightClass(id) {
      try {
        await this.$confirm("Your are sure to delete Weight Class. Continue?", "Warning", {
          confirmButtonText: "OK",
          cancelButtonText: "Cancel",
          type: "warning",
          center: true
        })
            .then(async () => {
              const res = await weightClassesService.deleteWeightClass({weight_class_code: id})
              this.$notify({
                title: "Success",
                message: res.message,
                type: "success"
              })
              this.list = this.list.filter(el => el.weight_class_code !== id)
            })
            .catch(() => {
            })
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: "Fail to Delete",
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: "Error getting Ubsend List",
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async onEditSubmit(values) {
      try {
        this.fullscreenLoading = true;
        const formData = new FormData();
        Object.entries(this.form).forEach(([key, value]) => {
          formData.append(key, value);
        });
        // loadingService.startLoading('main-loader:login');
        if (this.selectedRow === true) {
          this.update()
          return
        }
        const res = await weightClassesService.updateWeightClass(formData);
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        this.dialogFormVisible = false
        this.editFormVisible = false
        this.clearForm()
        await this.getWeightClassesList()
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.message,
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async onSubmit(values) {
      // display form values on success
      try {
        this.fullscreenLoading = true;
        const formData = new FormData();
        Object.entries(this.form).forEach(([key, value]) => {
          formData.append(key, value);
        });
        // loadingService.startLoading('main-loader:login');
        if (this.selectedRow === true) {
          this.update()
          return
        }
        const res = await weightClassesService.addWeightClass(formData);
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        this.dialogFormVisible = false
        this.editFormVisible = false
        this.clearForm()
        await this.getWeightClassesList()
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.message,
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    onCommandLang(lang) {
      if (lang.charAt(0) === "/") this.onCommand(lang)
      else this.lang = lang
    },
    onCommand(path) {
      if (path !== "skip") this.$router.push(path)
    },
    calcDims() {
      const tableWrapper = document.getElementById("table-wrapper")
      if (tableWrapper) this.width = tableWrapper.clientWidth

      if (!this.isMobile && tableWrapper) {
        this.height = tableWrapper.clientHeight - 44
      }

      if (this.width < 480) {
        this.pagination.small = true
        this.pagination.layout = "prev, pager, next"
      } else if (this.width >= 480 && this.width < 700) {
        this.pagination.small = false
        this.pagination.layout = "prev, pager, next, ->, sizes"
      } else {
        this.pagination.small = false
        this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
      }

      this.ready = true
    },
    handleResize: _.throttle(function (e) {
      this.ready = false
      this.width = 0
      setTimeout(this.calcDims, 1000)
    }, 500),
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      if (window.innerWidth <= 768) this.isMobile = true
    },
    async getWeightClassesList() {
      // display form values on success
      try {
        const res = await weightClassesService.getWeightClassesList()

        console.log(res);
        this.list = res.data.weight_list.map((item) => {
          return {
            id: item.id,
            weight_class_code: item.weight_class_code,
            min_weight: item.min_weight,
            max_weight: item.max_weight,
            unit: item.unit,
          }
        });

      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    }
  },
  filters: {
    selected: function (value, sel) {
      if (!value) return ""
      if (!sel) return value

      value = value.toString()
      sel = sel.toString()

      const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
      if (startIndex !== -1) {
        const endLength = sel.length
        const matchingString = value.substr(startIndex, endLength)
        return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
      }
      //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
      return value
    }
  },
  created() {
    this.init()
    this.getWeightClassesList()
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt

    this.calcDims()
  },
  components: {ResizeObserver}
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.el-dialog__body {
  padding: 0px;
}

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}
</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.el-pagination {
  .el-pagination__rightwrapper {
    display: flex;
  }
}

.pointer {
  cursor: pointer;
}

.bt-br-gray {
  border-top: 1px solid #ebeef5;
}

</style>
