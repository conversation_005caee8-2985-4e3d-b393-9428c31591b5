<template>
  <div class="login-page flex">
    <div class="box grow scrollable side-box box-left">
      <div class="align-vertical-middle wrapper">
        <img class="image-logo" src="@/assets/images/logo.svg" alt="logo-left"/>
        <!-- <h1 class="h-big">{{appName}}</h1> -->
<!--        <p class="p-50 text-white">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus ullamcorper nisl erat, vel
          convallis elit fermentum pellentesque. Sed mollis velit facilisis facilisis viverra.
        </p>-->
      </div>
    </div>
    <div class="box grow scrollable align-vertical side-box box-right body-blue">
      <div class="align-vertical-middle wrapper">
        <Form class="form-box" @submit="onSubmit" :validation-schema="schema">
          <h2>New Password</h2>
          <p class="mb-40">Please set your new password</p>

          <div class="field-box">
            <Field type="password" name="password" ref="password" placeholder="New Password" v-model="password"/>
            <ErrorMessage name="password" class="invalid-feedback"/>
          </div>
          <div class="field-box">
            <Field type="password" name="passwordConfirmation" placeholder="Confirm Password"
                   v-model="confirmation_password"/>
            <ErrorMessage name="passwordConfirmation" class="invalid-feedback"/>
          </div>

          <div>
            <button class="login-btn" type="submit">Submit</button>
          </div>
        </Form>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent} from "@vue/runtime-core"
import {useMainStore} from "../../../stores/main"
import {Form, Field, ErrorMessage} from 'vee-validate';
import * as Yup from 'yup';
import {
  authService,
  loadingService
} from "../../../services/_singletons"

const schema = Yup.object().shape({

  password: Yup.string()
      .min(8, 'Password must be at least 8 characters')
      .required('Password is required'),
  passwordConfirmation: Yup.string()
      .oneOf([Yup.ref('password'), null], 'Passwords must match')
});

export default defineComponent({
  name: "New Password",
  components: {
    Form,
    Field,
    ErrorMessage
  },
  data() {
    return {
      confirmation_password: "",
      password: "",
      appName: import.meta.env.VITE_APP_TITLE,
      token: null,
      schema
    }
  },
  methods: {
    async onSubmit(values) {
      // display form values on success
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      try {
        const otp = this.token;
        const res = await authService.resetPassword(this.password, otp)
        loading.close();
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        useMainStore().setLogin()
        window.location.href = '/login';
      } catch (e) {
        loading.close();
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async verifyOtp() {
      const res = await authService.verifyOtp(this.token)
      this.$notify({
        title: "Success",
        message: res.message,
        type: "success"
      })
    }
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search);
    this.token = urlParams.get('token');
    if (this.token) {
      this.verifyOtp()
    }
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables.scss";

.login-page {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0 !important;

  .side-box {
    .wrapper {
      width: 100%;
      box-sizing: border-box;
      padding: 10px;
    }
  }

  .box-left {
    background-image: url("../../../assets/images/login2.png");
    background-size: cover;
    background-position: 50% 50%;

    .wrapper {
      .image-logo {
        width: 90%;
        max-width: 20rem;
        margin-top: 10px;
      }
    }
  }

  .box-right {
    background: white;
  }

  .form-box {
    width: 100%;
    max-width: 340px;
    padding: 30px;
    box-sizing: border-box;
    margin: 20px auto;
    //background: transparent;

    a {
      font-size: 14px;
      color: $text-color-white;
      text-decoration: none;
      font-weight: 500;
    }

    .image-logo {
      width: 80px;
      margin: 0px auto;
      margin-bottom: 70px;
      display: block;
    }

    .field-box {
      margin-bottom: 10px;

      input {
        width: 100%;
        box-sizing: border-box;
        border-radius: 10px;
        padding: 10px 20px;
        outline: none;
        border: 1px solid $text-color-white;
      }
    }

    .login-btn {
      margin-top: 20px;
      width: 50%;
      background-color: $text-color-accent;
      color: #fff;
      font-weight: bold;
      border-radius: 10px;
      padding: 10px 20px;
      outline: none;
      border: none;
    }
  }
}

@media (max-width: 1200px) {
  .login-page {
    .box-left {
      .wrapper {
        .h-big {
          font-size: 50px;
        }
      }
    }
  }
}

@media (max-width: 900px) {
  .login-page {
    .box-left {
      .wrapper {
        .h-big {
          font-size: 30px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .login-page {
    display: block;
    overflow: auto;

    .side-box {
      display: block;
    }
  }
}

.field-box input {
  font-family: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 0 !important;
  font-size: 16px;
  color: #fff;
  border-radius: 0px !important;
  background: transparent;
  border-bottom: 1px solid #aaaaaa !important;
}

::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: white;
  opacity: 1; /* Firefox */
}

.field-box input[type="text"],
input[type="password"] {
  width: 100%;
  height: 40px;
  border-bottom: 1px solid #aaaaaa !important;
}

button,
input:focus {
  outline: 0;
}

::-webkit-input-placeholder {
  font-size: 16px;
  font-weight: 300;
  letter-spacing: -0.00933333em;
}

label {
  opacity: 1;
  -webkit-transform: translateY(5px);
  transform: translateY(5px);
  color: #aaa;
  font-weight: 300;
  font-size: 13px;
  letter-spacing: -0.00933333em;
  transition: all 0.2s ease-out;
}

input:placeholder-shown + label {
  opacity: 0;
  -webkit-transform: translateY(15px);
  transform: translateY(15px);
}

.checkbox-container {
  display: flex;
  margin-top: 35px;
}

.text-checkbox {
  color: #aaa;
  font-size: 16px;
  letter-spacing: -0.00933333em;
  font-weight: 300;
  margin-left: 15px;
}

input[type="checkbox"] {
  cursor: pointer;
  margin: 0;
  height: 22px;
  position: relative;
  width: 22px;
  -webkit-appearance: none;
  transition: all 180ms linear;
}

input[type="checkbox"]:before {
  border: 1px solid #aaa;
  background-color: #fff;
  content: "";
  width: 20px;
  height: 20px;
  display: block;
  border-radius: 2px;
  transition: all 180ms linear;
}

input[type="checkbox"]:checked:before {
  background: linear-gradient(198.08deg, #22bf64 45.34%, #e281e7 224.21%);
  border: 1px solid #22bf64;
}

input[type="checkbox"]:after {
  content: "";
  border: 2px solid #fff;
  border-right: 0;
  border-top: 0;
  display: block;
  height: 4px;
  left: 4px;
  opacity: 0;
  position: absolute;
  top: 6px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: 12px;
  transition: all 180ms linear;
}

input[type="checkbox"]:checked:after {
  opacity: 1;
}

.text-white {
  color: white;
}
</style>
