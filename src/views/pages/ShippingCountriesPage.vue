<template>
    <div class="scrollable only-y p-2">
        <div class="toolbar-box flex align-center">
            <div class="page-header">
                <h1 class="ml-2">Shipping Countries For Additional Services Pricing</h1>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                    <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
                    <el-breadcrumb-item :to="{ path: '/shipping-countries' }">Shipping Countries For Additional Services
                        Pricing
                    </el-breadcrumb-item>
                </el-breadcrumb>
            </div>

        </div>
        <div class="flex">
            <div class="col-10"></div>
            <div class="col-2 flex justify-flex-end">
                <el-button class="btn-blue-bg"
                           :disabled="countriesLoading"
                           @click="onClickAdd()">Add
                </el-button>
            </div>
        </div>
        <div class="mt-3 table-box card-base card-shadow--medium box grow"
             id="table-wrapper" v-loading="loadingShippingCountries">
            <el-table
                    :data="list"
                    style="width: 100%"
                    height="auto"
                    v-if="ready"
            >
                <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
                <el-table-column label="ID" min-width="100" prop="id" fixed>
                    <template #default="scope">
                        <span class="sel-string">{{scope.$index + 1}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="Origin Country" prop="origin" min-width="150">
                    <template #default="scope">
                        {{scope.row.origin.name}}
                    </template>
                </el-table-column>
                <el-table-column label="Receiver Country" prop="receiver_country" min-width="150">
                    <template #default="scope">
                        {{scope.row.receiver_country.name}}
                    </template>
                </el-table-column>
                <el-table-column label="Status" prop="is_active" min-width="100">
                    <template #default="scope">
                        <el-switch
                                @change="updateShippingCountryStatus(scope.row)"
                                :active-value="1"
                                :inactive-value="0"
                                v-model="scope.row.is_active"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column label="Action">
                    <template #default="scope">
                        <el-dropdown trigger="hover">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                            @click="onClickEditShippingProduct(scope.row)"
                                            divided
                                    >
                                        Edit
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                            v-loading.fullscreen.lock="fullscreenLoading"
                                            @click="onClickDeleteShippingProduct(scope.row.id)"
                                            divided>
                                        Delete
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :small="small"
                    v-model:current-page="current_page"
                    :page-sizes="[5, 10, 15, 20, 30, 50, 100]"
                    v-model:page-size="per_page"
                    :layout="layout"
                    :total="total"
            ></el-pagination>
        </div>

        <el-dialog
                width="50%"
                title="" v-model="shippingCountryFormDialog" class="p-0">

            <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">
                <el-form ref="form" class="mb-50 bb-br-gray" label-width="120px">
                    <h4 class="ml-12">Shipping Countries For Additional Services Pricing</h4>
                    <el-col class="demo-form-inline flex" :span="24">
                        <el-col class="demo-form-inline" :span="24">
                            <el-form-item
                                    label-width="250px"
                                    label="Origin Country" required
                                    class="asterisk-right">
                                <el-select
                                        disabled
                                        v-model="origin"
                                        filterable
                                        placeholder=""
                                        :loading="countriesLoading">
                                    <el-option
                                            v-for="(item,index) in countries"
                                            :key="`origin_${item.country_code}`"
                                            :label="item.name"
                                            :value="item.country_code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-col>

                    <el-col class="demo-form-inline flex" :span="24">
                        <el-col class="demo-form-inline" :span="24">
                            <el-form-item
                                    label-width="250px"
                                    label="Select Receiver Country" required
                                    class="asterisk-right">
                                <el-select
                                        v-model="receiver_country"
                                        filterable
                                        :loading="countriesLoading">
                                    <el-option
                                            v-for="(item,index) in countries"
                                            :key="`receiver_${item.country_code}_${index}`"
                                            :label="item.name"
                                            :value="item.country_code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                    </el-col>

                    <el-col class="demo-form-inline flex" :span="24">
                        <el-col class="demo-form-inline" :span="24">
                            <el-form-item label-width="250px" label="Status">
                                <el-switch v-model="is_active"></el-switch>
                            </el-form-item>
                        </el-col>
                    </el-col>

                    <div class="bt-br"></div>
                    <el-form-item v-if="ready" class="mt-30 dialog-footer">
                        <el-button class="btn-blue-bg" @click="saveShippingCountry"
                                   v-loading.fullscreen.lock="fullscreenLoading">
                            {{ isEdit ? 'Update' : 'Save' }}
                        </el-button>
                        <el-button class="btn" @click="hideShippingCountryDialog">Cancel</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-dialog>

    </div>
</template>

<script setup>
    import {ref, onBeforeMount, onMounted} from 'vue';
    import {merchantService, shippingCountryService} from '../../services/_singletons'
    import {ElNotification} from 'element-plus'

    /** this related to pagination **/
    const ready = ref(false);
    const current_page = ref(1);
    const per_page = ref(10);
    const layout = ref('total, ->, prev, pager, next, jumper, sizes');
    const small = ref(false);
    const total = ref(10);

    const list = ref([]);
    const shippingCountryFormDialog = ref(false);

    const origin = ref(3421137111);
    const receiver_country = ref(null);
    const is_active = ref(true);

    const countries = ref([]);
    const countriesLoading = ref(false);
    const fullscreenLoading = ref(false);
    const isEdit = ref(false);
    const loadingShippingCountries = ref(false);
    const handleSizeChange = (size) => {
        per_page.value = size;
        getShippingCountriesList();
    }
    const changeCurrentPage = (page) => {
        current_page.value = page;
        getShippingCountriesList();
    }
    const selectedId = ref(null);


    const getCountries = async () => {
        countriesLoading.value = true
        const res = await merchantService.getCountryList().finally(() => countriesLoading.value = false)
        countries.value = res.data.countries
    }
    const onClickAdd = () => {
        isEdit.value = false;
        shippingCountryFormDialog.value = true;
        selectedId.value = null;
        receiver_country.value = null;
        is_active.value = true;
    }
    const hideShippingCountryDialog = () => {
        isEdit.value = false;
        selectedId.value = null;
        shippingCountryFormDialog.value = false;
    }
    const mapShippingCountries = (data) => {
        list.value = data.map((item) => {
            return {
                id: item.id,
                is_active: item.is_active,
                origin: countries.value.find((country) => country.country_code == item.origin),
                receiver_country: countries.value.find((country) => country.country_code == item.receiver_country),
            }
        })
    }
    const getShippingCountriesList = async () => {
        const payload = {
            per_page: per_page.value,
            current_page: current_page.value,
            total: 10,
        };
        loadingShippingCountries.value = true;
        const response = await shippingCountryService.getShippingCountries(payload).finally(() => {
            ready.value = true;
            loadingShippingCountries.value = false
        })
        // list.value = response.data;
        mapShippingCountries(response.data)
        per_page.value = response.per_page;
        current_page.value = response.current_page;
        total.value = response.total
    }
    const updateShippingCountryStatus = (item) => {
        setShippingCountry(item);
        isEdit.value = true;
        saveShippingCountry();
    }
    const saveShippingCountry = async () => {
        if (origin.value == null || origin.value == '') {
            ElNotification.error({
                title: 'Error',
                message: 'Origin country not selected.',
            })
            return;
        }
        if (receiver_country.value == null || receiver_country.value == '') {
            ElNotification.error({
                title: 'Error',
                message: 'Receiver country not selected.',
            })
            return;
        }
        fullscreenLoading.value = true
        try {
            const payload = {
                origin: origin.value,
                receiver_country: receiver_country.value,
                is_active: is_active.value ? 1 : 0
            }
            if (isEdit.value) {
                payload.id = selectedId.value;
                await shippingCountryService.updateShippingCountry(payload, selectedId.value);
            } else {
                await shippingCountryService.addShippingCountry(payload)
            }
            ElNotification.success({
                title: 'Success',
                message: `Shipping Countries For Additional Services Pricing saved successfully.`,
            })
            await getShippingCountriesList();
            hideShippingCountryDialog();
        } catch (error) {
            if (error.response.data) {
                ElNotification.error({
                    title: 'Error',
                    message: error.response.data.error[0],
                })
            }
        } finally {
            fullscreenLoading.value = false
        }
        /* await shippingCountryService.addShippingCountry(payload).then(response => {
             ElNotification.success({
                 title: 'Success',
                 message: 'Shipping country saved successfully.',
             })

         }).catch(error => {
             ElNotification.error({
                 title: 'Error',
                 message: error.response.data.error[0],
             })
         }).finally(() => fullscreenLoading.value = false)*/
    }

    const setShippingCountry = (item) => {
        receiver_country.value = parseInt(item.receiver_country.country_code);
        is_active.value = item.is_active == 1 ? true : false;
        selectedId.value = item.id;
    }
    const onClickEditShippingProduct = (item) => {
        console.log("Item ", item)
        setShippingCountry(item);
        isEdit.value = true;
        shippingCountryFormDialog.value = true
    }
    const onClickDeleteShippingProduct = async (id) => {
        fullscreenLoading.value = true;
        await shippingCountryService.deleteShippingCountry(id).then(async response => {
            ElNotification.success({
                title: 'Success',
                message: 'Shipping Countries For Additional Services Pricing deleted successfully.',
            })
            await getShippingCountriesList();
        }).catch(error => {
            ElNotification.error({
                title: 'Error',
                message: error.response.data.error[0],
            })
        }).finally(() => {
            fullscreenLoading.value = false;
        })
    }

    const getAllData = async () => {
        await getCountries();
        await getShippingCountriesList()
    }
    onBeforeMount(() => {
        getAllData()
    })
</script>
<style lang="scss" scoped>
    @import "../../assets/scss/_variables";

    .el-dialog__body {
        padding: 0px;
    }

    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }
</style>

<style lang="scss">
    @import "../../assets/scss/_variables";

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }

    .el-pagination {
        .el-pagination__rightwrapper {
            display: flex;
        }
    }

    .bt-br-gray {
        border-top: 1px solid #ebeef5;
    }

    .el-form-item__label:before {
        content: "" !important;
    }

    .page-header {
        margin-bottom: 10px;
    }
</style>
