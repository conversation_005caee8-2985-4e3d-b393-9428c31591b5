<template>
    <div class="not-found">
        <!--<div class="msg-box">
          <h1 class="animate__animated animate__fadeInDown">403 Access Forbidden</h1>
          <h2 class="animate__animated animate__fadeInUp">Oops!<br/>It seems that you have not access rights.</h2>
        </div>-->
        <img src="@/assets/images/Logo2.png"
             alt="logo"/>
    </div>
</template>

<script>
    import {defineComponent} from "@vue/runtime-core"

    export default defineComponent({
        name: "403"
    })
</script>

<style lang="scss" scoped>
    @import "../../assets/scss/_variables";
    @import "../../assets/scss/_mixins";

    .msg-box {
        max-width: 240px;
        margin: 50px auto;

        h1 {
            @include text-bordered-shadow();
        }
    }

    .not-found {
        width: 100%;
        text-align: center;
    }
</style>
