<template>
    <div class="scrollable only-y p-2">
        <div class="toolbar-box flex align-center">
            <div class="page-header">
                <h1 class="ml-2">Balance Report</h1>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                    <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
                    <el-breadcrumb-item :to="{ path: '/reports/balance' }">Balance Report</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>
        <div class="flex justify-space-between">
            <div class="col-4">
                <div class="search-container">
                    <search-box @search=""></search-box>
                </div>
            </div>
            <div class="col-5 flex justify-flex-end">
                <el-date-picker
                        class="mr-5"
                        v-model="selectedDate"
                        :clearable="false"
                        type="date"
                        @change="onChangeDate"
                        placeholder="Select Date"
                        format="DD-MM-YYYY"
                />
                <el-dropdown split-button type="primary">
                    Export As
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item>Export as Excel</el-dropdown-item>
                            <el-dropdown-item>Export as CSV</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>
        <div class="mt-3 table-box card-base card-shadow--medium box grow"
             id="table-wrapper" v-loading="loadingBalanceReport">
            <el-table
                    :data="list"
                    style="width: 100%"
                    height="auto"
                    v-if="ready"
            >
                <el-table-column label="ID" min-width="100" prop="id" fixed>
                    <template #default="scope">
                        <span class="sel-string">{{scope.$index + 1}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="Origin Country" prop="origin" min-width="150">
                    <template #default="scope">
                        {{scope.row.origin.name}}
                    </template>
                </el-table-column>
                <el-table-column label="Receiver Country" prop="receiver_country" min-width="150">
                    <template #default="scope">
                        {{scope.row.receiver_country.name}}
                    </template>
                </el-table-column>
                <el-table-column label="Status" prop="is_active" min-width="100">
                    <template #default="scope">
                        <el-switch
                                @change="updateShippingCountryStatus(scope.row)"
                                :active-value="1"
                                :inactive-value="0"
                                v-model="scope.row.is_active"></el-switch>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :small="false"
                    v-model:current-page="pagination.current_page"
                    :page-sizes="[5, 10, 15, 20, 30, 50, 100]"
                    v-model:page-size="per_page"
                    layout="total, ->, prev, pager, next, jumper, sizes"
                    :total="pagination.total"
            ></el-pagination>
        </div>

    </div>
</template>

<script setup>
    import {ref, reactive} from 'vue';
    import moment from "moment";

    const date = new Date();
    const pagination = reactive({
        current_page: 1,
        total: 10,
        per_page: 10
    })
    const loadingBalanceReport = ref(false);
    const selectedDate = ref(moment(new Date(date.getFullYear(), date.getMonth(), 1)).format('YYYY-MM-DD'))
</script>

<style scoped>

</style>