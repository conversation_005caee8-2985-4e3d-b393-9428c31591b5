<template>
  <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
    <div class="page-header">
      <h1>Video</h1>
      <!-- <h4>simple table</h4> -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
        <el-breadcrumb-item>Videos</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="flex m-2">
      <div class="col-4"></div>
      <div class="col-4"></div>
      <div class="col-2"></div>
      <div class="col-2 flex justify-flex-end">
        <el-button class="btn-blue-bg" @click="dialogFormVisible = true">Add</el-button>
      </div>
    </div>
    <div class="toolbar-box flex align-center">
      <div class="box grow">
        <!-- <el-input placeholder="Search..." v-model="search" clearable></el-input> -->
      </div>
    </div>

    <resize-observer @notify="handleResize"/>

    <div class="table-box card-base card-shadow--medium box grow" id="table-wrapper" v-loading="!ready">
      <el-table
          :data="listInPage"
          style="width: 100%"
          :height="height"
          v-if="ready"
          @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
        <el-table-column label="ID" min-width="30" prop="id" :fixed="!isMobile">
          <template #default="scope">
            <span class="sel-string" v-html="$options.filters.selected(scope.$index + 1, search)"></span>
          </template>
        </el-table-column>
        <el-table-column label="Video Name" prop="name" min-width="100"></el-table-column>
        <el-table-column label="Video Thumbnail" prop="video_thumbnail" min-width="100">
          <template #default="scope">
            <img
                alt="Video Image"
                v-if="scope.row.video_thumbnail !==''" :src="scope.row.video_thumbnail"
                :id="scope.row.id"
                style="width:30px;height:30px;border-radius: 30px;"/>
          </template>
        </el-table-column>
        <el-table-column label="Link" prop="url" min-width="170"></el-table-column>
        <el-table-column label="Status" prop="is_active" min-width="70">
          <template #default="scope">
            <el-switch v-model="scope.row.is_active" @change="updateSelectedRow(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="Action">
          <template #default="scope">
            <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>
            <el-dropdown trigger="hover" @command="onCommand">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                      @click="setVideo(scope.row)"
                      :command="'skip'"
                      divided
                  >
                    Edit
                  </el-dropdown-item>
                  <el-dropdown-item @click="deleteVideo(scope.row.id)" :command="'skip'" divided>
                    Delete
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
          ref="pagin"
          v-if="ready"
          :small="pagination.small"
          v-model:current-page="pagination.page"
          :page-sizes="pagination.sizes"
          v-model:page-size="pagination.size"
          :layout="pagination.layout"
          :total="total"
      ></el-pagination>
    </div>

    <el-dialog title="" v-model="dialogFormVisible">
      <el-form :model="form">
        <div class="bt-br-gray">
          <div class="search-card scrollable only-y mt-2">
            <el-form ref="form" :model="form" label-width="120px">
              <!-- <Form class="form-box" @submit="onSubmit" :validation-schema="schema"> -->
              <el-col class="demo-form-inline flex" :span="24">
                <el-col class="demo-form-inline" :span="24">
                  <el-upload
                      :thumbnail-mode="true"
                      :auto-upload="false"
                      class="upload-demo"
                      :on-remove="handleRemove"
                      :before-remove="beforeRemove"
                      :on-change="handleStart"
                      @input="handleUpload"
                      drag
                      accept=".png,.jpeg,.jpg"
                      :limit="1"
                      :on-exceed="handleExceed"
                      :file-list="fileList"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">Drop file here or <em>click to upload</em></div>
                    <div class="el-upload__tip" slot="tip">The video thumbnail(png,jpg) must be at
                      least 1280 x 700
                      pixels.
                    </div>
                  </el-upload>
                </el-col>

              </el-col>
              <h4 class="ml-12">Video Link</h4>
              <el-col class="demo-form-inline flex" :span="24">
                <el-col class="demo-form-inline" :span="12">
                  <el-form-item label="Video Name">
                    <el-input v-model="form.name" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col class="demo-form-inline" :span="12">
                  <el-form-item label="Enter Link">
                    <el-input v-model="form.url" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
              </el-col>

              <el-col class="demo-form-inline" :span="24">
                <el-form-item label="Video Status">
                  <el-switch v-model="form.is_active"></el-switch>
                </el-form-item>
              </el-col>

              <div class="bt-br"></div>
              <el-form-item v-if="ready" class="mt-30 dialog-footer">
                <el-button class="btn-blue-bg" @click="onSubmit"
                           v-loading.fullscreen.lock="fullscreenLoading">
                  {{ selectedRow === true ? 'Update' : 'Save' }}
                </el-button>
                <el-button class="btn" @click="closeModal()">Cancel</el-button>
              </el-form-item>
            </el-form>

          </div>
        </div>

      </el-form>

    </el-dialog>
  </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"

import {defineComponent} from "@vue/runtime-core"
import {AxiosError} from "axios"
import {
  videoService,
  loadingService
  // loggingService,
} from "../../../services/_singletons"
import {useMainStore} from "@/stores/main"

export default defineComponent({
  name: "VideoList",
  data() {
    return {
      isMobile: false,
      dialogFormVisible: false,
      selectedRow: false,
      ready: false,
      width: 0,
      height: "auto",
      loading: false,
      fullscreenLoading: false,
      search: "",
      pagination: {
        page: 1,
        size: 20,
        sizes: [10, 15, 20, 30, 50, 100],
        layout: "total, ->, prev, pager, next, jumper, sizes",
        small: false
      },
      list: [],
      fileList: [],
      editMode: false,
      itemsChecked: [],
      dialogUserVisible: false,
      currentId: 0,
      dayjs,
      checkAll: false,
      checkedOptions: [],
      ckoptions: ["all", "Completed", "Pending"],
      isIndeterminate: true,
      form: {
        id: "",
        name: "",
        url: "",
        is_active: false,
        video_thumbnail: "",
      }
    }
  },
  computed: {
    listFiltered() {
      return this.list.filter(obj => {
        let ctrl = false
        for (let k in obj) {
          if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
        }
        return ctrl
      })
    },
    listSortered() {
      let prop = this.sortingProp
      let order = this.sortingOrder
      return [].concat(
          this.listFiltered.sort((item1, item2) => {
            let val1 = ""
            let val2 = ""

            val1 = item1[prop]
            val2 = item2[prop]
            if (order === "descending") {
              return val2 < val1 ? -1 : 1
            }
            return val1 < val2 ? -1 : 1
          })
      )
    },
    listInPage() {
      let from = (this.currentPage - 1) * this.itemPerPage
      let to = from + this.itemPerPage * 1
      //return this.listSortered.slice(from, to)
      return this.listFiltered.slice(from, to)
    },
    total() {
      return this.listFiltered.length
    },
    currentPage: {
      get() {
        return this.pagination.page
      },
      set(val) {
        this.pagination.page = val
      }
    },
    itemPerPage() {
      return this.pagination.size
    },
    selectedItems() {
      return this.itemsChecked.length || 0
    }
  },
  watch: {
    itemPerPage(val) {
      this.ready = false
      this.currentPage = 1

      setTimeout(() => {
        this.ready = true
      }, 500)
    },
    search(val) {
      this.currentPage = 1
    }
  },
  methods: {
    removeFile() {
      this.form.file = ''
      this.form.icon = ''
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handleStart(file) {
      if (!this.isValidFile(file.raw?.type.toLowerCase())) {
        this.file = null;
        return;
      }
      this.file = file.raw
    },
    handlePreview(file) {
      this.file = file
    },
    handleExceed(files, fileList) {
      this.$message.warning(
          "The limit is 3, you selected" +
          files.length +
          " files this time, add up to " +
          files.length +
          fileList.length +
          "totally"
      )
    },
    beforeRemove(file, fileList) {
      return this.$confirm(file.name + "?")
    },
    isValidFile(fileType) {
      let is_valid = false
      if (fileType.includes('jpeg') || fileType.includes('png') || fileType.includes('jpg')) {
        is_valid = true;
      }
      return is_valid;
    },
    handleUpload(file, fileList) {
      const item = file.target.files[0];
      if (!this.isValidFile(item.type.toLowerCase())) {
        this.$message.warning(
            "Kindly upload a valid file"
        )
        this.file = null;
        return;
      }
      this.file = item
      // return this.$confirm(file.name + "?")
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.options.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
    },
    setVideo(item) {
      this.dialogFormVisible = true
      this.selectedRow = true
      this.form.id = item.id
      this.form.name = item.name
      this.form.url = item.url
      this.form.is_active = item.is_active
    },
    updateSelectedRow(item) {
      this.selectedRow = true
      this.form.id = item.id
      this.form.name = item.name
      this.form.url = item.url
      this.form.is_active = item.is_active
      this.onSubmit()
    },
    clearForm() {
      this.form.id = ''
      this.file = ''
      this.form.name = ''
      this.form.url = ''
      this.form.video_thumbnail = ''
      this.form.is_active = false
      this.fileList = [];
    },
    closeModal() {
      this.dialogFormVisible = false
      this.clearForm()
    },
    async deleteVideo(id) {
      try {
        await this.$confirm("Your are sure to delete video. Continue?", "Warning", {
          confirmButtonText: "OK",
          cancelButtonText: "Cancel",
          type: "warning",
          center: true
        })
            .then(async () => {
              const res = await videoService.deleteVideo({id: id})
              this.$notify({
                title: "Success",
                message: res.message,
                type: "success"
              })
              this.list = this.list.filter(el => el.id !== id)
            })
            .catch(() => {
            })
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: "Fail to Delete",
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: "Error getting Ubsend List",
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async onSubmit(values) {
      // display form values on success
      try {

        this.form.video_thumbnail = this.file
        this.form.is_active === true ? 1 : 0
        let formData = new FormData()
        Object.entries(this.form).forEach(([key, value]) => {
          if (key === "is_active") {
            value = value === true ? 1 : 0
          }
          if (key === "video_thumbnail" && value === undefined) {
          } else {
            formData.append(key, value);
          }

        });
        this.fullscreenLoading = true;
        // loadingService.startLoading('main-loader:login');

        if (this.selectedRow === true) {
          this.update(formData)
          return
        }
        const res = await videoService.addVideo(formData)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        this.dialogFormVisible = false
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        this.clearForm()

        await this.getVideoList()
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async update(formData) {
      // display form values on success
      try {
        this.fullscreenLoading = true;
        // loadingService.startLoading('main-loader:login');
        const res = await videoService.updateVideo(formData)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        this.selectedRow = false
        await this.getVideoList()
        this.dialogFormVisible = false
        this.clearForm()
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: 'Error occurred',
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    onCommandLang(lang) {
      if (lang.charAt(0) === "/") this.onCommand(lang)
      else this.lang = lang
    },
    onCommand(path) {
      if (path !== "skip") this.$router.push(path)
    },
    calcDims() {
      const tableWrapper = document.getElementById("table-wrapper")
      if (tableWrapper) this.width = tableWrapper.clientWidth

      if (!this.isMobile && tableWrapper) {
        this.height = tableWrapper.clientHeight - 44
      }

      if (this.width < 480) {
        this.pagination.small = true
        this.pagination.layout = "prev, pager, next"
      } else if (this.width >= 480 && this.width < 700) {
        this.pagination.small = false
        this.pagination.layout = "prev, pager, next, ->, sizes"
      } else {
        this.pagination.small = false
        this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
      }

      this.ready = true
    },
    handleResize: _.throttle(function (e) {
      this.ready = false
      this.width = 0
      setTimeout(this.calcDims, 1000)
    }, 500),
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      if (window.innerWidth <= 768) this.isMobile = true
    },
    async getVideoList() {
      // display form values on success
      try {
        const res = await videoService.getVideoList()
        this.list = res.data.videos.map((item) => {
          return {
            id: item.id,
            name: item.name,
            url: item.url,
            is_active: item.is_enabled ? true : false,
            video_thumbnail: item.video_thumbnail
          }
        });
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }

        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    }
  },
  filters: {
    selected: function (value, sel) {
      if (!value) return ""
      if (!sel) return value

      value = value.toString()
      sel = sel.toString()

      const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
      if (startIndex !== -1) {
        const endLength = sel.length
        const matchingString = value.substr(startIndex, endLength)
        return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
      }
      //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
      return value
    }
  },
  created() {
    this.init()
    this.getVideoList()
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt

    this.calcDims()
  },
  components: {ResizeObserver}
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}
</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.el-pagination {
  .el-pagination__rightwrapper {
    display: flex;
  }
}

.bt-br-gray {
  border-top: 1px solid #ebeef5;
}

</style>
