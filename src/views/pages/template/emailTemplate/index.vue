<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Onboarding Email</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Onboarding Email</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <!-- <div class="flex m-2">
        <div v-if="showDropzone ===true" class="col-12" >
            handleStart
            <el-upload
                class="upload-demo"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :on-change="handleStart"
                @input="handleUpload"
                multiple
                drag
                accept=".csv"
                :limit="3"
                :on-exceed="handleExceed"
                :file-list="fileList"

                >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">Drop file here or <em>click to upload</em></div>
                    <div class="el-upload__tip" slot="tip">.csv files with a size less than 500kb</div>
                </el-upload>
                <div v-if="loadingUpload">
                    <el-form-item v-loading="ready"> </el-form-item>
                </div>
            </div>
        </div>
    <div class="row flex mt-3 mb-4">
      <div v-if="file" class="col-12">
        <i class="mdi mdi-clipboard-file"></i>
        {{ file.name }}
      </div>
      <div class="col-9"></div>
      <div v-if="file" class="col-1">
        <i class="mdi mdi-close-circle" @click="removeFile()"></i>
        <i :disabled="loadingUpload" class="mdi mdi-check-circle" @click="loadFile()"></i>
      </div>
    </div> -->
    <div class="card-base card-shadow--medium p-15">
      <div class="bb-bt-br">
        <div class="search-card scrollable only-y mt-2">
          <el-form ref="templateForm" :model="form" label-width="120px">
            <h4 class="ml-12">Onboarding Email</h4>
            <el-form-item label="Email Text">
              <vue-quill-editor
                  v-model="form.summary"
                  :content="form.summary"
                  contentType="html"
                  ref="myQuillEditor1"
                  theme="bubble"
              >
              </vue-quill-editor>
            </el-form-item>
            <el-form-item label="Video">
              <el-select v-model="video_id" @change="videoDetails" filterable placeholder="Select a Video"
                         style="width: 100%" :loading="loading">
                <el-option
                    v-for="video in videoList"
                    :key="video.id"
                    :label="video.name"
                    :value="video.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="ready" class="mt-30">
              <el-button class="btn-blue-bg" @click="onSubmit">Save</el-button>
              <el-button class="btn-blue-bg" @click="preview=true">Preview</el-button>
            </el-form-item>

          </el-form>

        </div>
      </div>
    </div>
    <EmailTemplate @close="close" v-if="preview" :preview="video_preview" :summary="form.summary"></EmailTemplate>
  </div>
</template>

<script>
import _, {find} from "lodash"
import dayjs from "dayjs"
import Affix from "@/components/Affix.vue"
import VueQuillEditor from "@/components/vue-quill-editor.vue"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
import {
  templateService,
  loadingService, videoService
} from "../../../../services/_singletons"
import {defineComponent} from "@vue/runtime-core"
import EmailTemplate from "@/components/previews/OnboardingTemplate.vue";

export default defineComponent({
  name: "Onboarding Email Summary",
  data() {
    return {
      video_id: null,
      videoList: [],
      preview: false,
      isMobile: false,
      ready: true,
      loading2: false,
      showDropzone: true,
      // dialogTableVisible: false,
      width: 0,
      height: "auto",
      loading: false,
      search: "",
      editMode: false,
      // itemsChecked: [],
      // merchantList: [],
      // carrierList: [],
      // priceGrouptList: [],
      // pricingList: [],
      // dialogUserVisible: false,
      currentId: 0,
      dayjs,
      form: {
        id: null,
        summary: "",
        type: "",
      },
      // gridData: [{
      //     date: '2016-05-02',
      //     name: 'John Smith',
      //     address: 'No.1518,  Jinshajiang Road, Putuo District'
      //     }, {
      //     date: '2016-05-04',
      //     name: 'John Smith',
      //     address: 'No.1518,  Jinshajiang Road, Putuo District'
      //     }, {
      //     date: '2016-05-01',
      //     name: 'John Smith',
      //     address: 'No.1518,  Jinshajiang Road, Putuo District'
      //     }, {
      //     date: '2016-05-03',
      //     name: 'John Smith',
      //     address: 'No.1518,  Jinshajiang Road, Putuo District'
      // }],
      formLabelWidth: '120px',
      video_preview: {
        video_thumbnail: '@/assets/images/play.png',
        video_url: '',
      }
    }
  },
  computed: {},
  watch: {},
  methods: {
    async getVideoList() {
      // display form values on success
      try {
        const res = await videoService.getVideoList([{key: 'is_enabled', value: 1}])
        this.videoList = res.data.videos.map((item) => {
          return {
            id: item.id,
            name: item.name,
            url: item.url,
            video_thumbnail: item.video_thumbnail
          }
        });
        const index=this.videoList.findIndex((item)=>item.id==this.form.email_video_link);
        if(index>-1){
          this.video_id = parseInt(this.form.email_video_link)
          await this.videoDetails();
        }
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }

        this.$notify.error({
          title: "Error",
          message: "An Error has ocurred",
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async videoDetails() {
      if (this.video_id) {
        const videoObject = find(this.videoList, {id: this.video_id});
        this.video_preview.video_url = videoObject.url
        this.video_preview.video_thumbnail = videoObject.video_thumbnail
      }
    },
    close(value) {
      this.preview = value
      console.log("Val ", value)
    },
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      if (window.innerWidth <= 768) this.isMobile = true
    },
    onCanel() {
      this.$refs.templateForm.resetFields()
      var self = this;
      Object.keys(this.form).forEach(function (key, index) {
        self.form[key] = '';
      });
    },
    async onSubmit(values) {
      if (this.video_id == null || this.video_id == '') {
        this.$notify.error({
          title: "Error",
          message: "Please select video",
        })
        return;
      }
      let video_link = this.video_id;
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');

        const res = await templateService.saveTemplate({
          id: this.form.id,
          summary: this.form.summary,
          email_video_link: video_link,
          // agreement_footer:this.form.agreement_footer,
          type: "EMAIL"
        })
        console.log(res);
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        await this.$router.push({path: "/template/create-email-template"})
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: "Merchant data failed to create",
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: "Error during creating record",
          position: "top-left"
        })
        // loggingService.error('Error during login', e);
        // notificationService.showError('Error during login');
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getTemplateSummary() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await templateService.getTemplateSummary({'type': 'EMAIL'})

        // console.log(res);

        if (res.data.template) {
          this.form = res.data.template
        }

      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        if (e.response?.status === 400) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-left"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        console.log(e)
        this.$notify.error({
          title: "Error",
          message: e,
          position: "top-left"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
  },
  async created() {
    await this.init()
    await this.getTemplateSummary()
    await this.getVideoList();
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt
  },
  components: {
    ResizeObserver,
    EmailTemplate,
    VueQuillEditor,
    Affix,
  }
})
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/variables";

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}

.bb-br-gray {
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-bg-gray {
  background: #151526 !important;
}

.el-collapse-item__header {
  background: #151526 !important;
}

.el-button {
  width: 95px;
  height: 41px;
  border-radius: 5px;
}

.btn-blue-bg {
  width: 95px;
  height: 41px;
  background: #006EFB;
  border-radius: 5px;
}

.quill-editor {
  width: 100% !important;
}

</style>

<style lang="scss">
@import "../../../../assets/scss/variables";


.el-button {
  height: 41px;
  border-radius: 5px;
}

.btn-blue-bg {
  height: 41px;
  background: #006EFB;
  border-radius: 5px;
}

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.full_width {
  width: 100%;
}
</style>
