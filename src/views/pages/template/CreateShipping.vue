<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Tracking Messages</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Tracking Messages Price
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="card-base card-shadow--medium p-15">
      <div class="bb-bt-br">
        <div class="search-card scrollable only-y mt-2">
          <el-form ref="templateForm" :model="form" label-width="175px">
            <!-- <h4 class="ml-12">Personalized Email</h4>
             <el-form-item label="Email Text">
                 <vue-quill-editor
                         v-model="form.summary"
                         :content="form.summary"
                         contentType="html"
                         ref="myQuillEditor1"
                         theme="bubble"
                 >
                 </vue-quill-editor>
             </el-form-item>-->
            <h4 class="ml-12">Prices:</h4>
            <el-form-item label="Tracking Email (DKK)"
                          required
                          size="default">
              <el-input
                  @input="(val)=>prices.personalize_email.price=val<0?0:prices.personalize_email.price"
                  min="0"
                  v-model="prices.personalize_email.price" type="number"
                  placeholder="Price in DKK"></el-input>
            </el-form-item>
            <!--<el-form-item label="Personalized SMS"
                          required
                          size="default">
                <el-input v-model="prices.personalize_sms.price" type="number"
                          placeholder="Price in DKK"></el-input>
            </el-form-item>
            <el-form-item label="SMS Notification"
                          required
                          size="default">
                <el-input v-model="prices.sms.price" type="number"
                          placeholder="Price in DKK"></el-input>
            </el-form-item>-->
            <el-form-item v-if="ready" class="mt-30">
              <el-button class="btn-blue-bg" @click="onSubmit">Save</el-button>
              <!-- <el-button class="btn-blue-bg" @click="preview=true">Preview</el-button>-->
            </el-form-item>

          </el-form>

        </div>
      </div>
    </div>
    <CreateShipmentTemplate @close="close" v-if="preview"
                            :summary="form.summary"></CreateShipmentTemplate>
  </div>
</template>

<script>
import _, {find} from "lodash"
import dayjs from "dayjs"
import Affix from "@/components/Affix.vue"
import VueQuillEditor from "@/components/vue-quill-editor.vue"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
import {
  templateService,
  loadingService, videoService
} from "../../../services/_singletons"
import {defineComponent} from "@vue/runtime-core"
import CreateShipmentTemplate from "@/components/previews/CreateShipmentTemplate.vue";

export default defineComponent({
  components: {
    ResizeObserver,
    CreateShipmentTemplate,
    VueQuillEditor,
    Affix,
  },
  name: "Create Shipment Email",
  data() {
    return {
      video_id: null,
      videoList: [],
      preview: false,
      isMobile: false,
      ready: true,
      loading2: false,
      showDropzone: true,
      // dialogTableVisible: false,
      width: 0,
      height: "auto",
      loading: false,
      search: "",
      editMode: false,
      currentId: 0,
      dayjs,
      form: {
        id: null,
        summary: "",
        type: "",
      },
      prices: {
        personalize_email: {
          code: '',
          price: 0
        },
        personalize_sms: {
          code: '',
          price: 0
        },
        sms: {
          code: '',
          price: 0
        },
      },
      formLabelWidth: '120px',
      video_preview: {
        video_thumbnail: '@/assets/images/play.png',
        video_url: '',
      }
    }
  },
  computed: {},
  watch: {},
  methods: {
    close(value) {
      this.preview = value
      console.log("Val ", value)
    },
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      if (window.innerWidth <= 768) this.isMobile = true
    },
    onCanel() {
      this.$refs.templateForm.resetFields()
      var self = this;
      Object.keys(this.form).forEach(function (key, index) {
        self.form[key] = '';
      });
    },
    async onSubmit(values) {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const prices = {};
        prices[this.prices.personalize_email.code] = this.prices.personalize_email.price
        //  prices[this.prices.personalize_sms.code] = this.prices.personalize_sms.price
        //prices[this.prices.sms.code] = this.prices.sms.price
        const payload = {
          id: this.form.id,
          summary: this.form.summary,
          type: "PERSONALIZED_SHIPMENT_EMAIL",
          data: prices
        }


        const res = await templateService.saveTemplate(payload)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        //  await this.$router.push({path: "/template/create-email-template"})
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: "Email template failed to create",
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: "Error during creating record",
          position: "top-left"
        })
        // loggingService.error('Error during login', e);
        // notificationService.showError('Error during login');
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getTemplateSummary() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await templateService.getTemplateSummary({'type': 'PERSONALIZED_SHIPMENT_EMAIL'})
        if (res.data.template) {
          this.form = res.data.template
          if (res.data.data) {
            this.prices.personalize_email = res.data.data.personalize_email ?? {};
            //  this.prices.sms = res.data.data.sms ?? {};
            // this.prices.personalize_sms = res.data.data.personalize_sms ?? {};
          }
        }
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        if (e.response?.status === 400) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-left"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        console.log(e)
        this.$notify.error({
          title: "Error",
          message: e,
          position: "top-left"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
  },
  async created() {
    await this.init()
    await this.getTemplateSummary()
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt
  }
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/variables";

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}

.bb-br-gray {
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-bg-gray {
  background: #151526 !important;
}

.el-collapse-item__header {
  background: #151526 !important;
}

.el-button {
  width: 95px;
  height: 41px;
  border-radius: 5px;
}

.btn-blue-bg {
  width: 95px;
  height: 41px;
  background: #006EFB;
  border-radius: 5px;
}

.quill-editor {
  width: 100% !important;
}

</style>

<style lang="scss">
@import "../../../assets/scss/variables";


.el-button {
  height: 41px;
  border-radius: 5px;
}

.btn-blue-bg {
  height: 41px;
  background: #006EFB;
  border-radius: 5px;
}

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.full_width {
  width: 100%;
}
</style>
