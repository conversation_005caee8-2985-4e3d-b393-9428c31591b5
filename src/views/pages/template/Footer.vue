<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Footer Template</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Footer Template</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="row flex mt-3 mb-4">
      <div v-if="file" class="col-12">
        <i class="mdi mdi-clipboard-file"></i>
        {{ file.name }}
      </div>
      <div class="col-9"></div>
      <div v-if="file" class="col-1">
        <i class="mdi mdi-close-circle" @click="removeFile()"></i>
        <i :disabled="loadingUpload" class="mdi mdi-check-circle" @click="loadFile()"></i>
      </div>
    </div>
    <div class="card-base card-shadow--medium p-15">
      <div class="bb-bt-br">
        <div class="search-card scrollable only-y mt-2">
          <el-form ref="templateForm" :model="form" label-width="120px">
            <!-- <Form class="form-box" @submit="onSubmit" :validation-schema="schema"> -->
            <h4 class="ml-12">Footer Template</h4>

            <el-form-item label="Summary">
              <vue-quill-editor
                  v-model="form.summary"
                  :content="form.summary"
                  contentType="html"
                  ref="myQuillEditor1"
                  theme="bubble"
              >
              </vue-quill-editor>
            </el-form-item>
            <el-form-item v-if="ready" class="mt-30">
              <el-button class="btn-blue-bg" @click="onSubmit">Save</el-button>
              <el-button class="btn-blue-bg" @click="preview=true">Preview</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <FooterTemplate v-if="preview" @close="(value)=>preview=value" :footer="form.summary"></FooterTemplate>
  </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import Affix from "@/components/Affix.vue"
import VueQuillEditor from "@/components/vue-quill-editor.vue"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
import {
  templateService,
  loadingService
} from "../../../services/_singletons"
import {defineComponent} from "@vue/runtime-core"
import FooterTemplate from "@/components/previews/FooterTemplate.vue";

export default defineComponent({
  name: "Agreement Summary",
  data() {
    return {
      preview:false,
      isMobile: false,
      ready: true,
      loading2: false,
      showDropzone: true,
      // dialogTableVisible: false,
      width: 0,
      height: "auto",
      loading: false,
      search: "",
      editMode: false,
      // dialogUserVisible: false,
      currentId: 0,
      dayjs,
      form: {
        id: null,
        summary: "",
        type: "",
        // agreement_footer: "",

      },
      formLabelWidth: '120px'
    }
  },
  components: {
    FooterTemplate,
    VueQuillEditor,
    Affix,
    ResizeObserver,
  },
  computed: {},
  watch: {},
  methods: {
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      if (window.innerWidth <= 768) this.isMobile = true
    },
    onCanel() {
      this.$refs.templateForm.resetFields()
      var self = this;
      Object.keys(this.form).forEach(function (key, index) {
        self.form[key] = '';
      });
    },
    async onSubmit(values) {
      // display form values on success
      try {
        loadingService.startLoading('main-loader:login');
        console.log({...this.form.template})

        const res = await templateService.saveTemplate({
          id: this.form.id,
          summary: this.form.summary,
          // agreement_footer:this.form.agreement_footer,
          type: "FOOTER"
        })
        console.log(res);
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        await this.$router.push({path: "/template/create-footer-template"})
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: "Merchant data failed to create",
            position: "top-left"
          })
          return
        }
        if (e.response?.status === 400) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: "Error during creating record",
          position: "top-left"
        })
        // loggingService.error('Error during login', e);
        // notificationService.showError('Error during login');
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getTemplateSummary() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await templateService.getTemplateSummary({'type': 'FOOTER'})

        if (res.data.template) {
          this.form = res.data.template
        }


      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        console.log(e)
        this.$notify.error({
          title: "Error",
          message: e,
          position: "top-left"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },

  },
  created() {
    this.init()
    this.getTemplateSummary()
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt
  },
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}

.bb-br-gray {
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-bg-gray {
  background: #151526 !important;
}

.el-collapse-item__header {
  background: #151526 !important;
}

.el-button {
  width: 95px;
  height: 41px;
  border-radius: 5px;
}

.btn-blue-bg {
  width: 95px;
  height: 41px;
  background: #006EFB;
  border-radius: 5px;
}

.quill-editor {
  width: 100% !important;
}

</style>

<style lang="scss">
@import "../../../assets/scss/_variables";


.el-button {
  height: 41px;
  border-radius: 5px;
}

.btn-blue-bg {
  height: 41px;
  background: #006EFB;
  border-radius: 5px;
}

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.full_width {
  width: 100%;
}
</style>
