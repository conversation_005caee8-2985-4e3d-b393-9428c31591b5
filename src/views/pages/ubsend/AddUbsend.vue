<template>
    <div class="scrollable only-y p-2">
        <div class="toolbar-box flex align-center">
            <div class="page-header">
                <h1 class="ml-2">UBsend</h1>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                    <el-breadcrumb-item>UBsend</el-breadcrumb-item>
                    <el-breadcrumb-item :to="{path:'/ubsend/list'}">UBsend List</el-breadcrumb-item>
                    <el-breadcrumb-item> {{ $route.params.id ? 'Edit Account' : 'Add Account' }}</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>
        <div class="card-base card-shadow--medium p-15">
            <div class="bb-bt-br">
                <div class="search-card scrollable only-y mt-2">
                    <el-form class="form-box" :validation-schema="schema" label-width="120px">
                        <h4 class="ml-12">UBsend Account</h4>
                        <el-col class="flex" :span="24">
                            <el-col class="demo-form-inline mr-10" :span="11">
                                <el-form-item label="Account Name">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <Field v-model="form.account_name" class="el-input__inner"
                                                   name="account_name"
                                                   placeholder=""/>
                                        </div>
                                    </div>
                                    <ErrorMessage name="account_name" class="invalid-feedback"/>
                                </el-form-item>
                            </el-col>
                            <el-col class="demo-form-inline" :span="11">
                                <el-form-item label="ClientID">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <Field v-model="form.client_id"
                                                   type="number"
                                                   min="3"
                                                   name="client_id"
                                                   class="el-input__inner"

                                                   placeholder=""/>
                                        </div>
                                    </div>
                                    <ErrorMessage name="client_id" class="invalid-feedback"/>
                                </el-form-item>
                            </el-col>
                        </el-col>

                        <el-col class="flex" :span="24">
                            <el-col class="demo-form-inline mr-10" :span="11">
                                <el-form-item label="Username">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <Field v-model="form.username"
                                                   class="el-input__inner"
                                                   type="email"
                                                   name="username"

                                                   placeholder=""
                                            />
                                        </div>
                                    </div>
                                    <ErrorMessage name="username" class="invalid-feedback"/>
                                </el-form-item>
                            </el-col>
                            <el-col class="demo-form-inline" :span="11">
                                <el-form-item label="Password">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <Field v-model="form.password"
                                                   name="password"
                                                   type="password"
                                                   class="el-input__inner"

                                                   placeholder=""
                                            />
                                        </div>
                                    </div>
                                    <ErrorMessage name="password" class="invalid-feedback"/>
                                </el-form-item>
                            </el-col>
                        </el-col>

                        <el-form-item>
                            <button
                                    @click="onSubmit"
                                    :disabled="isDisabled" class="el-button el-button--submit btn-blue-bg" type="submit"
                                    v-loading.fullscreen.lock="fullscreenLoading">Save
                            </button>
                            <el-button @click="onCanel">Cancel</el-button>
                        </el-form-item>
                        <!-- </el-form> -->
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import users from "@/assets/data/USERS_MOCK_DATA.json"
    import _ from "lodash"
    import dayjs from "dayjs"
    import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
    import {useMainStore} from "@/stores/main"
    import {defineComponent} from "@vue/runtime-core"
    import {
        ubsendService,
        loadingService
    } from '../../../services/_singletons'
    import {Form, Field, ErrorMessage} from "vee-validate"
    import * as Yup from "yup"

    const schema = Yup.object().shape({
        account_name: Yup.string().required("Account Name is required"),
        password: Yup.string().min(6, "Password must be at least 6 characters").required("Password is required"),
        client_id: Yup.number().required("ClientID should be greater then zero or minimum three digit").test('len', 'ClientID shold be greater then zero or minimum three digit', val => val.toString().length >= 3),
        username: Yup.string().required("Username is required").email()
    })

    export default defineComponent({
        name: "AddUBsend",
        data() {
            return {
                schema,
                isMobile: false,
                ready: false,
                width: 0,
                height: "auto",
                loading: false,
                search: "",
                list: users,
                editMode: false,
                itemsChecked: [],
                dialogUserVisible: false,
                fullscreenLoading: false,
                isDisabled: false,
                currentId: 0,
                dayjs,
                form: {
                    account_name: "",
                    client_id: 0,
                    username: "",
                    password: "",
                    id: this.$route.params.id ? this.$route.params.id : ''
                },
            }
        },
        computed: {
            editData() {
                return useMainStore().selectedUbsend
            }
        },
        watch: {
            'form.client_id': {
                handler: function (after, before) {
                    // Changes detected.
                    if (after < 0) {
                        this.form.client_id = 0;
                    }
                },
                deep: true
            }
        },
        methods: {
            handleSelectionChange(val) {
                this.itemsChecked = val
            },
            init() {
                if (window.innerWidth <= 768) this.isMobile = true
            },
            async onSubmit(values) {
                // display form values on success
                this.isDisabled = true
                this.fullscreenLoading = true
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await ubsendService.addUbsend(this.form)
                    this.$notify({
                        title: "Success",
                        message: res.message,
                        type: "success"
                    })
                    await this.$router.push({name: 'udsend-list'})
                } catch (e) {
                    this.isDisabled = false
                    this.fullscreenLoading = false
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                    // notificationService.showError('Error during login');
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getUbsend() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await ubsendService.getUbsentById(this.$route.params.id);
                    this.list = res.data.list

                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }

            },
            onCanel() {
                this.$router.back()
            }
        },
        created() {
            this.init()
            if (this.$route.params.id) {
                console.log(this.editData)
                this.form.account_name = this.editData['account_name']
                this.form.username = this.editData['username']
                this.form.client_id = this.editData['client_id']
                this.form.password = this.editData['password']
            }
        },
        mounted() {
            //ie fix
            if (!window.Number.parseInt) window.Number.parseInt = parseInt
        },
        components: {
            ResizeObserver,
            Form,
            Field,
            ErrorMessage
        },
    })
</script>

<style lang="scss" scoped>
    @import "../../../assets/scss/_variables";

    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }
</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    .bb-bt-br {
        border-bottom: 1px solid #ebeef5;
        border-top: 1px solid #ebeef5;
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }

    .invalid-feedback {
        // display: none;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 80%;
        color: #dc3545;
    }

    .d-none {
        display: none;
    }

    .el-input__wrapper {
        max-width: 100%;
    }

    .el-form-item__label {
        font-family: "Nunito Sans";
        font-weight: 400;
        font-size: 14px;
        width: 22%;
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }
</style>
