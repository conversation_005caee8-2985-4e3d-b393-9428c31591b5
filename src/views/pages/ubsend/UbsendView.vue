<template>
    <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
        <div class="page-header">
            <h1>UBsend</h1>
            <!-- <h4>simple table</h4> -->
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                <el-breadcrumb-item>UBsend</el-breadcrumb-item>
                <el-breadcrumb-item>Upload UBsend File</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="toolbar-box flex align-center">
            <div class="box">
                <el-form-item label="UBsend Account">
                    <el-input disabled placeholder="UBsend Account..." v-model="ubsend_account" clearable></el-input>
                </el-form-item>
            </div>
        </div>

        <div class="table-box card-base card-shadow--medium box grow" id="table-wrapper" v-loading="!ready">
            <el-table
                    :data="listInPage"
                    style="width: 100%"
                    :height="height"
                    v-if="ready"
                    table-layout="auto"
                    @selection-change="handleSelectionChange"
            >
                <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
                <el-table-column label="S. No" min-width="80" prop="full_name" :fixed="!isMobile">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.$index+1, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="From" prop="from" min-width="150">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.row.from, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="Service" prop="service" min-width="200">
                    <template #default="scope">
                        {{ scope.row.shipping_product }}
                    </template>
                </el-table-column>
                <el-table-column label="Product" prop="product" min-width="200">
                    <template #default="scope">
                        {{ scope.row.product }}
                    </template>
                </el-table-column>
                <el-table-column label="Monthy Product" prop="monty product" min-width="200">
                    <template #default="scope">
                        {{ scope.row.monty_product }}
                    </template>
                </el-table-column>
                <el-table-column label="Carrier Product" prop="product" min-width="200">
                    <template #default="scope">
                        {{ scope.row.carrier_product }}
                    </template>
                </el-table-column>
                <el-table-column label="ShipvagooProduct" prop="shipvagoo_product" min-width="200">
                    <template #default="scope">
                        {{ scope.row.shipvagoo_product ?? scope.row.product }}
                    </template>
                </el-table-column>
                <el-table-column label="From Country" prop="from_country" min-width="150">
                    <template #default="scope">
                        <span class="sel-string"
                              v-html="$options.filters.selected(scope.row.from_country, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="To Country" prop="to_country" min-width="150">
                    <template #default="scope">
                        {{ scope.row.to_country }}
                    </template>
                </el-table-column>
                <el-table-column label="Lastmile Carrier" min-width="110" prop="carrier">
                    <template #default="scope">
                        <!--              {{scope.row.carrier}}-->
                        <span class="sel-string" v-html="$options.filters.selected(scope.row.carrier, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="Lead Time (Days)" min-width="110" prop="lead_time">
                    <template #default="scope">
                        {{scope.row.lead_time}}
                    </template>
                </el-table-column>
                <el-table-column label="Currency" min-width="110" prop="currency">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.row.currency, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="Weight from" style="white-space: nowrap;" prop="weight_class_from"
                                 min-width="110">
                    <template #default="scope">
                        {{ scope.row.weight_class_from }}
                    </template>
                </el-table-column>
                <el-table-column label="Weight to" prop="weight_class_to" min-width="110">
                    <template #default="scope">
                        {{ scope.row.weight_class_to}}
                    </template>
                </el-table-column>
                <el-table-column label="Cost Price" prop="cost_price" min-width="110">
                    <template #default="scope">
                        <span class="sel-string"
                              v-html="$options.filters.selected(scope.row.cost_price, search)"></span>
                    </template>
                </el-table-column>
                <!--          <el-table-column label="Action">-->
                <!--            <template #default="scope">-->
                <!--              <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>-->
                <!--              &lt;!&ndash; <el-dropdown trigger="click" @command="onCommand">-->
                <!--                  <span class="el-dropdown-link">-->
                <!--                      <i class="mdi mdi-dots-vertical"></i>-->
                <!--                  </span>-->
                <!--                  <template #dropdown>-->
                <!--                      <el-dropdown-menu>-->
                <!--                          <el-dropdown-item @click="setUbsend(scope.row)" :command="'/ubsend/edit-ubsend/'+scope.row.id" divided>-->
                <!--                              Edit-->
                <!--                          </el-dropdown-item>-->
                <!--                          <el-dropdown-item   @click="deleteUbsend(scope.row.id)" :command="'skip'" divided>-->
                <!--                              Delete-->
                <!--                          </el-dropdown-item>-->
                <!--                      </el-dropdown-menu>-->
                <!--                  </template>-->
                <!--              </el-dropdown> &ndash;&gt;-->
                <!--            </template>-->
                <!--          </el-table-column>-->
            </el-table>

            <el-pagination
                    v-if="ready"
                    :small="pagination.small"
                    v-model:current-page="pagination.page"
                    :page-sizes="pagination.sizes"
                    v-model:page-size="pagination.size"
                    :layout="pagination.layout"
                    :total="total"
            ></el-pagination>
            <div v-else>
                <el-form-item v-loading="ready"></el-form-item>
            </div>
        </div>
    </div>
</template>

<script>
    import _ from "lodash"
    import dayjs from "dayjs"
    import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
    import {useMainStore} from "@/stores/main"
    import {defineComponent} from "@vue/runtime-core"
    import {
        ubsendService,
        loadingService
    } from "../../../services/_singletons"

    export default defineComponent({
        name: "UbsendView",
        data() {
            return {
                isMobile: false,
                ready: false,
                loadingUpload: false,
                width: 0,
                height: "auto",
                loading: false,
                search: "",
                ubsend_account: "",
                pagination: {
                    page: 1,
                    size: 20,
                    sizes: [10, 15, 20, 30, 50, 100],
                    layout: "total, ->, prev, pager, next, jumper, sizes",
                    small: false
                },
                list: [],
                editMode: false,
                itemsChecked: [],
                dialogUserVisible: false,
                currentId: 0,
                dayjs,
                checkAll: false,
                checkedOptions: [],
                ckoptions: ["all", "Completed", "Pending"],
                isIndeterminate: true,
                fileList: [],
                file: '',
                content: [],
                parsed: false
            }
        },
        computed: {
            listFiltered() {
                return this.list.filter(obj => {
                    let ctrl = false
                    for (let k in obj) {
                        if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                    }
                    return ctrl
                })
            },
            listSortered() {
                let prop = this.sortingProp
                let order = this.sortingOrder
                return [].concat(
                    this.listFiltered.sort((item1, item2) => {
                        let val1 = ""
                        let val2 = ""

                        val1 = item1[prop]
                        val2 = item2[prop]
                        if (order === "descending") {
                            return val2 < val1 ? -1 : 1
                        }
                        return val1 < val2 ? -1 : 1
                    })
                )
            },
            listInPage() {
                let from = (this.currentPage - 1) * this.itemPerPage
                let to = from + this.itemPerPage * 1
                //return this.listSortered.slice(from, to)
                return this.listFiltered.slice(from, to)
            },
            total() {
                return this.listFiltered.length
            },
            currentPage: {
                get() {
                    return this.pagination.page
                },
                set(val) {
                    this.pagination.page = val
                }
            },
            itemPerPage() {
                return this.pagination.size
            },
            selectedItems() {
                return this.itemsChecked.length || 0
            },
            editData() {
                return useMainStore().selectedUbsend
            }
        },
        watch: {
            itemPerPage(val) {
                this.ready = false
                this.currentPage = 1

                setTimeout(() => {
                    this.ready = true
                }, 500)
            },
            search(val) {
                this.currentPage = 1
            },
            list(val, old) {
                if (val.length == 0) {
                    this.ready = false
                } else {
                    this.ready = true
                }
            }
        },
        methods: {
            handleCheckedCitiesChange(value) {
                let checkedCount = value.length
                this.checkAll = checkedCount === this.options.length
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
            },
            goAdd() {
                this.$router.push("ubsend/add")
            },
            calcDims() {
                const tableWrapper = document.getElementById("table-wrapper")
                if (tableWrapper) this.width = tableWrapper.clientWidth

                if (!this.isMobile && tableWrapper) {
                    this.height = tableWrapper.clientHeight - 44
                }

                if (this.width < 480) {
                    this.pagination.small = true
                    this.pagination.layout = "prev, pager, next"
                } else if (this.width >= 480 && this.width < 700) {
                    this.pagination.small = false
                    this.pagination.layout = "prev, pager, next, ->, sizes"
                } else {
                    this.pagination.small = false
                    this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
                }

                this.ready = true
            },
            handleResize: _.throttle(function (e) {
                this.ready = false
                this.width = 0
                setTimeout(this.calcDims, 1000)
            }, 500),
            handleSelectionChange(val) {
                this.itemsChecked = val
            },
            init() {
                if (window.innerWidth <= 768) this.isMobile = true
            },
            handleRemove(file, fileList) {
                console.log(file, fileList)
            },
            handlePreview(file) {
                console.log(file)
                this.file = file;
                this.parseFile();
            },
            handleExceed(files, fileList) {
                this.$message.warning(
                    "The limit is 3, you selected" +
                    files.length +
                    " files this time, add up to " +
                    files.length +
                    fileList.length +
                    "totally"
                )
            },
            beforeRemove(file, fileList) {
                return this.$confirm(file.name + "?")
            },
            handleUpload(file, fileList) {
                console.log(file.target.files[0])
                this.file = file.target.files[0];
                this.parseFile();
                console.log(fileList)
                // return this.$confirm(file.name + "?")
            },
            async getUbsendPricingList() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await ubsendService.getPricingList(
                        {
                            ubsend_account_id: this.$route.params.id
                        });
                    console.log(res)
                    this.list = res.data.pricing

                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            removeFile() {
                this.fileList = [],
                    this.file = ''
            }

        },
        filters: {
            selected: function (value, sel) {
                if (!value) return ""
                if (!sel) return value

                value = value.toString()
                sel = sel.toString()

                const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
                if (startIndex !== -1) {
                    const endLength = sel.length
                    const matchingString = value.substr(startIndex, endLength)
                    return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
                }
                //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
                return value
            }
        },
        created() {
            this.init()
            if (this.$route.params.id) {
                this.getUbsendPricingList()
                this.ubsend_account = this.editData.account_name
            }
        },
        mounted() {
            //ie fix
            if (!window.Number.parseInt) window.Number.parseInt = parseInt

            this.calcDims()
        },
        components: {ResizeObserver}
    })
</script>

<style lang="scss" scoped>
    @import "../../../assets/scss/_variables";

    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }
</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";
    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }
</style>
