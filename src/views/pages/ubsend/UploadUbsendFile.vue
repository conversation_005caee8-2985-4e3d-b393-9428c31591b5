<template>
    <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
        <div class="page-header">
            <h1>UBsend</h1>
            <!-- <h4>simple table</h4> -->
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                <el-breadcrumb-item>UBsend</el-breadcrumb-item>
                <el-breadcrumb-item>Upload UBsend File</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="toolbar-box align-center">
            <div class="box col-8">
                <el-form-item label="UBsend Account">
                    <el-input disabled placeholder="UBsend Account..." v-model="ubsend_account" clearable></el-input>
                </el-form-item>
            </div>
        </div>
        <div v-loading="loading">
            <div class="flex m-2">
                <div v-if="showDropzone ===true" class="col-12">
                    <!-- handleStart -->
                    <el-upload
                            class="upload-demo"
                            :on-preview="handlePreview"
                            :on-remove="handleRemove"
                            :before-remove="beforeRemove"
                            :on-change="handleStart"
                            @input="handleUpload"
                            multiple
                            drag
                            accept=".csv"
                            :limit="3"
                            :on-exceed="handleExceed"
                            :file-list="fileList"
                    >
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">Drop file here or <em>click to upload</em></div>
                        <div class="el-upload__tip" slot="tip">.csv files with a size less than 500kb</div>
                    </el-upload>
                    <div v-if="loadingUpload">
                        <el-form-item v-loading="ready"></el-form-item>
                    </div>
                </div>
            </div>
            <div>
                <div class="row flex mt-3 mb-4">
                    <div v-if="file" class="col-12"><i class="mdi mdi-clipboard-file"></i> {{ file.name }}</div>
                    <div class="col-9"></div>
                    <div v-if="file" class="col-1">
                        <i style="cursor: pointer;font-size: 22px" class="mdi mdi-close-circle"
                           @click="removeFile()"></i>
                        <i style="cursor: pointer;font-size: 22px" :disabled="loadingUpload"
                           class="mdi mdi-check-circle"
                           @click="loadFile()"></i>
                    </div>
                </div>
                <resize-observer @notify="handleResize"/>
                <div class="row mt-3 mb-4">
                    <div class="table-box card-base card-shadow--medium box grow" id="table-wrapper" v-loading="!ready">
                        <el-table
                                table-layout="auto"
                                :data="listInPage"
                                style="width: 100%"
                                v-if="ready"
                                @selection-change="handleSelectionChange"
                        >
                            <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
                            <el-table-column label="ID" min-width="60" prop="id" :fixed="!isMobile">
                                <template #default="scope">
                                <span
                                        class="sel-string"
                                        v-html="$options.filters.selected(scope.$index+((currentPage-1)*itemPerPage) + 1, search)"
                                ></span>
                                </template>
                            </el-table-column>
                            <!-- <el-table-column label="From" prop="from" min-width="150">
                                <template #default="scope">
                                    <span
                                        class="sel-string"
                                        v-html="$options.filters.selected(scope.row.from, search)"
                                    ></span>
                                </template>
                            </el-table-column>
                            <el-table-column label="Service" prop="service" min-width="200">
                                <template #default="scope">
                                    {{ scope.row.service }}
                                </template>
                            </el-table-column> -->
                            <el-table-column label="Product" prop="product" min-width="200">
                                <template #default="scope">
                                    {{ scope.row.product }}
                                </template>
                            </el-table-column>
                            <el-table-column label="Monty Product" prop="monty product" min-width="240">
                                <template #default="scope">
                  <span style="white-space: nowrap;" :title="scope.row.monty_product"> {{
                      scope.row.monty_product
                    }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="Carrier Product" prop="product" min-width="200">
                                <template #default="scope">
                                    {{ scope.row.carrier_product }}
                                </template>
                            </el-table-column>
                            <el-table-column label="ShipvagooProduct" prop="shipvagoo_product" min-width="200">
                                <template #default="scope">
                                    {{ scope.row.shipvagoo_product ?? scope.row.product }}
                                </template>
                            </el-table-column>
                            <el-table-column label="From Country" prop="from_country" min-width="150">
                                <template #default="scope">
                                <span
                                        class="sel-string"
                                        v-html="$options.filters.selected(scope.row.from_country, search)"
                                ></span>
                                </template>
                            </el-table-column>
                            <el-table-column label="To Country" prop="to_country" min-width="150">
                                <template #default="scope">
                                    {{ scope.row.to_country }}
                                </template>
                            </el-table-column>
                            <el-table-column label="Lastmile Carrier" min-width="140" prop="carrier">
                                <template #default="scope">
                                    <!--                                {{scope.row.carrier}}-->
                                    <span
                                            style="white-space: nowrap"
                                            class="sel-string"
                                            v-html="$options.filters.selected(scope.row.carrier, search)"
                                    ></span>
                                </template>
                            </el-table-column>
                            <el-table-column label="Lead Time (Days)" min-width="140" prop="lead_time">
                                <template #default="scope">
                                    {{ scope.row.lead_time }}
                                </template>
                            </el-table-column>
                            <el-table-column label="Currency" min-width="110" prop="currency">
                                <template #default="scope">
                                <span
                                        class="sel-string"
                                        v-html="$options.filters.selected(scope.row.currency, search)"
                                ></span>
                                </template>
                            </el-table-column>
                            <el-table-column label="Weight from" prop="weight_class_from" min-width="110">
                                <template #default="scope">
                                    {{ scope.row.weight_class_from }}
                                </template>
                            </el-table-column>
                            <el-table-column label="Weight to" prop="weight_class_to" min-width="110">
                                <template #default="scope">
                                    {{ scope.row.weight_class_to }}
                                </template>
                            </el-table-column>
                            <el-table-column label="Cost Price" prop="cost_price" min-width="110">
                                <template #default="scope">
                                <span
                                        class="sel-string"
                                        v-html="$options.filters.selected(scope.row.cost_price, search)"
                                ></span>
                                </template>
                            </el-table-column>

                            <el-table-column label="Markup %" prop="markup" min-width="110">
                                <template #default="scope">

                                <span v-if="typeof scope.row.markup ==='undefined' && hasQuery ===true ">
                                    {{ scope.row.shipvagoo_price_group.markup }}
                                </span>


                                    <span v-if="typeof scope.row.markup !=='undefined' && hasQuery ===false ">
                                        {{ scope.row.markup }}
                                </span>

                                </template>
                            </el-table-column>
                            <el-table-column


                                    label="Min. List Price excl. VAT" prop="min_shipping_standard_list_price"
                                    min-width="120">
                                <template #default="scope">
                                    <!-- <span
                                        class="sel-string"
                                        v-html="typeof scope.row.min_shipping_standard_list_price ==='undefined' && hasQuery ===true  ?
                                        (scope.row.shipvagoo_price_group.min_ship_standard_list_price).toFixed(2)
                                        :(scope.row.min_shipping_standard_list_price).toFixed(2) "
                                    ></span> -->

                                    <span v-if="typeof scope.row.min_shipping_standard_list_price ==='undefined' && hasQuery ===true ">
                                    <!-- {{ckValue(scope.row.shipvagoo_price_group.markup)}} -->
                                    {{ (scope.row.shipvagoo_price_group.min_ship_standard_list_price.toFixed(2)) }}
                                </span>


                                    <span v-if="typeof scope.row.min_shipping_standard_list_price !=='undefined' && hasQuery ===false ">
                                        {{ (scope.row.min_shipping_standard_list_price).toFixed(2) }}
                                </span>

                                </template>
                            </el-table-column>

                            <el-table-column

                                    label="List Price excl. VAT" prop="shipment_standard_list_price" min-width="120">
                                <template #default="scope">
                  <span>{{
                      scope.row.shipvagoo_price_group?.ship_standard_list_price ? Number(scope.row.shipvagoo_price_group.ship_standard_list_price).toFixed(2) : Number(scope.row.shipment_standard_list_price).toFixed(2)
                    }}</span>
                                </template>
                                <!-- {{  scope.row.shipvagoo_price_group.ship_standard_list_price}} -->
                            </el-table-column>
                            <el-table-column

                                    label="Gross Profit" prop="gross_profit" min-width="120">
                                <template #default="scope">
                                    <!-- <span v-if="typeof scope.row.gross_profit ==='undefined' && hasQuery ===true ">
                                        {{ scope.row.shipvagoo_price_group.gross_profit}}
                                    </span>

                                    <span v-if="typeof scope.row.gross_profit !=='undefined' &&hasQuery ===true ">
                                        {{ ( scope.row.shipvagoo_price_group.margin).toFixed(2)}}
                                    </span>
                                    <span v-if="typeof scope.row.gross_profit !=='undefined' && hasQuery ===false ">
                                            {{ scope.row.gross_profit }}
                                    </span> -->
                                    {{
                                    typeof scope.row.gross_profit === 'undefined' && hasQuery === true ?
                                    Number(scope.row.shipvagoo_price_group.margin).toFixed(2) :
                                    Number(scope.row.gross_profit).toFixed(2)
                                    }}

                                </template>
                            </el-table-column>
                            <el-table-column

                                    label="Price Group Type" prop="price_group_type" min-width="120">
                                <template #default="scope">
                                <span
                                        class="sel-string"
                                        v-html="typeof scope.row.price_group_type ==='undefined' && hasQuery ===true  ? scope.row.shipvagoo_price_group.price_group_type:scope.row.price_group_type"
                                >
                                </span>
                                </template>
                            </el-table-column>
                            <!--                        <el-table-column label="Action">-->
                            <!--                            <template #default="scope">-->
                            <!--                                <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>-->
                            <!--                                &lt;!&ndash; <el-dropdown trigger="click" @command="onCommand">-->
                            <!--                                    <span class="el-dropdown-link">-->
                            <!--                                        <i class="mdi mdi-dots-vertical"></i>-->
                            <!--                                    </span>-->
                            <!--                                    <template #dropdown>-->
                            <!--                                        <el-dropdown-menu>-->
                            <!--                                            <el-dropdown-item @click="setUbsend(scope.row)" :command="'/ubsend/edit-ubsend/'+scope.row.id" divided>-->
                            <!--                                                Edit-->
                            <!--                                            </el-dropdown-item>-->
                            <!--                                            <el-dropdown-item   @click="deleteUbsend(scope.row.id)" :command="'skip'" divided>-->
                            <!--                                                Delete-->
                            <!--                                            </el-dropdown-item>-->
                            <!--                                        </el-dropdown-menu>-->
                            <!--                                    </template>-->
                            <!--                                </el-dropdown> &ndash;&gt;-->
                            <!--                            </template>-->
                            <!--                        </el-table-column>-->
                        </el-table>

                        <el-pagination
                                v-if="ready"
                                :small="pagination.small"
                                v-model:current-page="pagination.page"
                                :page-sizes="pagination.sizes"
                                v-model:page-size="pagination.size"
                                :layout="pagination.layout"
                                :total="total"
                        ></el-pagination>
                        <div v-else>
                            <!--                        <el-form-item v-loading="ready"> </el-form-item>-->
                        </div>
                    </div>
                    <div class="col-3 mt-5">
                        <button
                                v-if="showSaveBtn === true"
                                class="el-button el-button--submit btn-blue-bg"
                                @click="uploadFile()"
                                type="submit"
                                v-loading.fullscreen.lock="fullscreenLoading"
                        >
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    // import users from "@/assets/data/USERS_MOCK_DATA.json"
    import _ from "lodash"
    import dayjs from "dayjs"
    import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
    import {useMainStore} from "@/stores/main"
    import {defineComponent} from "@vue/runtime-core"
    import {ubsendService, loadingService} from "../../../services/_singletons"

    export default defineComponent({
        name: "UploadUbsendFile",
        data() {
            return {
                isMobile: false,
                ready: false,
                loadingUpload: false,
                width: 0,
                height: "100%",
                loading: false,
                fullscreenLoading: false,
                search: "",
                ubsend_account: "",
                pagination: {
                    page: 1,
                    size: 20,
                    sizes: [10, 15, 20, 30, 50, 100],
                    layout: "total, ->, prev, pager, next, jumper, sizes",
                    small: false
                },
                list: [],
                apiList: [],
                backup: [],
                editMode: false,
                itemsChecked: [],
                dialogUserVisible: false,
                showSaveBtn: false,
                currentId: 0,
                dayjs,
                checkAll: false,
                checkedOptions: [],
                ckoptions: ["all", "Completed", "Pending"],
                isIndeterminate: true,
                fileList: [],
                file: null,
                content: [],
                parsed: false,
                showDropzone: true
            }
        },
        computed: {
            listFiltered() {
                return this.list.filter(obj => {
                    let ctrl = false
                    for (let k in obj) {
                        if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                    }
                    return ctrl
                })
            },
            listSortered() {
                let prop = this.sortingProp
                let order = this.sortingOrder
                return [].concat(
                    this.listFiltered.sort((item1, item2) => {
                        let val1 = ""
                        let val2 = ""

                        val1 = item1[prop]
                        val2 = item2[prop]
                        if (order === "descending") {
                            return val2 < val1 ? -1 : 1
                        }
                        return val1 < val2 ? -1 : 1
                    })
                )
            },
            listInPage() {
                let from = (this.currentPage - 1) * this.itemPerPage
                let to = from + this.itemPerPage * 1
                //return this.listSortered.slice(from, to)
                return this.listFiltered.slice(from, to)
            },
            total() {
                return this.listFiltered.length
            },
            currentPage: {
                get() {
                    return this.pagination.page
                },
                set(val) {
                    this.pagination.page = val
                }
            },
            itemPerPage() {
                return this.pagination.size
            },
            selectedItems() {
                return this.itemsChecked.length || 0
            },
            editData() {
                return useMainStore().selectedUbsend
            },
            hasQuery() {
                if (this.$route.query.q) {
                    return true;
                }
                return false
            },
        },
        watch: {
            itemPerPage(val) {
                this.ready = false
                this.currentPage = 1
                setTimeout(() => {
                    this.ready = true
                }, 500)
            },
            search(val) {
                this.currentPage = 1
            },

            ckValue(val) {
                if (val === undefined) {
                    return 0;
                }
                return val;
            },

            list(val, old) {
                // if (val.length == 0) {
                //     this.ready = false
                // } else {
                //     this.ready = true
                // }
            }
        },
        methods: {
            handleCheckedCitiesChange(value) {
                let checkedCount = value.length
                this.checkAll = checkedCount === this.options.length
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
            },
            goAdd() {
                this.$router.push("ubsend/add")
            },
            calcDims() {
                const tableWrapper = document.getElementById("table-wrapper")
                if (tableWrapper) this.width = tableWrapper.clientWidth

                if (!this.isMobile && tableWrapper) {
                    this.height = tableWrapper.clientHeight - 44
                }

                if (this.width < 480) {
                    this.pagination.small = true
                    this.pagination.layout = "prev, pager, next"
                } else if (this.width >= 480 && this.width < 700) {
                    this.pagination.small = false
                    this.pagination.layout = "prev, pager, next, ->, sizes"
                } else {
                    this.pagination.small = false
                    this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
                }

                this.ready = true
            },
            handleResize: _.throttle(function (e) {
                this.ready = false
                this.width = 0
                setTimeout(this.calcDims, 1000)
            }, 500),
            handleSelectionChange(val) {
                this.itemsChecked = val
            },
            init() {
                if (window.innerWidth <= 768) this.isMobile = true
            },
            handleRemove(file, fileList) {
                /*console.log(file, fileList)*/
            },
            handleStart(file) {

                this.file = file.raw ? file.raw : file
                this.parseFile()
            },
            handlePreview(file) {
                /* console.log(file)*/
                this.file = file
                this.parseFile()
            },
            handleExceed(files, fileList) {
                this.$message.warning(
                    "The limit is 3, you selected" +
                    files.length +
                    " files this time, add up to " +
                    files.length +
                    fileList.length +
                    "totally"
                )
            },
            beforeRemove(file, fileList) {
                return this.$confirm(file.name + "?")
            },
            handleUpload(file, fileList) {
                /* console.log(file.target.files[0])*/
                this.file = file.target.files[0]
                this.parseFile()
                console.log("File ", this.file)

                // return this.$confirm(file.name + "?")
            },
            parseFile() {
                this.$papa.parse(this.file, {
                    header: true,
                    skipEmptyLines: true,
                    complete: function (results) {
                        this.content = results
                        this.parsed = true
                    }.bind(this)
                })
                setTimeout(() => {
                    /*console.log(this.content.data, 'this.content.data')*/
                    this.backup = this.content.data.map(item => {
                        return {
                            from: item.From,
                            service: item.Service,
                            product: item["Product"],
                            monty_product: item["Monty Product"],
                            carrier_product: item["Carrier Product"],
                            shipvagoo_product: item["Shipvagoo Product"],
                            from_country: item["From Country"],
                            to_country: item["To Country"],
                            carrier: item["Lastmile Carrier"],
                            lead_time: item["Lead Time (Days)"],
                            currency: item["Currency"],
                            weight_class_from: item["Weight from"],
                            weight_class_to: item["Weight to"],
                            cost_price: item["Cost Price"],
                            price_group_type: item["Price Group Type"],
                            markup: item["Markup %"],
                            // gross_profit: item["Gross Profit"],
                            gross_profit: item["Gross Margin"],
                            min_shipping_standard_list_price: item["Min. SV List Price ex. VAT"],
                            shipment_standard_list_price: item["SV List Price ex. VAT"],
                            // min_shipping_standard_list_price: item["Min. List Price excl. VAT"],
                            // shipment_standard_list_price: item["List Price excl. VAT"],
                            control_condition: item["Control Condition"]
                            // margin: item["margin"],
                            // cost_price_plus_markup: item["cost price plus markup"],
                            // customer_price: item["customer price"],
                        }
                    })
                }, 1000)
            },
            async loadFile() {
                if (this.apiList.length > 0) {
                    await this.$confirm(
                        "This UBsend account already have records. Do you really wants to overwrite?",
                        "Warning",
                        {
                            confirmButtonText: "Yes",
                            cancelButtonText: "Cancel",
                            type: "warning",
                            center: true
                        }
                    )
                        .then(() => {
                            // loadingService.startLoading('main-loader:login');
                            this.list = []
                            setTimeout(() => {
                                this.list = this.backup
                                this.showSaveBtn = true
                            }, 200)
                            this.$notify({
                                title: "Success",
                                message: res.message,
                                type: "success"
                            })
                        })
                        .catch(() => {
                        })
                } else {
                    this.list = []
                    console.log(this.backup);
                    setTimeout(() => {
                        this.list = this.backup
                        this.showSaveBtn = true
                    }, 1000)
                }
            },
            async uploadFile() {
                // display form values on success
                try {
                    this.ready = true
                    this.fullscreenLoading = true
                    const formData = new FormData()
                    formData.append("file", this.file)
                    formData.append("ubsend_account_id", this.$route.query.q)
                    // loadingService.startLoading('main-loader:login');
                    const res = await ubsendService.uploadFile(formData)
                    this.ready = false
                    this.$notify({
                        title: "Success",
                        message: res.message,
                        type: "success"
                    })
                    await this.$router.back();
                    this.fullscreenLoading = false

                } catch (e) {
                    //this.ready = false
                    this.fullscreenLoading = false
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }

                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                    // notificationService.showError('Error during login');
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getUbsendPricingList() {
                // display form values on success
                try {
                    this.loading = true
                    // loadingService.startLoading('main-loader:login');
                    this.fullscreenLoading = true
                    const res = await ubsendService.getPricingList({
                        ubsend_account_id: this.$route.query.q
                    })

                    this.ready = true
                    this.list = res.data.pricing
                    this.apiList = res.data.pricing
                    this.showDropzone = res.data.pricing.length > 0 ? false : true
                    this.fullscreenLoading = false
                    this.loading = false

                } catch (e) {
                    this.ready = true
                    this.fullscreenLoading = false

                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    loadingService.stopLoading("main-loader:login")
                    this.loading = false
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
                this.loading = false
            },
            removeFile() {
                // this.list= [],
                ;(this.content.data = []), (this.list = [])
                ;(this.fileList = []), (this.file = "")
                this.showSaveBtn = false
            }
        },
        filters: {
            selected: function (value, sel) {
                if (!value) return ""
                if (!sel) return value

                value = value.toString()
                sel = sel.toString()

                const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
                if (startIndex !== -1) {
                    const endLength = sel.length
                    const matchingString = value.substr(startIndex, endLength)
                    return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
                }
                //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
                return value
            }
        },
        created() {
            this.init()
            if (this.$route.query.q) {
                this.getUbsendPricingList()
                this.ubsend_account = this.editData?.account_name
            }
        },
        mounted() {
            //ie fix
            if (!window.Number.parseInt) window.Number.parseInt = parseInt
            const label = document.getElementsByClassName('el-form-item__label');
            if (label)
                console.log(label[0])
            label[0].style.width = '8% !important'
            this.calcDims()
        },
        components: {ResizeObserver}
    })
</script>

<style lang="scss" scoped>
    @import "../../../assets/scss/_variables";

    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }


</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }

    .box > .el-form-item > .el-form-item__label {
        font-family: "Nunito Sans";
        font-weight: 400;
        font-size: 14px;
        width: 8% !important;
    }
</style>
