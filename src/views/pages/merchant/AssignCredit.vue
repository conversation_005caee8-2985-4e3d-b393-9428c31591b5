<template>
    <div class="scrollable only-y p-2" v-loading="loading">
        <div class="card-base card-shadow--medium p-15">
            <div class="bb-bt-br">
                <div class="search-card scrollable only-y mt-2">
                    <el-form label-width="130px">
                        <el-form label-width="130px" class="mt-5">
                            <el-form-item label="Assign Credit">
                                <el-switch
                                    v-model="credit_enabled"
                                    :active-value="true"
                                    :inactive-value="false"
                                    @change="showCreditStatusConfirmation"
                                />
                            </el-form-item>
                        </el-form>
                    </el-form>
                </div>
            </div>
        </div>

        <div class="card-base card-shadow--medium p-15 mt-20" v-if="credit_enabled">
            <div class="bb-bt-br">
                <div class="search-card scrollable only-y mt-2">
                    <el-form label-width="130px">
                        <Form class="form-box" @submit="saveCreditDetails" @submit.prevent>
                            <el-col class="demo-form-inline flex" :span="24">
                                <el-col class="demo-form-inline" :span="12">
                                    <el-form-item label="Credit Limit">
                                        <el-input type="number" v-model.number="credit_limit" style="width: 100%" />
                                    </el-form-item>
                                </el-col>

                                <el-col class="demo-form-inline pl-10" :span="12">
                                    <el-form-item label="Billing Cycle (days)">
                                        <el-input type="number" v-model.number="billing_cycle" style="width: 100%" />
                                    </el-form-item>
                                </el-col>
                            </el-col>
                            <el-col class="demo-form-inline flex" :span="24">
                                <el-col class="demo-form-inline" :span="12">
                                    <el-form-item label="Billing Cycle Start">
                                        <el-date-picker
                                            v-model="billing_cycle_start"
                                            type="date"
                                            placeholder="Pick a date"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col class="demo-form-inline" :span="12">
                                    <el-form-item>
                                        <el-button type="primary" @click="saveCreditDetails" :loading="saving">
                                            Save
                                        </el-button>
                                    </el-form-item>
                                </el-col>
                            </el-col>
                        </Form>
                    </el-form>
                    <div v-if="loading" class="mt-3">Loading credit details...</div>
                </div>
            </div>
        </div>

        <!-- Credit Status Confirmation Modal -->
        <el-dialog title="" v-model="confirmationDialogVisible" class="p-0" style="width: 500px;">
            <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">
                <h4 class="ml-12">{{ confirmationTitle }}</h4>
                <div class="mt-20 mb-20">
                    <p>{{ confirmationMessage }}</p>
                </div>
                <div class="bt-br"></div>
                <div class="mt-30 dialog-footer">
                    <el-button class="btn-blue-bg" @click="confirmCreditStatusUpdate" v-loading="confirmationLoading">
                        Yes
                    </el-button>
                    <el-button class="btn" @click="cancelCreditStatusUpdate">
                        No
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import Affix from "@/components/Affix.vue"
import { defineComponent } from "@vue/runtime-core"
import { merchantService } from "@/services/_singletons"

export default defineComponent({
    name: "AssignCredit",
    props: {
        user: {
            type: Object,
            required: true
        },
        companyCode: {
            type: String,
            required: true
        }
    },

    data() {
        return {
            loading: false,
            isDisabled: false,
            saving: false,
            credit_limit: null,
            credit_enabled: false,
            billing_cycle: null,
            billing_cycle_start: null,
            confirmationDialogVisible: false,
            confirmationLoading: false,
            pendingCreditStatus: false,
            confirmationTitle: '',
            confirmationMessage: ''
        }
    },
    components: {
        Affix
    },
    created() {
        this.fetchCreditDetails()
    },
    methods: {
        async fetchCreditDetails() {
            this.loading = true
            try {
                const res = await merchantService.getMerchantCreditDetails(this.companyCode)
                const details = Array.isArray(res) ? res[0] : res.data.company || null

                // Prefill form fields if available
                if (details) {
                    this.credit_limit = details.credit_limit ?? null
                    this.credit_enabled = !!(
                        details.credit_enabled === true ||
                        details.credit_enabled === "1" ||
                        details.credit_enabled === 1
                    )
                    this.billing_cycle = details.billing_cycle ?? null
                    this.billing_cycle_start = details.billing_cycle_start ?? null
                }
            } catch (e) {
                this.$notify?.error?.({
                    title: "Error",
                    message: e?.response?.data?.error?.[0] || "Failed to fetch credit details",
                    position: "top-right"
                })
            } finally {
                this.loading = false
            }
        },

        async saveCreditDetails() {
            this.saving = true
            try {
                const payload = {
                    company_code: this.companyCode,
                    credit_limit: this.credit_limit,
                    billing_cycle: this.billing_cycle,
                    billing_cycle_start: this.billing_cycle_start
                }

                // You may need to adjust the service method and endpoint for saving
                await merchantService.updateCreditDetails(payload)
                this.$notify?.({
                    title: "Success",
                    message: "Credit details updated successfully!",
                    type: "success"
                })
                this.fetchCreditDetails()
            } catch (e) {
                this.$notify?.error?.({
                    title: "Error",
                    message: e?.response?.data?.error?.[0] || "Failed to update credit details",
                    position: "top-right"
                })
            } finally {
                this.saving = false
            }
        },

        showCreditStatusConfirmation(newValue) {
            // Store the pending status change
            this.pendingCreditStatus = newValue

            // Set confirmation dialog content based on the action
            if (newValue) {
                this.confirmationTitle = 'Enable Credit'
                this.confirmationMessage = 'Are you sure you want to enable customer credit functionality? This will allow customers to use credit for their purchases.'
            } else {
                this.confirmationTitle = 'Disable Credit'
                this.confirmationMessage = 'Are you sure you want to disable customer credit functionality? This will prevent customers from using credit for their purchases.'
            }

            // Revert the switch to its previous state temporarily
            this.credit_enabled = !newValue

            // Show confirmation dialog
            this.confirmationDialogVisible = true
        },

        async confirmCreditStatusUpdate() {
            this.confirmationLoading = true
            try {
                const requestPayload = {
                    company_code: this.$route.params.company_code,
                    credit_enabled: this.pendingCreditStatus
                }

                const res = await merchantService.updateCreditStatus(requestPayload)

                // Update the switch to the new value
                this.credit_enabled = this.pendingCreditStatus

                // Emit event to parent component to update display
                this.$emit('credit-status-changed', this.pendingCreditStatus)

                this.$notify({
                    title: "Success",
                    message: res.message,
                    type: "success"
                })

                this.confirmationDialogVisible = false
            } catch (e) {
                this.$notify?.error?.({
                    title: "Error",
                    message: e?.response?.data?.error?.[0] || "Failed to update credit status",
                    position: "top-right"
                })
            } finally {
                this.confirmationLoading = false
            }
        },

        cancelCreditStatusUpdate() {
            this.confirmationDialogVisible = false
        }
    }
})
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.el-dialog .header{
    padding: 0px !important;
    margin: 0px !important;
}
.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
}

.Font-Size1 {
    font-size: 14px !important;
}

.seacr-box {
    width: 58rem;
    max-width: 100%;
    padding: 10px 30px;
    border-radius: 5px;
    border: 1px solid #eeeeee;
    outline: none;
}

.mdi {
    padding-top: 2px;
    padding-left: 4px;
    color: #bbbbbb !important;
}
</style>
