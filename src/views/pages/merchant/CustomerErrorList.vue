<template>
    <div class="page-table column scrollable only-y mb-20" :class="{ flex: !isMobile, overflow: isMobile }">
        <div class="mt-20">
            <search-box
                @search="value => (search = value)"
                placeholder="Search by Customer, Customer ID, Error Form"
            ></search-box>
        </div>
        <div class="card-shadow--medium box border grow mt-10" v-loading="loading">
            <el-table :data="list" style="width: 100%" :height="height">
                <el-table-column label="Error ID" min-width="35" align="center" prop="id" />
                <el-table-column label="Date" prop="created_at">
                    <template #default="scope">
                        {{ dayjs(scope.row.created_at).format("DD-MM-YYYY HH:mm:ss") }}
                    </template>
                </el-table-column>
                <el-table-column label="Error From" prop="platform"></el-table-column>
                <el-table-column label="Exception (Error)" prop="exception">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.exception" placement="bottom" effect="dark">
                            {{ scope.row.exception ? JSON.stringify(scope.row.exception).substring(0, 50) : "" }}
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="Transaction ID" prop="transaction_code"></el-table-column>
                <el-table-column label="Shipment ID" prop="shipment_code"></el-table-column>

                <el-table-column label="Action" align="center">
                    <template #default="scope">
                        <span class="sel-string"></span>
                        <el-dropdown trigger="hover" @command="onCommand">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="onClickViewDetails(scope.row)">
                                        View Detail
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                v-if="!loading"
                :small="pagination.small"
                v-model:current-page="pagination.page"
                :page-sizes="pagination.sizes"
                v-model:page-size="pagination.size"
                :layout="pagination.layout"
                @current-change="changeCurrentPage"
                @size-change="handleSizeChange"
                :total="pagination.total"
            ></el-pagination>
        </div>
    </div>
</template>

<script>
import dayjs from "dayjs"
import { defineComponent } from "@vue/runtime-core"
import { logService, loadingService } from "../../../services/_singletons"

export default defineComponent({
    name: "CustomerErrorList",
    props: {
        companyCode: {
            required: true,
            type: String
        }
    },
    data() {
        return {
            isMobile: false,
            loading: false,
            ready: false,
            width: 0,
            height: "auto",
            search: "",
            pagination: {
                page: 1,
                size: 20,
                sizes: [10, 15, 20, 30, 50, 100],
                layout: "total, ->, prev, pager, next, jumper, sizes",
                small: false,
                total: 10
            },
            list: [],
            editMode: false,
            itemsChecked: [],
            dialogUserVisible: false,
            currentId: 0,
            dayjs,
            checkAll: false,
            checkedOptions: [],
            ckoptions: ["all", "Completed", "Pending"],
            isIndeterminate: true
        }
    },
    computed: {
        listFiltered() {
            return this.list.filter(obj => {
                let ctrl = false
                for (let k in obj) {
                    if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                }
                return ctrl
            })
        },
        listSortered() {
            let prop = this.sortingProp
            let order = this.sortingOrder
            return [].concat(
                this.listFiltered.sort((item1, item2) => {
                    let val1 = ""
                    let val2 = ""

                    val1 = item1[prop]
                    val2 = item2[prop]
                    if (order === "descending") {
                        return val2 < val1 ? -1 : 1
                    }
                    return val1 < val2 ? -1 : 1
                })
            )
        },
        listInPage() {
            let from = (this.currentPage - 1) * this.itemPerPage
            let to = from + this.itemPerPage * 1
            //return this.listSortered.slice(from, to)
            return this.listFiltered.slice(from, to)
        },
        total() {
            return this.listFiltered.length
        },
        currentPage: {
            get() {
                return this.pagination.page
            },
            set(val) {
                this.pagination.page = val
            }
        },
        itemPerPage() {
            return this.pagination.size
        },
        selectedItems() {
            return this.itemsChecked.length || 0
        }
    },
    watch: {
        itemPerPage(val) {
            this.ready = false
            this.currentPage = 1

            setTimeout(() => {
                this.ready = true
            }, 500)
        },
        search(val) {
            this.currentPage = 1
        }
    },
    methods: {
        async onClickViewDetails(row) {
            await localStorage.setItem("errorLog", JSON.stringify(row))
            this.$router.push({ path: `/error/logs/${row.id}/details` })
        },
        changeCurrentPage(page) {
            this.getErrorLogsList(page, this.pagination.size)
        },
        handleSizeChange(size) {
            this.getErrorLogsList(this.pagination.page, size)
        },
        onCommandLang(lang) {
            if (lang.charAt(0) === "/") this.onCommand(lang)
            else this.lang = lang
        },
        onCommand(path) {
            if (path !== "skip") this.$router.push(path)
        },
        init() {
            if (window.innerWidth <= 768) this.isMobile = true
        },
        async getErrorLogsList(page = 1, perPage = 10) {
            this.loading = true
            try {
                const params = {
                    page: page,
                    perPage: perPage,
                    search: this.search,
                    company_code: this.companyCode
                }
                const res = await logService.getCustomerErrorLogsList(params)
                const data = res.data

                this.list = data.errorLogs
                this.pagination = {
                    ...this.pagination,
                    page: data.currentPage,
                    size: data.perPage,
                    total: data.total
                }
                this.loading = false
            } catch (e) {
                console.log("Error ", e)
                this.loading = false
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response?.data?.error[0],
                    position: "top-right"
                })
                // loggingService.error('Error during login', e);
            } finally {
                loadingService.stopLoading("main-loader:login")
                this.loading = false
            }
        }
    },
    filters: {
        selected: function (value, sel) {
            if (!value) return ""
            if (!sel) return value

            value = value.toString()
            sel = sel.toString()

            const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
            if (startIndex !== -1) {
                const endLength = sel.length
                const matchingString = value.substr(startIndex, endLength)
                return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
            }
            return value
        }
    },
    created() {
        this.init()
        this.getErrorLogsList()
    },
    mounted() {
        if (!window.Number.parseInt) window.Number.parseInt = parseInt
    }
})
</script>
