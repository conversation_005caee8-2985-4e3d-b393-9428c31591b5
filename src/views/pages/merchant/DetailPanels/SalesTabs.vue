<template>
  <div class="mws-scroller">
    <el-row :gutter="20">
      <el-col :span="12">
        <ShipvagooEarning
            :dates="dates"
            :customerId="customerId" @change="changeDates"/>
      </el-col>
      <el-col :span="12">
        <CustomerSpent
            @change="changeDates"
            :customerId="customerId"
            :dates="dates"/>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <div style="line-height: 30px;margin-bottom: 20px;margin-top: 20px;margin-left: 10px">Sales Statistics</div>
        <SalesStatistics @changeYear="(value)=>{year=value}" :customerId="customerId" :year="year"/>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <div style="line-height: 30px;margin-bottom: 20px;margin-top: 20px;margin-left: 10px">Carrier Statistics</div>
        <CarrierStastistics @changeYear="(value)=>{year=value}" :customerId="customerId" :year="year"/>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ShipvagooEarning from "@/views/pages/merchant/DetailPanels/Sales/ShipvagooEarning.vue";
import CustomerSpent from "@/views/pages/merchant/DetailPanels/Sales/CustomerSpent.vue";
import SalesStatistics from "@/views/pages/merchant/DetailPanels/Sales/SalesStatistics.vue";
import CarrierStastistics from "@/views/pages/merchant/DetailPanels/Sales/CarrierStastistics.vue";
import {ref} from 'vue';
import moment from "moment";

const default_date = '01-01-2023';
const date=new Date();
export default {
  name: "SalesTabs",
  props: ['customerId'],
  setup(props) {
    let dates = ref({
      start_date: moment(new Date(date.getFullYear(), date.getMonth(), 1)).format('YYYY-MM-DD'),// new Date(default_date),
      end_date: new Date()//moment(new Date(default_date)).add(1, 'months').format('yyyy-MM-DD')
    })
    const year = ref(date.getFullYear())
    const changeDates = (value) => {
      dates.value = value;
    }
    return {
      customerId: props.customerId,
      changeDates, dates,
      year
    }
  },
  components: {CarrierStastistics, SalesStatistics, CustomerSpent, ShipvagooEarning}
}
</script>

<style scoped>
.el-scrollbar {
  overflow: hidden;
  position: relative;
  height: 66vh;
}

.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-col:first-child {
  padding-left: 0 !important;
}

.el-col:last-child {
  padding-right: 0 !important;
}

.mws-scroller {
  position: relative;
  height: 100% !important;
  padding-bottom: 200px !important;
}

</style>