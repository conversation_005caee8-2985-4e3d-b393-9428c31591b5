<template>
    <div>
        <div class="search-container">
            <search-box @search="search"></search-box>
        </div>
        <div class="page-table scrollable only-y" id="affix-container">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Created At</th>
                            <th>Invoice Number</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Due Date</th>
                            <th>Month</th>
                            <th>Year</th>
                            <th>Payment Status</th>
                            <th class="text-right">Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(invoice, index) in invoices" :key="index">
                            <td>
                                {{ dayjs(String(invoice.created_at)).format("DD-MM-YYYY") }}<br />
                                {{ dayjs(String(invoice.created_at)).format("HH:mm:ss") }}
                            </td>
                            <td>
                                {{ HelperService.formatId(invoice.id) }}
                            </td>
                            <td>{{ dayjs(String(invoice.start_date)).format("DD-MM-YYYY ") }}</td>

                            <td>
                                {{ dayjs(String(invoice.end_date)).format("DD-MM-YYYY ") }}
                            </td>
                            <td>
                                {{ dayjs(String(invoice.due_date)).format("DD-MM-YYYY ") }}
                            </td>
                            <td>
                                {{ invoice.month }}
                            </td>
                            <td>{{ invoice.year }}</td>

                            <td>
                                {{ invoice.payment_status }}
                            </td>
                            <td class="text-right">DKK {{ danishNumberFormat(invoice.total_amount ?? 0) }}</td>
                            <td>
                                <el-dropdown trigger="hover" @command="onCommand">
                                    <span class="el-dropdown-link">
                                        <i class="mdi mdi-dots-vertical"></i>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item @click="onClickCreateSettlement(invoice)">Create
                                                settlement</el-dropdown-item>
                                            <el-dropdown-item>View invoice</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination class="flex" ref="pgi" v-if="!loading" small="false" @current-change="changeCurrentPage"
                    @size-change="handleSizeChange" :current-page="currentPage" v-model:page-size="perPage"
                    :page-sizes="[10, 20, 50, 100]" layout="total, ->,sizes, prev, pager, next, jumper"
                    :total="total"></el-pagination>
            </div>
        </div>

        <!-- Settlement Dialog -->
        <SettlementDialog v-model:visible="settlementDialogVisible" :invoice="selectedInvoice" @save="onSaveSettlement"
            @close="onCloseSettlement" />

        <div class="pdf-assets" aria-hidden>
            <img class="pdf-logo" height="106" width="662.88" src="../../../../../assets/images/shipvagoo_dark.png" />
        </div>
        <Invoice v-if="receipt_data" :invoiceToPrint="receipt_data"></Invoice>
    </div>
</template>

<script>
import { getDateFormat } from "../../../../../helpers"
import { loadingService, merchantService } from "@/services/_singletons"
import PaymentReceipt from "@/components/previews/payments/PaymentReceipt.vue"
import { useMainStore } from "@/stores/main"
import { danishNumberFormat } from "@/helpers"
import Invoice from "@/components/pdfs/Invoice.vue"
import dayjs from "dayjs"
import HelperService from "../../../../../services/helper.service"
import { useHelper } from "../../../../../composeable/Helper"
import SettlementDialog from "@/components/dialogs/CreateSettlementDialog.vue" // Adjust path as needed

const { downloadTransactionPdf } = useHelper()
export default {
    name: "Invoices",
    components: {
        Invoice,
        PaymentReceipt,
        SettlementDialog
    },
    methods: {
        danishNumberFormat,

        onClickCreateSettlement(invoice) {
            this.selectedInvoice = invoice
            this.settlementDialogVisible = true
        },

        async onSaveSettlement(settlementData) {
            try {

                console.log('Settlement data:', settlementData)

                // Example API call:
                // await merchantService.createSettlement(settlementData)

                this.$notify.success({
                    title: "Success",
                    message: "Settlement created successfully",
                    position: "top-right"
                })

                // Refresh the invoices list
                this.getBillingInvoices(this.currentPage, this.perPage)

                // Close the dialog
                this.onCloseSettlement()

            } catch (error) {
                console.error('Error creating settlement:', error)
                this.$notify.error({
                    title: "Error",
                    message: error.message || "Failed to create settlement",
                    position: "top-right"
                })
            }
        },
        onCloseSettlement() {
            this.settlementDialogVisible = false
            this.selectedInvoice = null
        },
        async showReceipt(transaction) {
            await downloadTransactionPdf(transaction.transaction_code, transaction.transaction_type)
        },
        search(value) {
            this.getBillingInvoices(1, this.perPage, value)
        },
        async showInvoice(invoice) {
            await downloadTransactionPdf(invoice.transaction_code, "invoice")
        },
        handleSizeChange(size) {
            this.getBillingInvoices(this.currentPage, size)
        },
        changeCurrentPage(page) {
            this.getBillingInvoices(page, this.perPage)
        },
        getDateFormat,
        async getBillingInvoices(page = 1, perPage = 10, filter = "") {
            this.loading = true
            try {
                const params = {
                    customerId: this.customerId,
                    page: page,
                    perPage: perPage,
                    filter: filter
                }
                const res = await merchantService.getMerchantBillingInvoices(params)
                if (res.entities) {
                    this.currentPage = res.currentPage
                    this.lastPage = res.lastPage
                    this.perPage = res.perPage
                    this.total = res.totalRecords
                    this.invoices = res.entities
                    this.loading = false
                } else {
                    this.currentPage = res.data.currentPage
                    this.lastPage = res.data.lastPage
                    this.perPage = res.data.perPage
                    this.total = res.data.totalRecords
                    this.invoices = res.data.invoice
                    this.loading = false
                }

                console.log(this.company)
            } catch (e) {
                console.log("ERROR ", e)
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                } else if (e.response.status === 500) {
                    this.$notify.error({
                        title: "Error",
                        message: e.message ?? e.message,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0] ?? e.response.data.error,
                    position: "top-right"
                })
                this.loading = false
            } finally {
                loadingService.stopLoading("main-loader:login")
                this.loading = false
            }
        }
    },
    data() {
        return {
            invoices: [],
            loading: false,
            currentPage: 1,
            lastPage: 0,
            perPage: 10,
            total: 0,
            receipt_data: null,
            merchant: {},
            view_transaction_dialog: false,
            settlementDialogVisible: false,
            selectedInvoice: null,
            dayjs,
            HelperService
        }
    },
    props: ["customerId"],
    created() {
        this.getBillingInvoices(this.currentPage, this.perPage)
    },
    mounted() {
        this.merchant = useMainStore().getMerchant
    },
    setup(props) {
        return {
            customerId: props.customerId
        }
    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
    margin-bottom: 11px;
}

.Font-Size1 {
    font-size: 14px !important;
}

.seacr-box {
    width: 58rem;
    max-width: 100%;
    padding: 10px 30px;
    border-radius: 5px;
    border: 1px solid #eeeeee;
    outline: none;
}

.mdi {
    padding-top: 2px;
    padding-left: 4px;
    color: #bbbbbb !important;
}

.pdf-assets {
    height: 0;
    overflow: hidden;
}
</style>