<template>
    <div>
        <div class="search-container">
            <search-box @search="search"></search-box>
        </div>
        <div class="page-table scrollable only-y" id="affix-container">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Created At</th>
                            <th>Transaction ID</th>
                            <th>Comments</th>
                            <th>Invoice Number</th>
                            <th>Transaction Type</th>
                            <th class="text-right">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(invoice, index) in invoices" :key="index">
                            <td>{{ invoice.created_at }}</td>
                            <td class="cursor-pointer text-blue">
                                <a href="#" @click="showReceipt(invoice)">
                                    {{ invoice.transaction_code }}
                                </a>
                            </td>
                            <td>Shipment ID: {{ invoice.shipment_code }}</td>
                            <td>
                                {{ HelperService.formatId(invoice.id) }}
                            </td>
                            <td>{{ invoice.transaction_type }}</td>
                            <td class="text-right">DKK {{ danishNumberFormat(invoice.amount) }}</td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination
                    class="flex"
                    ref="pgi"
                    v-if="!loading"
                    small="false"
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :current-page="currentPage"
                    v-model:page-size="perPage"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, ->,sizes, prev, pager, next, jumper"
                    :total="total"
                ></el-pagination>
            </div>
        </div>
        <!--<el-dialog class="cai-dialog-wrapper" title="" v-model="view_transaction_dialog">
        <PaymentReceipt :data="receipt_data" type="invoice"></PaymentReceipt>
    </el-dialog>-->
    </div>
    <div class="pdf-assets" aria-hidden>
        <img class="pdf-logo" height="106" width="662.88" src="../../../../../assets/images/shipvagoo_dark.png" />
    </div>
    <Invoice v-if="receipt_data" :invoiceToPrint="receipt_data"></Invoice>
</template>

<script>
import { getDateFormat } from "../../../../../helpers"
import { loadingService, merchantService } from "@/services/_singletons"
import PaymentReceipt from "@/components/previews/payments/PaymentReceipt.vue"
import { useMainStore } from "@/stores/main"
import { danishNumberFormat } from "@/helpers"
import Invoice from "@/components/pdfs/Invoice.vue"
import dayjs from "dayjs"
import HelperService from "../../../../../services/helper.service"
import { useHelper } from "../../../../../composeable/Helper"

const { downloadTransactionPdf } = useHelper()
export default {
    name: "Invoices",
    components: { Invoice, PaymentReceipt },
    methods: {
        danishNumberFormat,
        async showReceipt(transaction) {
            await downloadTransactionPdf(transaction.transaction_code, transaction.transaction_type)
        },
        search(value) {
            this.getTransactions(1, this.perPage, value)
        },
        async showInvoice(invoice) {
            await downloadTransactionPdf(invoice.transaction_code, "invoice")
        },
        handleSizeChange(size) {
            this.getTransactions(this.currentPage, size)
        },
        changeCurrentPage(page) {
            this.getTransactions(page, this.perPage)
        },
        getDateFormat,
        async getTransactions(page = 1, perPage = 10, filter = "") {
            this.loading = true
            try {
                const params = {
                    types: "invoice",
                    customerId: this.customerId,
                    page: page,
                    perPage: perPage,
                    filter: filter
                }
                const res = await merchantService.getMerchantTransactions(params)
                this.currentPage = res.data.currentPage
                this.lastPage = res.data.lastPage
                this.perPage = res.data.perPage
                this.total = res.data.totalRecords
                this.invoices = res.data.invoice
                this.loading = false
            } catch (e) {
                console.log("ERROR ", e)
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                } else if (e.response.status === 500) {
                    this.$notify.error({
                        title: "Error",
                        message: e.message ?? e.message,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0] ?? e.response.data.error,
                    position: "top-right"
                })
                this.loading = false
            } finally {
                loadingService.stopLoading("main-loader:login")
                this.loading = false
            }
        }
    },
    data() {
        return {
            invoices: [],
            loading: false,
            currentPage: 1,
            lastPage: 0,
            perPage: 10,
            total: 0,
            receipt_data: null,
            merchant: {},
            view_transaction_dialog: false,
            dayjs,
            HelperService
        }
    },
    props: ["customerId"],
    created() {
        this.getTransactions(this.currentPage, this.perPage)
    },
    mounted() {
        this.merchant = useMainStore().getMerchant
    },
    setup(props) {
        return {
            customerId: props.customerId
        }
    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
    margin-bottom: 11px;
}

.Font-Size1 {
    font-size: 14px !important;
}

.seacr-box {
    width: 58rem;
    max-width: 100%;
    padding: 10px 30px;
    border-radius: 5px;
    border: 1px solid #eeeeee;
    outline: none;
}

.mdi {
    padding-top: 2px;
    padding-left: 4px;
    color: #bbbbbb !important;
}

.pdf-assets {
    height: 0;
    overflow: hidden;
}
</style>
