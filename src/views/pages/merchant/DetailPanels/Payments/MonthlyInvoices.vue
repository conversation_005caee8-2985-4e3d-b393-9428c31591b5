<template>
    <div v-if="!viewSpeicifications">
        <div class="search-container">
            <search-box @search="search"></search-box>
        </div>
        <div class="page-table scrollable only-y" id="affix-container">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Created At</th>
                            <th>Invoice Number</th>
                            <th>Month</th>
                            <th>Year</th>
                            <th class="text-right">Total Amount incl.VAT</th>
                            <th class="text-right">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(invoice, index) in invoices" :key="index">
                            <td>{{ dayjs(String(invoice.created_at)).format("DD/MM/YYYY HH:mm:ss") }}</td>
                            <td class="cursor-pointer text-blue">
                                <a href="#" @click="showInvoice(invoice)">
                                    {{ HelperService.formatId(invoice.id) }}
                                </a>
                            </td>
                            <td>
                                {{ dayjs(invoice.created_at).subtract(1, "month").format("MMMM") }}
                            </td>
                            <td>{{ dayjs(invoice.created_at).subtract(1, "month").format("YYYY") }}</td>

                            <td class="text-right">
                                DKK {{ invoice.amount ? danishNumberFormat(invoice.amount) : 0 }}
                            </td>
                            <td class="text-right">
                                <el-dropdown trigger="hover" @command="onCommand">
                                    <span class="el-dropdown-link">
                                        <i class="mdi mdi-dots-vertical"></i>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item @click="onClickViewSpecifications(invoice)"
                                                >View specifications</el-dropdown-item
                                            >
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination
                    class="flex"
                    ref="pgi"
                    v-if="!loading"
                    small="false"
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :current-page="currentPage"
                    v-model:page-size="perPage"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, ->, sizes, prev, pager, next, jumper"
                    :total="total"
                ></el-pagination>
            </div>
        </div>
    </div>
    <div v-else>
        <el-button class="themed" type="primary" plain @click="viewSpeicifications = false">Go back </el-button>
        <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="specificationsLoading">
            <div class="flex justify-space-between full-width">
                <h3>Shipments</h3>
                <el-button type="primary" plain @click="onClickExport"> Export Excel </el-button>
            </div>
            <div class="table-wrapper">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Date</th>
                            <th>Description</th>
                            <th>Reference</th>
                            <th class="text-right">Amount (excl. VAT)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for="(shipment, index) in shipments" :key="`shipment_title_${index}`">
                            <tr>
                                <td colsppan="4">
                                    <strong>({{ shipment.title }})</strong>
                                </td>
                            </tr>

                            <tr v-for="(invoice, iIndex) in shipment.data" :key="`shipment_row_${index}_${iIndex}`">
                                <td>
                                    {{ dayjs(String(invoice.date)).format("DD/MM/YYYY HH:mm:ss") }}
                                </td>

                                <td>
                                    {{ invoice.description }}
                                </td>
                                <td>
                                    {{ invoice.reference }}
                                </td>
                                <td class="text-right">
                                    DKK {{ invoice.amount ? danishNumberFormat(invoice.amount) : 0 }}
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <h3>Additionals</h3>
            <div class="table-wrapper">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Date</th>
                            <th>Description</th>
                            <th>Reference</th>
                            <th class="text-right">Amount (excl. VAT)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(additional, iIndex) in additionals" :key="`additional_row_${iIndex}`">
                            <td>{{ dayjs(String(additional.date)).format("DD/MM/YYYY HH:mm:ss") }}</td>
                            <td>{{ additional.description }}</td>
                            <td>{{ additional.reference }}</td>
                            <td class="text-right">
                                DKK {{ additional.amount ? danishNumberFormat(additional.amount) : 0 }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination
                    class="flex"
                    ref="pgi"
                    v-if="!specificationsLoading"
                    small="false"
                    @current-change="changeShipmentCurrentPage"
                    @size-change="handleShipmentPaginationSizeChange"
                    :current-page="currentPage"
                    v-model:page-size="perPage"
                    :page-sizes="[10,100, 500, 100]"
                    layout="total, ->, sizes, prev, pager, next, jumper"
                    :total="total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import { getDateFormat } from "../../../../../helpers"
import { loadingService, merchantService } from "@/services/_singletons"
import PaymentReceipt from "@/components/previews/payments/PaymentReceipt.vue"
import { useMainStore } from "@/stores/main"
import { danishNumberFormat } from "@/helpers"
import Invoice from "@/components/pdfs/Invoice.vue"
import dayjs from "dayjs"
import HelperService from "../../../../../services/helper.service"
import { useHelper } from "../../../../../composeable/Helper"

const { downloadMonthlyInvoicePdf } = useHelper()
export default {
    name: "MonthlyInvoices",
    components: { Invoice, PaymentReceipt },
    data() {
        return {
            invoices: [],
            loading: false,
            currentPage: 1,
            lastPage: 0,
            perPage: 10,
            total: 0,
            receipt_data: null,
            merchant: {},
            view_transaction_dialog: false,
            dayjs,
            HelperService,
            viewSpeicifications: false,
            specifications: [],
            specificationsLoading: false,
            selectedInvoice: null,
            shipments: [],
            additionals: []
        }
    },
    methods: {
        danishNumberFormat,
        onClickViewSpecifications(row) {
            this.viewSpeicifications = true
            this.getMonthlSpecifications(row)
            this.selectedInvoice = row
        },
        search(value) {
            this.getTransactions(1, this.perPage, value)
        },
        async showInvoice(invoice) {
            this.loading = true
            await downloadMonthlyInvoicePdf(invoice.id)
            this.loading = false
        },
        handleSizeChange(size) {
            this.getTransactions(this.currentPage, size)
        },
        changeCurrentPage(page) {
            this.getTransactions(page, this.perPage)
        },
        getDateFormat,
        async getTransactions(page = 1, perPage = 10, filter = "") {
            this.loading = true
            try {
                const params = {
                    customerId: this.customerId,
                    page: page,
                    perPage: perPage,
                    filter: filter
                }
                const res = await merchantService.getMonthlyInvoices(params)
                this.currentPage = res.data.current_page
                this.lastPage = res.data.last_page
                this.perPage = res.data.per_page
                this.total = res.data.total
                this.invoices = res.data.invoices
                this.loading = false
            } catch (e) {
                console.log("ERROR ", e)
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                } else if (e.response.status === 500) {
                    this.$notify.error({
                        title: "Error",
                        message: e.message ?? e.message,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0] ?? e.response.data.error,
                    position: "top-right"
                })
                this.loading = false
            } finally {
                loadingService.stopLoading("main-loader:login")
                this.loading = false
            }
        },

        handleShipmentPaginationSizeChange(size) {
            this.getMonthlSpecifications(this.selectedInvoice, this.currentPage, size)
        },
        changeShipmentCurrentPage(page) {
            this.getMonthlSpecifications(this.selectedInvoice, page, this.perPage)
        },
        async getMonthlSpecifications(invoice, page = 1, perPage = 1000) {
            const params = {
                invoiceId: invoice.id,
                page: page,
                perPage: perPage
            }
            try {
                this.specificationsLoading = true
                const data = await merchantService.getMonthlyInvoiceSpecifications(params)
                console.log("Data ",data);
                this.currentPage = data.current_page
                this.lastPage = data.last_page
                this.perPage = data.per_page
                this.total = data.total

                this.hydrateTheShipmentsData(data.data)
            } finally {
                this.specificationsLoading = false
            }
        },
        hydrateTheShipmentsData(invoices) {
            this.shipments = Object.values(
                invoices.reduce((acc, invoice) => {
                    const title = invoice.description ?? "No description"

                    if (!acc[title]) {
                        acc[title] = { title, data: [] }
                    }
                    const shipment = invoice.invoice_items.find(item => item.type == "Shipments")?.items[0]
                    let amount = "0,00"
                    let weight_class = ""
                    if (shipment) {
                        amount = danishNumberFormat(Number(shipment.unit_price).toFixed(2))
                        weight_class = shipment.weight_class
                    }
                    acc[title].data.push({
                        reference: `Shipment ${invoice.shipment_code}`,
                        description: weight_class,
                        date: invoice.created_at,
                        amount: amount
                    })
                    return acc
                }, {})
            )

            this.additionals = invoices.flatMap(invoice => {
                const additionalServices = invoice.invoice_items
                    .filter(item => item.type !== "Shipments")
                    .flatMap(item => item.items || [])
                return additionalServices.map(ad_item => ({
                    reference: `Shipment ${invoice.shipment_code}`,
                    description: ad_item.name,
                    date: invoice.created_at,
                    amount: danishNumberFormat(Number(ad_item.unit_price).toFixed(2))
                }))
            })
        },
        onClickExport() {
            window.location.href = `${import.meta.env.VITE_baseUrl}admin/monthly-invoice/download/${
                this.selectedInvoice.id
            }`
        }
    },
    props: ["customerId"],
    created() {
        this.getTransactions(this.currentPage, this.perPage)
    },
    mounted() {
        this.merchant = useMainStore().getMerchant
    },
    setup(props) {
        return {
            customerId: props.customerId
        }
    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
    margin-bottom: 11px;
}

.Font-Size1 {
    font-size: 14px !important;
}

.seacr-box {
    width: 58rem;
    max-width: 100%;
    padding: 10px 30px;
    border-radius: 5px;
    border: 1px solid #eeeeee;
    outline: none;
}

.mdi {
    padding-top: 2px;
    padding-left: 4px;
    color: #bbbbbb !important;
}

.pdf-assets {
    height: 0;
    overflow: hidden;
}

.table-wrapper {
    max-height: 300px !important;
    overflow-y: auto;
    //  border: 1px solid #ccc;
}

/* Optional: Ensure table takes full width */
.table-wrapper table {
    width: 100%;
    border-collapse: collapse;
}

/* Make the header sticky */
.table-wrapper thead th {
    position: sticky;
    top: 0;
    background-color: #fff; /* match table bg */
    z-index: 2;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
}
</style>
