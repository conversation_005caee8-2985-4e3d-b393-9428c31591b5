<template>
    <div>
        <div class="search-container">
            <search-box @search="search"></search-box>
        </div>
        <div class="page-table scrollable only-y" id="affix-container">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Created At</th>
                            <th>Transaction ID</th>
                            <th>Comments</th>
                            <th class="text-right">Amount</th>
                            <th>Payment Method</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(fund, index) in funds">
                            <td>{{ getDateFormat(fund.created_at) }}</td>
                            <td class="cursor-pointer text-blue">
                                <a href="#" @click="showReceipt(fund)"> {{ fund.transaction_code }}</a>
                            </td>
                            <td>{{ fund.description ?? "N/A" }}</td>
                            <td class="text-right" style="white-space: nowrap">
                                DKK {{ fund.sign }}{{ danishNumberFormat(fund.amount) }}
                            </td>
                            <td>{{ showPaymentMethod(fund.payment_method) }}</td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination
                    class="flex"
                    ref="pgi"
                    v-if="!loading"
                    small="false"
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :current-page="currentPage"
                    v-model:page-size="perPage"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, ->,sizes, prev, pager, next, jumper"
                    :total="total"
                ></el-pagination>
            </div>
        </div>
    </div>
    <div class="pdf-assets" aria-hidden>
        <img class="pdf-logo" height="106" width="662.88" src="../../../../../assets/images/shipvagoo_dark.png" />
    </div>
    <PaymentReceipt v-if="receipt_data" :transactionToPrint="receipt_data"></PaymentReceipt>
</template>

<script>
import { getDateFormat } from "../../../../../helpers"
import { useHelper } from "../../../../../composeable/Helper"
import { loadingService, merchantService } from "@/services/_singletons"
import { useMainStore } from "@/stores/main"
import PaymentReceipt from "@/components/previews/payments/PaymentReceipt.vue"
import { danishNumberFormat } from "@/helpers"

const { downloadTransactionPdf } = useHelper()
export default {
    name: "Funds",
    components: { PaymentReceipt },
    data() {
        return {
            funds: [],
            loading: false,
            currentPage: 1,
            lastPage: 0,
            perPage: 10,
            total: 0,
            view_transaction_dialog: false,
            receipt_data: null,
            visible: false
        }
    },
    methods: {
        danishNumberFormat,
        showPaymentMethod(paymentMethod) {
            if (paymentMethod == null) {
                return "Balance"
            } else {
                if (paymentMethod.type == "balance") {
                    return "Balance"
                } else if (paymentMethod.type == "card") {
                    const details = paymentMethod.details
                    return details
                        ? `${details.brand.charAt(0).toUpperCase() + details.brand.slice(1)} (${details.bin.substring(
                              0,
                              4
                          )} **** **** ${details.last4})`
                        : "Card"
                } else {
                    return "Balance" //this for old transactions.
                }
            }
        },
        search(value) {
            this.getTransactions(this.currentPage, this.perPage, value)
        },
        async showReceipt(transaction) {
            await downloadTransactionPdf(transaction.transaction_code, transaction.transaction_type)
        },
        getDateFormat,
        handleSizeChange(size) {
            this.getTransactions(this.currentPage, size)
        },
        changeCurrentPage(page) {
            this.getTransactions(page, this.perPage)
        },
        async getTransactions(page = 1, perPage = 3, filter = "") {
            this.loading = true
            try {
                const params = {
                    types: "funds",
                    customerId: this.customerId,
                    page: page,
                    perPage: perPage,
                    filter: filter
                }
                const res = await merchantService.getMerchantTransactions(params)
                console.log(res.data)
                this.currentPage = res.data.currentPage
                this.lastPage = res.data.lastPage
                this.perPage = res.data.perPage
                this.total = res.data.totalRecords
                this.funds = res.data.funds
                this.loading = false
            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                } else if (e.response?.status === 500) {
                    this.$notify.error({
                        title: "Error",
                        message: e.message ?? e.message,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0] ?? e.response.data.error,
                    position: "top-right"
                })
                this.loading = false
            } finally {
                loadingService.stopLoading("main-loader:login")
                this.loading = false
            }
        }
    },
    created() {
        this.getTransactions(this.currentPage, this.perPage)
    },
    mounted() {
        this.merchant = useMainStore().getMerchant
    },
    props: ["customerId"],
    setup(props) {
        return {
            customerId: props.customerId
        }
    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
    margin-bottom: 11px;
}

.Font-Size1 {
    font-size: 14px !important;
}

.pdf-assets {
    height: 0;
    overflow: hidden;
}
</style>
