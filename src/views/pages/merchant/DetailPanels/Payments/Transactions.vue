<template>
    <div>
        <div class="search-container">
            <search-box @search="search"></search-box>
        </div>
        <div class="page-table scrollable only-y" id="affix-container">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Created At</th>
                            <th>Transaction ID</th>
                            <th>Comments</th>
                            <th class="text-right" style="white-space: nowrap">Amount (incl. VAT)</th>
                            <th style="text-align: right">Balances</th>
                            <th>Payment Type</th>
                            <th>Payment Method</th>
                            <th>Payment Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr :key="index" v-for="(transaction, index) in transactions">
                            <td>{{ getDateFormat(transaction.created_at) }}</td>
                            <td class="cursor-pointer text-blue">
                                <a href="#" @click="showReceipt(transaction)"> {{ transaction.transaction_code }}</a>
                            </td>
                            <td>{{ transaction.description ?? "N/A" }}</td>
                            <td style="white-space: nowrap; text-align: right">
                                DKK {{ transaction.sign }}{{ danishNumberFormat(transaction.amount) }}
                            </td>
                            <td style="white-space: nowrap; text-align: right">{{ showBalance(transaction) }}</td>
                            <td>{{ transaction.transaction_type }}</td>
                            <td class="text-center">{{ showPaymentMethod(transaction.payment_method,
                                transaction.balance) }}</td>

                            <td v-if="transaction.transaction_status == 'PROCESSING'">
                                <a id="btn-processing">{{ transaction.transaction_status }}</a>
                            </td>
                            <td v-else>
                                <a id="btn-completed">{{ transaction.transaction_status }}&nbsp</a>
                            </td>
                            <td>
                                <el-dropdown trigger="hover" @command="onCommand">
                                    <span class="el-dropdown-link">
                                        <i class="mdi mdi-dots-vertical"></i>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item>Create Credit Note</el-dropdown-item>
                                            <el-dropdown-item>Fine Imposed</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination class="flex" ref="pgi" v-if="!loading" small="false" @current-change="changeCurrentPage"
                    @size-change="handleSizeChange" :current-page="currentPage" v-model:page-size="perPage"
                    :page-sizes="[10, 20, 50, 100]" layout="total, ->,sizes, prev, pager, next, jumper"
                    :total="total"></el-pagination>
            </div>
        </div>
        <!--<el-dialog class="cai-dialog-wrapper" title="" v-model="view_transaction_dialog">
            <PaymentReceipt :data="receipt_data"></PaymentReceipt>
        </el-dialog>-->
    </div>
    <!-- <div class="pdf-assets" aria-hidden>
         <img class="pdf-logo" height="106" width="662.88" src="../../../../../assets/images/shipvagoo_dark.png"/>
     </div>
     <Invoice v-if="receipt_data" :invoiceToPrint="receipt_data"></Invoice>-->
</template>

<script>
import { getDateFormat } from "../../../../../helpers"
import { loadingService, merchantService, templateService } from "../../../../../services/_singletons"
import PaymentReceipt from "@/components/previews/payments/PaymentReceipt.vue"
import { useMainStore } from "@/stores/main"
import { danishNumberFormat } from "@/helpers"
import Invoice from "@/components/pdfs/Invoice.vue"
import { useHelper } from "../../../../../composeable/Helper"

const { downloadTransactionPdf } = useHelper()
let windowRef
export default {
    name: "Transactions",
    components: { PaymentReceipt, Invoice },
    props: ["customerId"],
    data() {
        return {
            transactions: [],
            loading: false,
            currentPage: 1,
            lastPage: 0,
            perPage: 10,
            total: 0,
            view_transaction_dialog: false,
            receipt_data: null,
            merchant: {},
            footer: null
        }
    },
    created() {
        this.getTransactions(this.currentPage, this.perPage)
    },
    mounted() {
        //this.getMerchant();
        this.merchant = useMainStore().getMerchant
        this.getFooter()
    },
    watch: {
        currentPage(newPage, oldPage) { }
    },
    methods: {
        danishNumberFormat,
        getDateFormat,
        showBalance(transaction) {
            return `DKK ${this.danishNumberFormat(transaction.balance)}`
            /*if (transaction.payment_method) {
                  return `DKK ${this.danishNumberFormat(transaction.balance)}`
                   /!* if (transaction.payment_method.type == 'balance' ||
                        transaction.payment_type == 'ADD_FUNDS') {
                        return `DKK ${this.danishNumberFormat(transaction.balance)}`
                    } else {
                        return ""
                    }*!/
                } else {
                    return `DKK ${this.danishNumberFormat(transaction.balance)}`
                }*/
        },
        showPaymentMethod(paymentMethod, balance) {
            console.log(balance);

            if (paymentMethod == null) {
                return "Balance"
            } else {
                if (paymentMethod.type == "balance") {
                    return balance > 0 ? "Balance" : "Credit"
                } else if (paymentMethod.type == "card") {
                    const details = paymentMethod.details
                    return details
                        ? `${details.brand.charAt(0).toUpperCase() + details.brand.slice(1)} (${details.bin.substring(
                            0,
                            4
                        )} **** **** ${details.last4})`
                        : "Card"
                } else {
                    return balance > 0 ? "Balance" : "Credit"

                }
            }
        },
        search(value) {
            this.getTransactions(this.currentPage, this.perPage, value)
        },
        async getFooter() {
            const res = await templateService.getTemplateSummary({ type: "FOOTER" })
            if (res.data.template) {
                this.footer = res.data.template.summary
            }
        },
        async getMerchant() {
            const res = await merchantService.getMerchantSummaryDetails(this.customerId)
            this.user = res.data.user
        },
        async showReceipt(transaction) {
            await downloadTransactionPdf(transaction.transaction_code, transaction.transaction_type)
        },

        onCommand(path) {
            if (path !== "skip") this.$router.push(path)
        },
        handleSizeChange(size) {
            console.log("handle size", size)
            this.perPage = size
            this.getTransactions(this.currentPage, size)
        },
        changeCurrentPage(page) {
            console.log("current page ", page)
            this.getTransactions(page, this.perPage)
        },
        async getTransactions(page = 1, perPage = 3, query = "") {
            this.loading = true
            try {
                const params = {
                    types: "transactions",
                    customerId: this.customerId,
                    page: page,
                    perPage: perPage,
                    filter: query
                }
                const res = await merchantService.getMerchantTransactions(params)
                this.currentPage = res.data.currentPage
                this.lastPage = res.data.lastPage
                this.perPage = res.data.perPage
                this.total = res.data.totalRecords
                this.transactions = res.data.transactions
                this.loading = false
            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                } else if (e.response?.status === 500) {
                    this.$notify.error({
                        title: "Error",
                        message: e.message ?? e.message,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0] ?? e.response.data.error,
                    position: "top-right"
                })
                this.loading = false
            } finally {
                loadingService.stopLoading("main-loader:login")
                this.loading = false
            }
        }
    },
    /*created() {
          const unwatch = this.$watch('allTransactions',(transactions)=>{
            console.log(transactions,'ddd')
          })
        },*/
    setup(props) {
        return {
            customerId: props.customerId
        }
    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
    margin-bottom: 11px;
}

.Font-Size1 {
    font-size: 14px !important;
}

#btn-processing {
    padding: 10px;
    background-color: rgb(236, 239, 243);
    color: rgb(55, 120, 240);
    cursor: default;
    text-decoration: none;
}

#btn-completed {
    padding: 10px;
    background-color: rgb(229, 245, 222);
    color: rgb(46, 179, 46);
    cursor: default;
    text-decoration: none;
}

.pdf-assets {
    height: 0;
    overflow: hidden;
}
</style>
