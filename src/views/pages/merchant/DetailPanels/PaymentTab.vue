<template>
    <div class="payments">
        <el-tabs class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="Transactions">
                <Transactions v-if="paymentTab === 'Transactions'" :customerId="customerId" />
            </el-tab-pane>
            <el-tab-pane label="Funds">
                <Funds v-if="paymentTab === 'Funds'" :customerId="customerId" />
            </el-tab-pane>
            <el-tab-pane label="Monthly Invoices">
                <MonthlyInvoices v-if="paymentTab === 'Monthly Invoices'" :customerId="customerId" />
            </el-tab-pane>
            <el-tab-pane label="Billing">
                <BillingInvoices v-if="paymentTab === 'Billing'" :customerId="customerId" />
            </el-tab-pane>
            <el-tab-pane label="Invoices">
                <Invoices v-if="paymentTab === 'Invoices'" :customerId="customerId" />
            </el-tab-pane>
            <el-tab-pane label="Credit Notes"> </el-tab-pane>
            <el-tab-pane label="Fine"> </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import Transactions from "@/views/pages/merchant/DetailPanels/Payments/Transactions.vue"
import Funds from "@/views/pages/merchant/DetailPanels/Payments/Funds.vue"
import Invoices from "@/views/pages/merchant/DetailPanels/Payments/Invoices.vue"
import BillingInvoices from "@/views/pages/merchant/DetailPanels/Payments/BillingInvoices.vue"
import MonthlyInvoices from "@/views/pages/merchant/DetailPanels/Payments/MonthlyInvoices.vue"
export default {
    name: "PaymentTab",
    components: {
        Transactions,
        Funds,
        Invoices,
        MonthlyInvoices,
        BillingInvoices
    },
    props: {
        customerId: {
            type: String,
            required: true
        }
    },
    setup(props) {
        return {
            customerId: props.customerId
        }
    },
    data() {
        return {
            paymentTab: "Transactions",
            result: {}
        }
    },
    beforeMount() {
        // this.getTransactions();
        //   this.paymentTab = this.$route.query.subtab || 'Transactions';
    },
    methods: {
        handleClick(tab, event) {
            this.paymentTab = tab.props.label

            this.$router.push({
                /*  query: {
                    ...this.$route.query, 
                    subtab: this.paymentTab 
                } */
            })
        }
    }
}
</script>

<style scoped>
.demo-tabs > .el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}
</style>
