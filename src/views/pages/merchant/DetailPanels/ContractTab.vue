<template>
  <div class="search-container">
    <search-box @search=""></search-box>
  </div>
  <div class="page-table scrollable only-y" id="affix-container">
    <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
      <table class="styled striped">
        <thead>
        <tr class="Font-Size1">
          <th>ID</th>
          <th>Name</th>
          <th>Designation</th>
          <th>Email</th>
          <th>Phone</th>
        </tr>
        </thead>
        <tbody>
        <tr v-if="lists !==undefined && lists.length>0" v-for="item in lists" :key="item.id">
          <td>{{ item.id }}</td>
          <td>{{ item.name }}</td>
          <td>{{ item.designation??'N/A' }}</td>
          <td>{{ item.email }}</td>
          <td>{{ item.mobile }}</td>
        </tr>
        <tr v-else>
          <td colspan="5">Contract not found</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import Affix from "@/components/Affix.vue"
import {defineComponent} from "@vue/runtime-core"
import {loadingService, merchantService} from "@/services/_singletons";
import {ElLoading} from "element-plus";

export default defineComponent({
  name: "ContractTab",
  props: {
    customerId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return {
      customerId: props.customerId
    }
  },
  data() {
    return {
      affixEnabled: true,
      lists: [],
      loading:false
    }
  },
  components: {
    Affix
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
     this.loading=true
      try {
        const res = await merchantService.getMerchantContractTab(this.customerId)
        this.lists = res.data.contacts
        this.loading=false
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          this.loading=false
          return
        }else if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.message ?? e.message,
            position: "top-right"
          })
          this.loading = false
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        this.loading=false
      } finally {
        loadingService.stopLoading("main-loader:login")
        this.loading=false
      }
    }
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
  padding: 0% !important;
  margin-top: 2% !important;
  padding-right: 15px;
  padding-bottom: 20px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
}

.table-box {
  overflow: auto;
}

.Font-Size1 {
  font-size: 14px !important;
}

.seacr-box {
  width: 58rem;
  max-width: 100%;
  padding: 10px 30px;
  border-radius: 5px;
  border: 1px solid #EEEEEE;
  outline: none;
}

.mdi {
  padding-top: 2px;
  padding-left: 4px;
  color: #BBBBBB !important;
}
</style>
