<template>
  <div>
    <div class="mws-scroller" v-if="user !== null" style="margin-bottom: 30px;">
      <div class="page-table scrollable only-y" id="affix-container">
        <div class="page-header">
          <h1 class="User-Name">{{ user.company_name }}</h1>
        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-10 font-weight-700">Company Url:</div>
          <div class="col-4 import-info1 mt-10"><a :href="user.company_url">{{ user.company_url }}</a></div>
          <div class="col-6"></div>
        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-5 font-weight-700">Address:</div>
          <div class="col-4 import-info1 mt-5">{{ user.address ?? 'N/A' }}</div>
          <div class="col-2 import-info mt-5 font-weight-700">Person Name:</div>
          <div class="col-4 import-info1 mt-5">{{ user.person_name }}</div>

        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-5 font-weight-700">Zip Code:</div>
          <div class="col-4 import-info1 mt-5">{{ user.zip_code }}</div>
          <div class="col-2 import-info mt-5 font-weight-700">Phone:</div>
          <div class="col-4 import-info1 mt-5">{{ user.phone ?? 'N/A' }}</div>
        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-5 font-weight-700">City:</div>
          <div class="col-4 import-info1 mt-5">{{ user.city ?? 'N/A' }}</div>
          <div class="col-2 import-info mt-5 font-weight-700">Email:</div>
          <div class="col-4 import-info1 mt-5">{{ user.email }}</div>
        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-5 font-weight-700">Country:</div>
          <div class="col-4 import-info1 mt-5">{{ user.country ?? 'N/A' }}</div>
          <div class="col-2 import-info mt-5 font-weight-700">Platform:</div>
          <div class="col-4 import-info1 mt-5">Shopify</div>
        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-5 font-weight-700">CVR:</div>
          <div class="col-4 import-info1 mt-5">{{ user.cvr }}</div>
          <div class="col-2 import-info mt-5 font-weight-700">Platform URL:</div>
          <div class="col-4 import-info1 mt-5"><a>{{ user.platform_url }}</a></div>
        </div>
        <div class="flex gaps grid-12">
          <div class="col-2 import-info mt-5 font-weight-700">Summary:</div>
          <div style="margin-left: -8px" class="col-10 import-info mt-5">{{ user.summary ?? '' }}</div>

        </div>
        <div style="height: 1px; background-color: #EEEEEE; margin-top: 2% ; margin-bottom: 2%;">
        </div>
      </div>
      <div class="flex gaps grid-12">
        <div class="col-6 page-header">
          <h4>Old Agreement</h4>
        </div>
        <div class="col-6 page-header">
          <h4>Timeline</h4>
        </div>
      </div>

      <div class="flex gaps grid-12">
        <div class="col-6"
             style="width: 518px; height: 315px; overflow-y: auto; box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25); border-radius:5px;">
          <Tables :agreements="user.current_agreement"/>
        </div>

        <div class="col-6"
             style="width: 518px; height: 315px;  overflow-y: auto; box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25); border-radius:5px;">
          <Timeline :logs="logs"/>
        </div>
      </div>

      <div style="margin-top: 15px" class="SummaryTabs">
        <!--        <div class="flex gaps grid-12">-->
        <div class="row">
          <div class="col-md-12">
            <div class="import-info">Summary:</div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="paragraph-imp">{{ user.agreement_summary ?? '' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline.vue";
import Tables from "@/components/Tables.vue";
import {ref, watch} from 'vue';

export default {
  name: "SummaryTab",
  components: {
    Timeline,
    Tables
  },
  props: {
    user: {
      type: String,
      required: true
    },
    logs: {
      logs: {
        type: Object,
        required: true
      }
    }
  },
  setup(props) {
    const user = ref(props.user);
    const logs = ref(props.logs);
    watch(() => props.user, (currentValue, oldValue) => {
      user.value = currentValue;
    });
    watch(() => props.logs, (currentValue, oldValue) => {
      console.log("Log watcher...")
      logs.value = currentValue;
    });
    return {
      user, logs
    }
  }
}
</script>

<style scoped>
.mws-scroller {
  /* position: relative;
   height: calc(100vh) !important;*/
}
</style>