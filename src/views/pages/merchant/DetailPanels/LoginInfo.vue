<template>
  <div class="scrollable only-y p-2">
    <div class="card-base card-shadow--medium p-15">
      <div class="bb-bt-br">
        <div class="search-card scrollable only-y mt-2">
          <el-form label-width="130px">
            <Form class="form-box" @submit="onSubmit" @submit.prevent>
              <el-col class="flex" :span="24">
                <el-form-item label="Login Email">
                  <div class="el-input">
                    <el-input disabled v-model="user.email" clearable style="width: 250px;"></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-form-item>
                <button class="el-button el-button--submit btn-blue-bg" type="submit">Send Reset Password Link</button>
              </el-form-item>
            </Form>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Affix from "@/components/Affix.vue"
import {defineComponent} from "@vue/runtime-core"
import {loadingService, merchantService, ubsendService} from "@/services/_singletons";
import {ElLoading} from "element-plus";

export default defineComponent({
  name: "LoginInfo",
  props: {
    user: {
      type: Object,
      required: true
    },
    customerId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return {
      user: props.user,
      customerId: props.customerId
    }
  },
  data() {
    return {
      width: 0,
      email: this.email,
      height: "auto",
      affixEnabled: true,
      list: [],
      loading: false
    }
  },
  components: {
    Affix
  },
  created() {
    //this.init(),
    this.email = this.user.email
  },
  methods: {
    async onSubmit(values) {
      this.isDisabled = true
      try {
        const res = await merchantService.resetPassword({user_code: this.customerId})
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
      } catch (e) {
        this.isDisabled = false
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.message,
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
        // notificationService.showError('Error during login');
      } finally {
        //loadingService.stopLoading("main-loader:login")
      }
    }
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
  padding: 0% !important;
  margin-top: 2% !important;
  padding-right: 15px;
  padding-bottom: 20px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
}

.table-box {
  overflow: auto;
}

.Font-Size1 {
  font-size: 14px !important;
}

.seacr-box {
  width: 58rem;
  max-width: 100%;
  padding: 10px 30px;
  border-radius: 5px;
  border: 1px solid #EEEEEE;
  outline: none;
}

.mdi {
  padding-top: 2px;
  padding-left: 4px;
  color: #BBBBBB !important;
}

</style>
