<template>
    <div>
        <!-- <div class="search-container">
          <i class="mdi mdi-magnify"></i>
          <input class="seacr-box" type="text" placeholder="Search.." name="search">
        </div> -->
        <div class="flex mt-20">
            <div class="col-10">
            </div>
            <div class="col-2 flex justify-flex-end">
                <el-button class="btn-blue-bg"
                           :disabled="disabled"
                           @click="createAgreement(merchantData,false)">Create
                </el-button>
            </div>
        </div>
        <div class="page-table scrollable only-y" id="affix-container">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                    <tr class="Font-Size1">
                        <th>Agreement No</th>
                        <th>Created Date</th>
                        <th>Sent Date</th>
                        <th>Onboarding Date</th>
                        <th>Onboarding Status</th>
                        <th>Revised Date</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-if="Object.keys(list).length>0" :key="list.agreement_no">
                        <td><a
                                v-loading.fullscreen.lock="fullscreenLoading"
                                href="#" @click="getInvoiceData(merchantData)"> {{ list.agreement_no }}</a></td>
                        <td>{{ list.created_date }}</td>
                        <td>{{ list.sent_date ?? '--' }}</td>
                        <td>{{ list.onboarding_date ?? '--' }}</td>
                        <td>
                            <span v-if="list.onboarding_status ===0" plain>....</span>
                            <el-button v-if="list.onboarding_status ===1" type="info" plain>Draft</el-button>
                            <el-button v-if="list.onboarding_status ===2" type="primary" plain>Pending</el-button>
                            <el-button v-if="list.onboarding_status ===3" type="success" plain>Completed</el-button>

                        </td>
                        <td>{{ list.updated_date ?? '--' }}</td>
                        <td>
                            <el-dropdown trigger="hover">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                                @click="createAgreement(merchantData,true)"
                                                :command="`/customer/update-customer-agreement/${list.agreement_id}`"
                                                divided>
                                            Update Agreement
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                                v-if="!list.is_email_sent && (list.onboarding_status===1 || list.onboarding_status===2)"
                                                @click="sendEmail">
                                            Send Onboarding Email
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                                v-if="list.is_email_sent && (list.onboarding_status===1 || list.onboarding_status===2)"
                                                @click="sendEmail"
                                                divided>
                                            {{
                                            list.onboarding_status == 1 ? 'Send Onboarding Email' : 'Resend Onboarding Email' }}
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </td>
                    </tr>
                    <tr v-else>
                        <td style="color: aliceblue;"></td>
                        <td style="text-align: center;" colspan="7">Agreement not founds</td>
                    </tr>
                    </tbody>
                </table>


            </div>
        </div>
        <el-dialog class="cai-dialog-wrapper" title="" v-model="dialogTableVisible">
            <CustomerAgreementInvoiceDialog :data="loadPrintData" :detail="printDetail"/>
        </el-dialog>
    </div>
</template>

<script>
    import Affix from "@/components/Affix.vue"

    import {defineComponent} from "@vue/runtime-core"

    import {
        merchantService,
        loadingService
    } from "../../../../services/_singletons"
    import {ref} from 'vue'
    import {ElLoading} from "element-plus";
    import moment from "moment";
    // import ResizeObserver from "@/components/vue-resize/ResizeObserver";
    import CustomerAgreementInvoiceDialog from "../../agreement/CustomerAgreementInvoiceDialog.vue";
    import {useMainStore} from "@/stores/main";
    import {useHelper} from "../../../../composeable/Helper";

    const {downloadAgreementPdf} = useHelper();
    export default {
        name: "AgreementTab",
        props: {
            customerId: {
                type: String,
                required: true
            }
        },
        setup(props) {
            return {
                customerId: props.customerId
            }
        },
        data() {
            return {
                fullscreenLoading: false,
                countryList: [],
                printDetail: {},
                merchantData: {},
                dialogTableVisible: false,
                printData: [],
                affixEnabled: true,
                list: {},
                loading: false,
                merchantChecked: [
                    {
                        label: 'Not Started',
                        value: 0
                    },
                    {
                        label: 'Draft',
                        value: 1
                    },
                    {
                        label: 'Pending',
                        value: 2
                    },
                    {
                        label: 'Completed',
                        value: 3
                    },
                    {
                        label: 'All',
                        value: 4
                    }
                ],
                disabled: true,
                footer: null
            }
        },
        computed: {
            loadPrintData() {
                return this.printData
            },
        },
        components: {
            Affix,
            CustomerAgreementInvoiceDialog
        },

        created() {
            this.init()
            this.merchantData = useMainStore().getMerchant
            this.getCountryList()

        },
        methods: {
            async getInvoiceData(value) {
                try{
                    this.fullscreenLoading = true
                    const userCode = value.user_code;
                    await downloadAgreementPdf(userCode)
                }catch (e) {
                }finally {
                    this.fullscreenLoading = false;
                }

                /*if (value.has_agreement === 0) {
                    return;
                }
                this.printData = []
                //let pricing = [...value.agreement.pricing]
                let pricing = [...this.list.pricing]
                pricing.forEach((res, idx) => {
                    // res.
                    let toCountry = this.getCountryByIso(res.shipvagoo_pricing.ubsend_price.to_country)
                    if (toCountry) {
                        const price = Number(res.shipvagoo_pricing.ship_standard_list_price - res.discount).toFixed(2);
                        const obj = {
                            desc: `DK >${toCountry} - ${res.price_group.carrier} - ${res.shipvagoo_pricing.ubsend_price.shipvagoo_prodct}  ${res.shipvagoo_pricing.ubsend_price.weight_class_from} - ${res.shipvagoo_pricing.ubsend_price.weight_class_to} kg`,
                            price: price
                        };
                        this.printData.push(obj)

                    }
                })
                this.printDetail = {}
                this.printDetail = {
                    name: value.company_name,
                    date: this.list.date,
                    summary: this.list.summary,
                    address: value.address,
                    footer: this.footer
                }
                this.dialogTableVisible = true*/
            },

            sendEmail() {
                this.$router.push({
                    name: 'CustomerEmail',
                    params: {merchantId: this.list.merchant.user_code, customerId: this.list.merchant.user_code}
                })
            },
            createAgreement(data, update) {
                if (data) {
                    data['update'] = update
                    useMainStore().setCustomer(data)
                }
                if (!update) {
                    this.$router.push({name: "add-customer"})
                } else {
                    this.$router.push({
                        name: 'update-customer-agreementAgreementId',
                        params: {agreementId: this.list.id}
                    })
                }
            },
            async getCountryList() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await merchantService.getCountryList()
                    this.countryList = res.data.countries
                    this.allCountries = this.countryList

                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            }
            ,


            getCountryByIso(country) {

                let city = this.countryList.find(el => el.name.toLowerCase() == country.toLowerCase())
                if (city) {
                    return city.iso
                } else {
                    return false
                }
            }
            ,
            async init() {
                this.loading = true
                try {
                    const res = await merchantService.getMerchantAgreement(this.customerId)
                    this.list = res.data.agreement
                    this.footer = res.data.footer
                    this.disabled = Object.keys(this.list).length > 0;
                    this.$emit('changeOnboardingStatus', this.list.onboarding_status)
                    this.loading = false
                } catch (e) {
                    this.disabled = Object.keys(this.list).length > 0;
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        this.loading = false
                        return
                    } else if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.message ?? e.message,
                            position: "top-right"
                        })
                        this.loading = false
                        return
                    }
                    this.$notify.warning({
                        title: "Warning",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    this.loading = false
                } finally {
                    loadingService.stopLoading("main-loader:login")
                    this.loading = false
                }
            }
            ,
            getKeyByStatus(value) {
                let obj = '';
                this.merchantChecked.map((row, key) => {
                    if (row.value === value) {
                        obj = row.label;
                    }
                })
                return obj;
            }
            ,
        }
    }
</script>


<style lang="scss" scoped>
    @import "@/assets/scss/_variables";

    .page-table {
        padding: 0% !important;
        margin-top: 2% !important;
        padding-right: 15px;
        padding-bottom: 20px;
        border: 1px solid #EEEEEE;
        border-radius: 5px;
    }

    .table-box {
        overflow: auto;
    }

    /* Automatic Serial Number Row */
    .css-serial {
        counter-reset: serial-number; /* Set the serial number counter to 0 */
    }

    /*.css-serial td:first-child:before {
      counter-increment: serial-number; !* Increment the serial number counter *!
      content: counter(serial-number); !* Display the counter *!
    }*/

    .Font-Size1 {
        font-size: 14px !important;
    }

    .seacr-box {
        width: 58rem;
        max-width: 100%;
        padding: 10px 30px;
        border-radius: 5px;
        border: 1px solid #EEEEEE;
        outline: none;
    }

    .mdi {
        padding-top: 2px;
        padding-left: 4px;
        color: #BBBBBB !important;
    }
</style>