<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form>
            <el-form-item label="From" required>
              <el-col :span="11">
                <el-form-item prop="startDate">
                  <el-date-picker
                      v-model="startDate"
                      type="date"
                      @change="handleChangeStart"
                      placeholder="From"
                      format="DD-MM-YYYY"
                      style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col class="text-center" :span="2">
                <span class="text-gray-500">To</span>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="endDate">
                  <el-date-picker
                      v-model="endDate"
                      type="date"
                      @change="handleChange"
                      placeholder="TO"
                      format="DD-MM-YYYY"
                      style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="card-body-item" v-loading="loading">
        <div>Customer Spent <span style="font-size: 12px;">(incl. VAT)</span></div>
        <b>{{ result.currency }} {{ result.amount ? danishNumberFormat(Number(result.amount)) : '0.0' }}</b>
      </div>
    </el-card>
  </div>
</template>

<script>
import moment from "moment/moment";
import {loadingService, merchantService} from "@/services/_singletons";
import {watch} from 'vue'
import {danishNumberFormat} from "@/helpers";

const default_date = '01-01-2023';
export default {
  name: "CustomerSpent",
  props: ['customerId', 'dates'],
  setup(props) {
    return {
      customerId: props.customerId
    }
  },
  watch: {
    dates(newVal, oldVal) {
      this.startDate = newVal.start_date;
      this.endDate = newVal.end_date
      const start_date = moment(newVal.start_date).format('yyyy-MM-DD');
      const end_date = moment(newVal.end_date).format('yyyy-MM-DD');
      if (start_date != this.startDate || end_date != this.endDate) {
        this.init(start_date, end_date)
      }
    }
  },
  data() {
    return {
      startDate: new Date(default_date),
      endDate: moment(new Date(default_date)).add(1, 'months').format('yyyy-MM-DD'),
      result: {},
      loading: false
    }
  },
  mounted() {
    this.startDate = moment(this.dates.start_date).format('yyyy-MM-DD');
    this.endDate = moment(this.dates.end_date).format('yyyy-MM-DD');
    this.init(this.startDate, this.endDate)
  },
  methods: {
    danishNumberFormat,
    handleChange(e) {
      this.init(this.startDate, moment(e).format('yyyy-MM-DD'))
      this.emitChange();
    },
    handleChangeStart(e) {
      this.endDate = moment(this.startDate).add(1, 'months').format('yyyy-MM-DD');
      this.init(moment(e).format('yyyy-MM-DD'), this.endDate)
      this.emitChange()
    },
    emitChange() {
      this.$emit('change', {start_date: this.startDate, end_date: this.endDate})
    },
    async init(startDate, endDate) {
      this.loading = true
      try {
        const res = await merchantService.postMerchantCustomerSpent({
          user_code: this.customerId,
          from_date: startDate,
          to_date: endDate
        })
        this.result = res.data
        this.loading = false
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-right"
          })
          loading.close()
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0] ?? e.response.data.error,
          position: "top-right"
        })
        this.loading = false

      } finally {
        loadingService.stopLoading("main-loader:login")
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.card-body-item b {
  font-size: 22px;
  line-height: 30px;
}
</style>