<template>
    <div>
        <el-card class="box-card">
            <template #header>
                <div class="card-header" style="overflow: hidden">
                    <el-form-item label="Year" prop="region" style="float: right">
                        <el-select style="width:80px" v-model="selectYear" placeholder="Activity zone">
                            <el-option v-for="(y,i) in selectedYears" :key="i" :label="y" :value="y"/>
                        </el-select>
                    </el-form-item>
                </div>
            </template>
            <div class="card-body-item" v-loading="loading">
                <div class="table-box card-base card-shadow--medium">
                    <table class="table styled striped label-text" style="table-layout: fixed">
                        <thead>
                        <tr class="Font-Size1">
                            <th class="shipping-product" style="text-align: left">Shipping Product</th>
                            <th v-for="(month,i) in months" :key="i">{{ month }}</th>
                            <th><b>Total</b></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="sales.length>0" v-for="(rows,index) in sales" :key="index">
                            <td class="statics-row-values"
                                v-for="(label,i) in rows" :key="i">
                                {{ label.value }}
                            </td>
                        </tr>
                        <tr v-else>
                            <td style="text-align: center" colspan="14">Sales stats not founds!</td>
                        </tr>
                        <tr class="bg-accent-light" v-if="sales.length>0">
                            <td class="statics-row-values"><strong>Total</strong></td>
                            <td v-for="(month,i) in months" :key="i"><strong class="statics-row-values">{{
                                monthTotal[month] ?? 0
                                }}</strong></td>
                            <td><strong class="statics-row-values">{{ monthTotal.total }}</strong></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script>
    import {merchantService} from "@/services/_singletons";

    export default {
        name: "SalesStatistics",
        props: ['customerId', 'year'],
        setup(props) {
            return {
                customerId: props.customerId,
            }
        },
        data() {
            return {
                loading: false,
                selectYear: new Date().getFullYear(),
                sales: [],
                monthTotal: [],
                totalMonthlySales: [],
                selectedYears: [
                    2022,
                    2023,
                    2024,
                    2025,
                    2026,
                    2027,
                    2028,
                    2029,
                ],
                months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            }
        },
        watch: {
            selectYear(selectY, oldSelectYear) {
                this.$emit('changeYear', selectY)
                this.getSalesStatistics(selectY)
            },
            year(newVal, oldVal) {
                this.selectYear = newVal;
            }
        },
        created() {
            this.getSalesStatistics(this.selectYear)
        },
        methods: {
            async getSalesStatistics(selectYear) {
                this.loading = true
                const res = await merchantService.getSalesStatistics({year: selectYear, user_code: this.customerId})
                try {
                    this.sales = []
                    let monthWiseTotal = [];
                    res.data.sales.map((row, index) => {
                        let rows = [];
                        rows.push({
                            name: '',
                            value: row.name,
                            isLast: false
                        })
                        let totals = 0;
                        this.months.map((month, i) => {
                            if (row.stats[month] !== undefined) {
                                rows.push({
                                    name: month,
                                    value: row.stats[month],
                                    isLast: false
                                })
                                totals += row.stats[month]
                                monthWiseTotal[month] = (monthWiseTotal[month] ?? 0) + Number(row.stats[month])
                            } else {
                                rows.push({
                                    name: month,
                                    value: 0,
                                    isLast: false
                                })
                                monthWiseTotal[month] = (monthWiseTotal[month] ?? 0) + 0
                            }
                        })
                        monthWiseTotal['total'] = (monthWiseTotal['total'] ?? 0) + totals
                        rows.push({
                            name: 'Total',
                            value: totals,
                            isLast: true
                        })
                        this.sales.push(rows)
                    })
                    this.monthTotal = monthWiseTotal;
                    this.loading = false
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0] ?? e.response.data.error,
                            position: "top-right"
                        })
                        this.loading = false
                        return
                    } else if (e.response.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.message ?? e.message,
                            position: "top-right"
                        })
                        this.loading = false
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                }
                this.loading = false
            }
        },
        mounted() {
            this.selectYear = this.year;
            console.log("Selected ", this.selectYear)
        }
    }
</script>

<style scoped>
    .label-text tbody td:last-child {
        font-weight: bold
    }

    .label-text tbody td:first-child {
        text-align: center
    }


    .statics-row-values {
        text-align: left !important;
        white-space: nowrap;
        font-size: 12px;
    }

    @media only screen and (max-width: 1025px) and (min-width: 769px) {
        .shipping-product {
            width: 220px;
        }

        .statics-row-values {
            font-size: 10px;
        }
    }

    @media only screen and (max-width: 1370px) and (min-width: 1025px) {
        .shipping-product {
            width: 285px;
        }

        .statics-row-values {
            font-size: 11px;
        }
    }

    @media only screen and (max-width: 1600px) and (min-width: 1371px) {
        .shipping-product {
            width: 340px;
        }

        .statics-row-values {
            font-size: 12px;
        }
    }

    @media only screen and (min-width: 1601px) {
        .shipping-product {
            width: 400px;
        }

        .statics-row-values {
            font-size: 14px;
        }
    }

    table.styled thead tr th {
        padding: 5px 10px;
        text-align: left;
    }
</style>