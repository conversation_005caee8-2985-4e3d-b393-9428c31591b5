<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header" style="overflow: hidden">
          <el-form-item label="Year" prop="region" style="float: right">
            <el-select
                style="width:80px"
                v-model="selectYear" placeholder="Activity zone">
              <el-option v-for="(y,i) in selectedYears" :key="i" :label="y" :value="y"/>
            </el-select>
          </el-form-item>
        </div>
      </template>
      <div class="card-body-item" v-loading="loading">
        <div class="table-box card-base card-shadow--medium">
          <table class="table styled striped label-text" style="table-layout: fixed;">
            <thead>
            <tr class="Font-Size1">
              <th class="shipping-product">Carrier</th>
              <th v-for="(month,i) in months" :key="i">{{ month }}</th>
              <th>Total</th>
            </tr>
            </thead>
            <tbody>
            <tr v-if="carriers.length>0" v-for="(rows,index) in carriers" :key="index">
              <td
                  class="statics-row-values" v-for="(label,i) in rows" :key="i">
                <img style="width: 40px;border-radius: 50%"
                     v-if="label.icon !==undefined"
                     :src="label.icon"
                     :alt="label.value"/>
                <span v-else>{{ label.value }}</span>
              </td>
            </tr>
            <tr v-else>
              <td style="text-align: center" colspan="14">Carrier stats not founds!</td>
            </tr>
            <tr class="bg-accent-light" v-if="carriers.length>0">
              <td class="statics-row-values"><strong>Total</strong></td>
              <td v-for="(month,i) in months" :key="i"><strong class="statics-row-values">{{
                  monthTotal[month] ?? 0
                }}</strong></td>
              <td><strong class="statics-row-values">{{ monthTotal.total }}</strong></td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {merchantService} from "@/services/_singletons";

export default {
  name: "CarrierStastistics",
  props: ['customerId', 'year'],
  setup(props) {
    return {
      customerId: props.customerId
    }
  },
  data() {
    return {
      carriers: [],
      loading: false,
      monthTotal: [],
      selectYear: new Date().getFullYear(),
      selectedYears: [
        2022,
        2023,
        2024,
        2025,
        2026,
        2027,
        2028,
        2029
      ],
      months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    }
  },
  watch: {
    selectYear(selectY, oldSelectYear) {
      this.$emit('changeYear', selectY)
      this.getCarrierStatistics(selectY)
    },
    year(newVal, oldVal) {
      this.selectYear = newVal;
    }
  },
  created() {
    this.getCarrierStatistics(this.selectYear)
  },
  methods: {
    async getCarrierStatistics(selectYear) {
      this.loading = true
      const res = await merchantService.getCarrierStatistics({year: selectYear, user_code: this.customerId})
      try {
        this.carriers = []
        let monthWiseTotal = [];
        res.data.carriers.map((row, index) => {
          let rows = [];
          rows.push({
            name: '',
            value: row.name,
            icon: row.icon
          })
          //row.stats.map((month,i)=>{})
          let totals = 0;

          this.months.map((month, i) => {
            if (row.stats[month] !== undefined) {
              rows.push({
                name: month,
                value: row.stats[month]
              })
              totals += row.stats[month]
              monthWiseTotal[month] = (monthWiseTotal[month] ?? 0) + Number(row.stats[month])
            } else {
              rows.push({
                name: month,
                value: 0
              })
              monthWiseTotal[month] = (monthWiseTotal[month] ?? 0) + 0
            }
          })
          rows.push({
            name: 'Total',
            value: totals
          })
          monthWiseTotal['total'] = (monthWiseTotal['total'] ?? 0) + totals
          this.carriers.push(rows)
        })
        this.monthTotal = monthWiseTotal;
        this.loading = false
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error ?? '',
            position: "top-right"
          })
          this.loading = false
          return
        } else if (e.response.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.message ?? e.message,
            position: "top-right"
          })
          this.loading = false
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0] ?? e.response.data.error,
          position: "top-right"
        })
        this.loading = false
      }
      this.loading = false
    }
  },
  mounted() {
    this.selectYear = this.year;
  }
}
</script>

<style scoped>
.label-text tbody td:last-child {
  font-weight: bold
}

.label-text tbody td:first-child {
  text-align: center
}

.statics-row-values {
  text-align: left !important;
  white-space: nowrap;
  font-size: 12px;
}

@media only screen and (max-width: 1025px) and (min-width: 769px) {
  .shipping-product {
    width: 220px;
  }

  .statics-row-values {
    font-size: 10px;
  }
}

@media only screen and (max-width: 1370px) and (min-width: 1025px) {
  .shipping-product {
    width: 270px;
  }

  .statics-row-values {
    font-size: 11px;
  }
}

@media only screen and (max-width: 1600px) and (min-width: 1371px) {
  .shipping-product {
    width: 340px;
  }

  .statics-row-values {
    font-size: 12px;
  }
}

@media only screen and (min-width: 1601px) {
  .shipping-product {
    width: 400px;
  }

  .statics-row-values {
    font-size: 14px;
  }
}


table.styled thead tr th {
  padding: 5px 10px;
  text-align: left;
}
</style>