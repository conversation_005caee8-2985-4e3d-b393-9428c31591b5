<template>

  <div class="search-container">
    <search-box @search=""></search-box>
  </div>
  <div class="page-table scrollable only-y" id="affix-container">


    <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
      <table class="css-serial styled striped">
        <thead>
        <tr class="Font-Size1">
          <th>ID</th>
          <th style="padding-left: 15%;">Carrier</th>
          <th style="padding-left: 25%;">Price Group</th>

        </tr>
        </thead>
        <tbody>
        <tr v-if="list.length>0" v-for="item in list" :key="item.price_group_id">
          <td></td>
          <td style="padding-left: 15%;">{{ item.price_group.carrier }}</td>
          <td style="padding-left: 25%;">{{ item.price_group.price_group_name }}</td>
        </tr>
        <tr v-else>
          <td style="color: aliceblue;"></td>
          <td style="text-align: center;" colspan="3">Price Group not founds</td>
        </tr>
        </tbody>
      </table>


    </div>
  </div>
</template>

<script>
import Affix from "@/components/Affix.vue"
import {defineComponent} from "@vue/runtime-core"
import {loadingService, merchantService} from "@/services/_singletons";
import {ElLoading} from "element-plus";

export default defineComponent({
  name: "PriceGroup",
  props: {
    customerId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return {
      customerId: props.customerId
    }
  },
  data() {
    return {
      affixEnabled: true,
      list: [],
      loading: false
    }
  },
  components: {
    Affix
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      this.loading = true
      try {
        const res = await merchantService.getMerchantPriceGroup(this.customerId)
        this.list = res.data['price groups']
        this.loading = false
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-right"
          })
          this.loading = false
          return
        } else if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.message ?? e.message,
            position: "top-right"
          })
          this.loading = false
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0] ?? e.response.data.error,
          position: "top-right"
        })
        this.loading = false
      } finally {
        loadingService.stopLoading("main-loader:login")
        this.loading = false
      }
    }
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
  padding: 0% !important;
  margin-top: 2% !important;
  padding-right: 15px;
  padding-bottom: 20px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
}

.table-box {
  overflow: auto;
}

/* Automatic Serial Number Row */
.css-serial {
 counter-reset: serial-number; /* Set the serial number counter to 0 */
}
.css-serial td:first-child:before {
 counter-increment: serial-number; /* Increment the serial number counter */
 content: counter(serial-number); /* Display the counter */
}

.Font-Size1 {
  font-size: 14px !important;
}

</style>
