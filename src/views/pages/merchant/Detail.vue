<template>
    <div class="d-flex flex-column">
        <div style="height: 1px; background-color: #eeeeee; margin-top: 1%; margin-bottom: 1%"></div>
        <div class="flex gaps grid-6">
            <div style="font-size: 14px">Customer ID</div>
            <div style="font-size: 14px">{{ getBalanceLabel() }}</div>
            <div style="font-size: 14px" v-if="shouldShowCreditLimit()">Credit Limit</div>
            <div style="font-size: 14px">Customer Status</div>
            <div style="font-size: 14px">Onboarding Status</div>
            <div></div>
            <div class="flex justify-center" style="font-size: 14px">Status</div>
        </div>
        <div class="flex gaps grid-6 top-menu mt-10" style="margin-bottom: 2%">
            <div style="font-size: 16px; font-weight: 800">{{ $route.params.company_code }}</div>
            <div style="font-size: 16px; font-weight: 800">
                DKK {{ user.balance ? danishNumberFormat(user.balance) : "0.00" }}
            </div>
            <div style="font-size: 16px; font-weight: 800" v-if="shouldShowCreditLimit()">
                DKK {{ user.credit_limit ? danishNumberFormat(user.credit_limit) : "0.00" }}
            </div>
            <div style="font-size: 16px; font-weight: 800">{{ is_active ? "Active" : "Inactive" }}</div>
            <div style="font-size: 16px; font-weight: 800" v-if="onboardingStatus == '0'">Not Started</div>
            <div style="font-size: 16px; font-weight: 800" v-else-if="onboardingStatus == '1'">Draft</div>
            <div style="font-size: 16px; font-weight: 800" v-else-if="onboardingStatus == '2'">Pending</div>
            <div style="font-size: 16px; font-weight: 800" v-else-if="onboardingStatus == '3'">Complete</div>
            <div></div>
            <div class="flex justify-center">
                <el-switch
                    v-model="is_active"
                    :active-value="1"
                    :inactive-value="0"
                    @change="updateCustomerStatus"
                ></el-switch>
            </div>
        </div>
        <div class="page-layout-tabbed">
            <el-tabs @tab-click="handleClick" type="border-card" v-model="currentTab">
                <el-tab-pane label="Summary" name="Summary">
                    <div class="mws-custom-scroll">
                        <SummaryTab v-if="currentTab == 'Summary'" :user="user" :logs="logs" />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Sales" name="Sales">
                    <div class="mws-custom-scroll">
                        <SalesTabs v-if="currentTab == 'Sales'" :customerId="$route.params.user_code" />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Agreement" name="Agreement">
                    <div class="mws-custom-scroll">
                        <AgreementTab
                            @changeOnboardingStatus="value => (onboardingStatus = value)"
                            v-if="currentTab == 'Agreement'"
                            :customerId="$route.params.user_code"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Price Group" name="Price Group">
                    <div class="mws-custom-scroll">
                        <PriceGroup
                            v-if="currentTab == 'Price Group'"
                            :currentTab="currentTab"
                            :customerId="$route.params.user_code"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Customer Service"> </el-tab-pane>
                <el-tab-pane label="Contacts" name="Contacts">
                    <div class="mws-custom-scroll">
                        <ContractTab v-if="currentTab === 'Contacts'" :customerId="$route.params.user_code" />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Payments" name="Payments">
                    <div class="mws-custom-scroll">
                        <PaymentTab v-if="currentTab === 'Payments'" :customerId="$route.params.user_code" />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Shipments" name="Shipments"> </el-tab-pane>
                <el-tab-pane label="Login Info" name="Login Info">
                    <div class="mws-custom-scroll">
                        <LoginInfo
                            v-if="currentTab === 'Login Info'"
                            :user="user"
                            :customerId="$route.params.user_code"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Error Log" name="Error Log">
                    <div class="mws-custom-scroll">
                        <CustomerErrorList
                            v-if="currentTab === 'Error Log'"
                            :company-code="$route.params.company_code"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="Assign Credit" name="Assign Credit">
                    <div class="mws-custom-scroll">
                        <AssignCredit v-if="currentTab === 'Assign Credit'"
                        :user="user"
                        :company-code="$route.params.company_code"
                        @credit-status-changed="onCreditStatusChanged"
                        />
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { defineComponent, h } from "@vue/runtime-core"
import Timeline from "@/components/Timeline.vue"
import Tables from "@/components/Tables.vue"
import { useRoute } from "vue-router"
import AgreementTab from "./DetailPanels/AgreementTab.vue"
import PriceGroup from "./DetailPanels/PriceGroup.vue"
import ContractTab from "./DetailPanels/ContractTab.vue"
import SummaryTab from "./DetailPanels/SummaryTab.vue"
import PaymentTab from "./DetailPanels/PaymentTab.vue"
import LoginInfo from "./DetailPanels/LoginInfo.vue"
import { merchantService } from "@/services/_singletons"
import { ElLoading } from "element-plus"
import SalesTabs from "@/views/pages/merchant/DetailPanels/SalesTabs.vue"
import { number, string } from "yup"
import { useMainStore } from "@/stores/main"
import { danishNumberFormat } from "@/helpers"
import CustomerErrorList from "@/views/pages/merchant/CustomerErrorList.vue"
import AssignCredit from "./AssignCredit.vue"

export default defineComponent({
    name: "LayoutTabbed",
    components: {
        CustomerErrorList,
        AssignCredit,
        SalesTabs,
        Timeline,
        Tables,
        AgreementTab,
        PriceGroup,
        ContractTab,
        SummaryTab,
        PaymentTab,
        LoginInfo
    },
    data() {
        return {
            user: {},
            logs: [],
            onboardingStatus: string,
            currentTab: "Summary",
            is_active: 0,
            credit_enabled: false
        }
    },

    async created() {
        await this.init()
        await this.fetchCreditDetails()
        const tab = localStorage.getItem("merchant-current-tab")
        if (tab == "Agreement") {
            this.currentTab = tab
        } else if (tab == "Error Log") {
            this.currentTab = tab
        }
    },
    beforeRouteEnter(to, from, next) {
        if (from.name == "customer-list") {
            localStorage.removeItem("merchant-current-tab")
        }
        next(vm => {
            vm.prevRoute = from
        })
    },
    watch: {
        currentTab(newVal, oldVal) {
            localStorage.setItem("merchant-current-tab", newVal)
            // Refresh credit details when switching to Assign Credit tab
            if (newVal === "Assign Credit") {
                this.fetchCreditDetails()
            }
        }
    },
    methods: {
        danishNumberFormat,
        async updateCustomerStatus() {
            const requestPayload = { user_code: this.$route.params.user_code, is_active: this.is_active }
            const res = await merchantService.updateCustomerStatus(requestPayload)
            this.$notify({
                title: "Success",
                message: res.message,
                type: "success"
            })
        },
        async init() {
            const route = useRoute()
            const userCode = route.params.user_code
            const loading = ElLoading.service({
                lock: true,
                text: "Loading",
                background: "rgba(0, 0, 0, 0.7)"
            })
            try {
                const res = await merchantService.getMerchantSummaryDetails(userCode)
                this.user = res.data.user
                this.logs = res.data.logs
                this.is_active = res.data.user.is_active
                this.onboardingStatus = useMainStore().getMerchant["onboarding_status"]
                loading.close()
            } catch (e) {
                loading.close()
            }
            this.$forceUpdate()
        },
        handleClick(tab, event) {
            this.currentTab = tab.props.label
        },

        getBalanceLabel() {
            if (this.user.balance > 0) {
                return "Customer Balance"
            } else if (this.user.balance < 0 && this.credit_enabled) {
                return "Credit Spend"
            } else {
                return "Customer Balance"
            }
        },

        shouldShowCreditLimit() {
            return this.user.balance < 0 && this.credit_enabled
        },

        async fetchCreditDetails() {
            try {
                const res = await merchantService.getMerchantCreditDetails(this.$route.params.company_code)
                const details = Array.isArray(res) ? res[0] : res.data.company || null

                if (details) {
                    this.credit_enabled = !!(
                        details.credit_enabled === true ||
                        details.credit_enabled === "1" ||
                        details.credit_enabled === 1
                    )
                }
            } catch (e) {
                console.error("Failed to fetch credit details:", e)
            }
        },

        onCreditStatusChanged(newCreditStatus) {
            this.credit_enabled = newCreditStatus
        }
    }
})
</script>

<style lang="scss" scoped>
// @import "@/assets/scss/_variables.scss";

.page-layout-tabbed {
    .page-header {
        margin-bottom: 20px;
    }

    .el-tabs {
        border: none;
        overflow: hidden;
        color: #000;
    }

    .demo-img {
        width: 100%;
        margin-bottom: 10px;
        border-radius: 4px;
        max-width: 600px;
        float: left;
        margin-right: 35px;
        margin-bottom: 30px;
    }

    .import-info {
        font-size: 14px !important;
        font-weight: 700 !important;
        padding-bottom: 1% !important;
    }

    .import-info1 {
        font-size: 14px !important;
    }

    .page-table {
        padding: 0% !important;
    }

    .paragraph-imp {
        padding-left: 10px !important;
        font-size: 12px !important;
    }

    .User-Name {
        font-size: 24px !important;
    }

    .mws-custom-scroll {
        height: calc(100vh - 300px);
        overflow-y: scroll;
        padding-left: 15px;
        padding-right: 15px;
    }
}
</style>
