<template>
    <div class="scrollable only-y p-2">
        <div class="toolbar-box flex align-center">
            <div class="page-header">
                <h1 class="ml-2">Customer</h1>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                    <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
                    <el-breadcrumb-item>Customer</el-breadcrumb-item>
                    <el-breadcrumb-item :to="{ path: 'customer/add-customer' }">
                        {{ $route.params.id ? 'Edit Customer' : 'Add Customer' }}
                    </el-breadcrumb-item>

                </el-breadcrumb>
            </div>
        </div>

        <div class="card-base card-shadow--medium search-card scrollable only-y mb-5 p-10">

            <el-form ref="form" class="mb-50 bb-br-gray" :model="form" label-width="120px">
                <h4 class="ml-12">Customer Detail</h4>

                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Company Name" required
                                      size="default"
                                      class="asterisk-right"
                        >
                            <el-input v-model="form.company_name" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Person Name" required
                                      class="asterisk-right">
                            <el-input v-model="form.name" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-col>
                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Email" required
                                      class="asterisk-right">
                            <el-input v-model="form.email" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Phone" required
                                      class="asterisk-right">
                            <el-input min="0" v-model="form.phone_no" type="number" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-col>

                <el-form-item label="Address" required
                              class="asterisk-right">
                    <el-input :maxlength="40" v-model="form.address"></el-input>
                </el-form-item>

                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Country" required
                                      class="asterisk-right">
                            <el-select
                                    disabled
                                    v-model="form.country_code"
                                    filterable
                                    placeholder=""
                                    :loading="loading"
                            >
                                <el-option
                                        v-for="item in countryList"
                                        :key="item.country_code"
                                        :label="item.name"
                                        :value="item.country_code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="City" required
                                      class="asterisk-right">
                            <el-input v-model="form.city" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-col>

                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Zipcode" required
                                      class="asterisk-right">
                            <el-input v-model="form.zip" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="CVR" required
                                      class="asterisk-right">
                            <el-input v-model="form.vat" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-col>
                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Platform" required
                                      class="asterisk-right">
                            <el-select v-model="form.platform" placeholder="">
                                <template v-for="data in platformList" :key="data">
                                    <el-option :label="data.name" :value="data.platform_code"></el-option>
                                </template>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Platform URL" required
                                      class="asterisk-right">
                            <el-input v-model="form.platform_url" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-col>
                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Company URL">
                            <el-input v-model="form.company_url"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Status">
                            <el-switch :active-value="1" :inactive-value="0" v-model="form.is_active"></el-switch>
                        </el-form-item>
                    </el-col>
                </el-col>

                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Economic ID">
                            <el-input v-model="form.economic_id"></el-input>
                        </el-form-item>
                    </el-col>
                </el-col>

                <el-form-item label="Summary">
                    <el-input show-word-limit :maxlength="200" rows="4" type="textarea"
                              v-model="form.summary"></el-input>
                </el-form-item>

                <div class="bt-br-gray mb-2"></div>
                <div class="flex m-2">
                    <div class="col-8">
                        <h4 class="ml-12 mt-10">Old Agreement Details</h4>
                    </div>
                    <div class="col-4 flex justify-flex-end">
                        <el-button v-if="dynamicList.length==0" @click="addExperience()" style="margin-top: 7px;"
                                   type="primary"
                                   size="medium">Add
                        </el-button>
                    </div>
                </div>

                <!--        <CountryListAgreement v-if="!loading" :countryList="countryList" :dynamicList="dynamicList"
                                              @deleteExperience="deleteExperience"/>
                                                      <el-col class="demo-form-inline flex" :span="24">
                      <el-col class="demo-form-inline" :span="7">
                        <el-form-item label="Country From">
                          <el-select
                              v-model="country_from"
                              filterable
                              placeholder=" "
                              :loading="loading"
                          >
                            &lt;!&ndash;   @click="checkDuplicateHandle(data.country_from,'from')"              &ndash;&gt;
                            <el-option
                                v-for="item in countryList"
                                :key="item.country_code"
                                :label="item.name"
                                :value="item.country_code"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col class="demo-form-inline" :span="7">
                        <el-form-item label="Country To">
                          <el-select
                              v-model="country_to"
                              filterable
                              placeholder=" "
                              :loading="loading"
                          >
                            <el-option
                                v-for="item in countryList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.country_code"
                            />
                          </el-select>
                          &lt;!&ndash;  @click="checkDuplicateHandle(data.country_to,'to')"            &ndash;&gt;
                        </el-form-item>
                      </el-col>
                      <el-col class="demo-form-inline" :span="8">
                        <el-form-item label="Parcel Per Year">
                          <el-col class="demo-form-inline">
                            <el-input min="0" max="999999" @input="parcelValidation" v-model="parcels" type="number"
                                      placeholder=""></el-input>
                          </el-col>
                        </el-form-item>
                      </el-col>
                      <el-col class="demo-form-inline ml-6" :span="8">
                        <i
                            class="widget-icon mdi mdi-plus-circle accent-text fs-25 cursor-pointer"
                            @click="addExperience()"
                        ></i>
                      </el-col>
                    </el-col>-->
                <el-col class="demo-form-inline flex" :span="24" v-for="(data, index) in dynamicList" :key="index">
                    <el-col class="demo-form-inline" :span="7">
                        <el-form-item label="Country From">
                            <el-select
                                    v-model="data.country_from"
                                    filterable
                                    placeholder=" "
                            >
                                <el-option
                                        v-for="item in countryList"
                                        :key="item.country_code"
                                        :label="item.name"
                                        :value="item.country_code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="7">
                        <el-form-item label="Country To">
                            <el-select
                                    v-model="data.country_to"
                                    filterable
                                    placeholder=" "
                            >
                                <el-option
                                        v-for="item in countryList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.country_code"
                                />
                            </el-select>
                            <!--  @click="checkDuplicateHandle(data.country_to,'to')"            -->
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline" :span="8">
                        <el-form-item label="Parcel Per Year">
                            <el-col class="demo-form-inline">
                                <el-input
                                        @input="onAddParcelPerYear(index)"
                                        min="0" max="999999" v-model="data.parcels" type="number"
                                        placeholder=""></el-input>
                            </el-col>
                        </el-form-item>
                    </el-col>
                    <el-col class="demo-form-inline ml-6" :span="8">
                        <i
                                title="Remove"
                                class="widget-icon mdi mdi-delete-circle-outline fs-23 cursor-pointer"
                                style="color: #b22234"
                                @click="deleteExperience(index)"
                        ></i>
                        <i
                                title="Add new"
                                v-if="index+1==dynamicList.length"
                                style="margin-left: 4px;"
                                class="widget-icon mdi mdi-plus-circle accent-text fs-23 cursor-pointer"
                                @click="addExperience(index)"
                        ></i>
                    </el-col>
                </el-col>

                <el-form-item label="Summary">
                    <el-input show-word-limit :maxlength="200" rows="4" type="textarea"
                              v-model="form.agreement_summary"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button class="btn-blue-bg" @click="onSubmit" v-loading.fullscreen.lock="fullscreenLoading">
                        {{ $route.params.id ? 'Update' : 'Save' }}
                    </el-button>
                    <el-button @click="onCanel">Cancel</el-button>
                </el-form-item>
            </el-form>

        </div>
    </div>
</template>


<script>
    import users from "@/assets/data/USERS_MOCK_DATA.json"
    import _ from "lodash"
    import dayjs from "dayjs"
    import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
    import {merchantService, loadingService, ubsendService} from "../../../services/_singletons"
    import {useMainStore} from "@/stores/main"
    import {defineComponent} from "@vue/runtime-core"
    import {mwsEmailValidation, mwsUrlValidation} from "@/helpers";
    import CountryListAgreement from "@/views/pages/merchant/CountryListAgreement.vue";

    export default defineComponent({
        name: "AddMerchantPage",
        data() {
            var checkAge = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error("Please input the age"))
                }
                setTimeout(() => {
                    if (!Number.isInteger(value)) {
                        callback(new Error("Please input digits"))
                    } else {
                        if (value < 18) {
                            callback(new Error("Age must be greater than 18"))
                        } else {
                            callback()
                        }
                    }
                }, 1000)
            }
            var validatePass = (rule, value, callback) => {
                if (value === "") {
                    callback(new Error("Please input the password"))
                } else {
                    if (this.ruleForm2.checkPass !== "") {
                        this.$refs.ruleForm2.validateField("checkPass")
                    }
                    callback()
                }
            }
            var validatePass2 = (rule, value, callback) => {
                if (value === "") {
                    callback(new Error("Please input the password again"))
                } else if (value !== this.ruleForm2.pass) {
                    callback(new Error("Two inputs don't match!"))
                } else {
                    callback()
                }
            }
            return {
                isMobile: false,
                country_from: '',
                country_to: '',
                parcels: '',
                ready: false,
                width: 0,
                height: "auto",
                onSubmitload: false,
                search: "",
                pagination: {
                    page: 1,
                    size: 20,
                    sizes: [10, 15, 20, 30, 50, 100],
                    layout: "total, ->, prev, pager, next, jumper, sizes",
                    small: false
                },
                list: [],
                editMode: false,
                loading: false,
                fullscreenLoading: false,
                itemsChecked: [],
                selectedCountry: null,
                currentId: 0,
                dayjs,
                carrierList: [],
                platformList: [],
                countryList: [],
                allCountries: [],
                merchantList: [],
                form: {
                    company_name: "",
                    company_code: "",
                    address: "",
                    country_code: "",
                    zip: "",
                    vat: "",
                    platform: "",
                    platform_url: "",
                    economic_id:"",
                    summary: "",
                    agreement_summary: "",
                    name: "",
                    email: "",
                    phone_no: "",
                    city: "",
                    formAgreement: [],
                    merchant_current_agreement: [],
                    is_active: 0
                },
                rules2: {
                    company_name: [{validator: validatePass, trigger: "blur"}],
                    address: [{validator: validatePass2, trigger: "blur"}],
                    name: [{validator: checkAge, trigger: "blur"}]
                },
            }
        },
        computed: {
            async editData() {
                var companyData = useMainStore().getMerchant
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await merchantService.getMerchantDetail({'user_code': companyData['user_code']});
                    return res.data.customer
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }

            },
            dynamicList: {
                //return this.form.formAgreement
                set(newValue) {
                    console.log("Newval ", newValue);
                    this.form.merchant_current_agreement = newValue;
                },
                get() {
                    return this.form.merchant_current_agreement
                }
            },
            listFiltered() {
                return this.list.filter(obj => {
                    let ctrl = false
                    for (let k in obj) {
                        if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                    }
                    return ctrl
                })
            },
            listSortered() {
                let prop = this.sortingProp
                let order = this.sortingOrder
                return [].concat(
                    this.listFiltered.sort((item1, item2) => {
                        let val1 = ""
                        let val2 = ""

                        val1 = item1[prop]
                        val2 = item2[prop]
                        if (order === "descending") {
                            return val2 < val1 ? -1 : 1
                        }
                        return val1 < val2 ? -1 : 1
                    })
                )
            },
            listInPage() {
                let from = (this.currentPage - 1) * this.itemPerPage
                let to = from + this.itemPerPage * 1
                //return this.listSortered.slice(from, to)
                return this.listFiltered.slice(from, to)
            },
            total() {
                return this.listFiltered.length
            },
            currentPage: {
                get() {
                    return this.pagination.page
                },
                set(val) {
                    this.pagination.page = val
                }
            },
            itemPerPage() {
                return this.pagination.size
            },
            selectedItems() {
                return this.itemsChecked.length || 0
            }
        },
        watch: {
            itemPerPage(val) {
                this.ready = false
                this.currentPage = 1

                setTimeout(() => {
                    this.ready = true
                }, 500)
            }
        },
        methods: {
            onAddParcelPerYear(index) {
                const parcelPerYear = Math.trunc(Number(this.dynamicList[index].parcels));
                this.dynamicList[index].parcels = parcelPerYear;
                if (parcelPerYear < 0) {
                    setTimeout(() => {
                        this.dynamicList[index].parcels = 1;
                    }, 10)
                }
            },
            validateAgreementDetails() {
                let isValid = true;
                this.form.merchant_current_agreement.forEach((item, index) => {
                    if (item.country_from == "" || item.country_to == "" || item.parcels == "") {
                        isValid = false;
                    }
                })
                return isValid;
            },
            parcelValidation() {
                this.parcels = Math.trunc(this.parcels)
            },
            countryExist(countryId, key) {
                let i = -1
                this.form.merchant_current_agreement.map((row, index) => {
                    if (index < this.form.merchant_current_agreement.length - 1) {
                        //  if (row[key] === countryId && row.show === true) {
                        if (row[key] === countryId) {
                            i = index
                        }
                    }
                })
                return i
            },
            checkDuplicateHandle(countryId, placement) {
                let flag = false;
                if (placement === 'from') {
                    if (this.countryExist(countryId, 'country_from') !== -1) {
                        flag = true
                    }
                }
                if (placement === 'to') {
                    if (this.countryExist(countryId, 'country_to') !== -1) {
                        flag = true
                    }
                }
                console.log(countryId, placement)
                return flag
            },
            handleSelectionChange(val) {
                this.itemsChecked = val
            },
            async init() {
                this.loading = true
                await this.getCountryList()
                await this.getPlatformList()
                this.loading = false
                if (window.innerWidth <= 768) this.isMobile = true
            },
            notifyMessage(msg) {
                this.$notify.warning({
                    title: "Warning",
                    message: msg,
                    position: "top-right"
                })
            },
            addExperience(idx) {
                const row = this.dynamicList[idx];
                if (row) {
                    if (row.country_from == '') {
                        this.notifyMessage('Country from field is not selected.')
                        return;
                    }
                    if (row.country_to == '') {
                        this.notifyMessage('Country to field is not selected.')
                        return;
                    }
                    if (row.parcels == '') {
                        this.notifyMessage('Parcel per year field is empty.')
                        return;
                    }
                    const countryFrom = this.countryExist(row.country_from, 'country_from');
                    const countryTo = this.countryExist(row.country_to, 'country_to');
                    if (countryFrom !== -1 && countryTo !== -1) {
                        this.$notify.warning({
                            title: "Warning",
                            message: 'Country Already Added!',
                            position: "top-right"
                        })
                        return;
                    }
                }

                this.form.merchant_current_agreement.push({
                    country_from: "",
                    country_to: "",
                    parcels: "",
                    show: true
                })
                this.resetCurrentAgreement();
                return;
            },
            resetCurrentAgreement() {
                this.country_from = ''
                this.country_to = ''
                this.parcels = ''
            },
            deleteExperience(idx) {
                console.log('after', idx)
                this.form.merchant_current_agreement.splice(idx, 1)
            },

            async onSubmit(values) {
                // display form values on success
                if (!mwsEmailValidation(this.form.email)) {
                    this.$notify.warning({
                        title: "Error",
                        message: 'Email is not valid !',
                        position: "top-right"
                    })
                    return
                }
                if (!mwsUrlValidation(this.form.platform_url)) {
                    this.$notify.warning({
                        title: "Error",
                        message: 'Platform Url is not valid !',
                        position: "top-right"
                    })
                    return
                }
                if (this.country_from != "" || this.country_to != "" || this.parcels != "") {
                    const data = {
                        country_from: this.country_from,
                        country_to: this.country_to,
                        parcels: this.parcels,
                        show: true
                    }
                    this.form.merchant_current_agreement.push(data)
                    this.resetCurrentAgreement()
                }
                if (!this.validateAgreementDetails()) {
                    this.$notify.warning({
                        title: "Error",
                        message: 'Some old agreement fields are empty!',
                        position: "top-right"
                    })
                    return
                }


                try {
                    this.fullscreenLoading = true;
                    // loadingService.startLoading('main-loader:login');
                    if (this.$route.params.id) {
                        this.update()
                        return
                    }
                    const res = await merchantService.addMerchant(this.form)
                    this.$notify({
                        title: "Success",
                        message: res.message,
                        type: "success"
                    })
                    await this.$router.push({path: "/customer/customer-list"})
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.fullscreenLoading = false;
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                    // notificationService.showError('Error during login');
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            remoteMethod(query) {
                if (query) {
                    this.loading = true
                    setTimeout(() => {
                        this.loading = false
                        this.countryList = this.countryList.filter((item) => {
                            return item.name.toLowerCase().includes(query.toLowerCase())
                        })
                    }, 200)
                } else {
                    this.countryList = this.allCountries
                }
            },
            async update(values) {
                // display form values on success

                try {

                    // loadingService.startLoading('main-loader:login');
                    const res = await merchantService.updateMerchant(this.form)
                    this.$notify({
                        title: "Success",
                        message: res.message,
                        type: "success"
                    })
                    await this.$router.push({path: "/customer/customer-list"})
                    setTimeout(() => {
                        this.fullscreenLoading = false;
                    }, 500);
                } catch (e) {
                    setTimeout(() => {
                        this.fullscreenLoading = false;
                    }, 500);
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async onCanel() {
                await this.$router.push({path: "/customer/customer-list"})
            },
            handleSelect(item) {
                this.search = item.name
                this.form.country_code = item.country_code
            },
            querySearch(queryString, cb) {
                var list = this.countryList
                var results = queryString ? list.filter(this.createFilter(queryString)) : list
                // call callback function to return suggestions
                cb(results)
            },
            createFilter(queryString) {
                return link => {
                    return link.name.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
                }
            },
            async getCountryList() {
                // display form values on success
                try {

                    // loadingService.startLoading('main-loader:login');
                    const res = await merchantService.getCountryList()
                    this.countryList = res.data.countries
                    this.allCountries = this.countryList
                    if (this.$route.params.id) {
                        var editData = await this.editData;
                        let countryId = this.countryList.filter((item) => item.country_code === editData["company"]["country_code"])
                        if (countryId.length > 0) {
                            this.form.country_code = countryId[0].country_code
                        }
                    }
                    this.form.country_code = 3421137111;
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e,
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getPlatformList() {
                // display form values on success
                try {

                    // loadingService.startLoading('main-loader:login');
                    const res = await merchantService.getPlatformList()
                    this.platformList = res.data

                    if (this.$route.params.id) {
                        const editData = await this.editData;

                        let platformId = this.platformList.filter((item) => item.platform_code === editData.company.platform?.platform_code)

                        if (platformId.length > 0) {
                            this.form.platform = platformId[0].platform_code
                        }
                    }
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e,
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },

        },
        async created() {
            this.init()

            if (this.$route.params.id) {
                var editData = await this.editData;
                if (this.editData === null) {
                    this.onCanel()
                }
                this.form.company_name = editData['company']['company_name']
                this.form.company_url = editData["company"]['website']
                this.form.economic_id = editData["company"]['economic_id']
                this.form.company_code = editData["company"]['company_code']
                this.form.address = editData["company"]['address']
                this.form.zip = editData["company"]["zipcode"]
                this.form.vat = editData["company"]["vat_no"]
                this.form.platform_url = editData["company"]["platform_url"]
                this.form.agreement_summary = editData["company"]["agreement_summary"]
                this.form.summary = editData["company"]["summary"]
                this.form.name = editData["name"]
                this.form.email = editData["email"]
                this.form.phone_no = editData["company"]["phone_no"]
                this.form.city = editData["company"]["city"]
                this.form.is_active = editData["is_active"]
                this.form.merchant_current_agreement = editData["current_agreement"].map((data, index) => {
                    return {
                        country_from: data.country_from,
                        country_to: data.country_to,
                        created_at: data.created_at,
                        id: data.id,
                        parcels: data.parcels,
                        user_code: data.user_code,
                        show: index === 0 ? true : false
                    }
                })
                // console.log("agreement length ", this.form.merchant_current_agreement.length)
            }
            if (this.form.merchant_current_agreement.length === 0) {
                this.form.merchant_current_agreement.push({
                    country_from: "",
                    country_to: "",
                    parcels: "",
                    show: true
                });
            }
        },
        mounted() {
            //ie fix
            if (!window.Number.parseInt) window.Number.parseInt = parseInt
        },
        components: {CountryListAgreement, ResizeObserver}
    })
</script>

<style lang="scss">
    .el-form-item__label {
        font-size: 13px;
    }
</style>
<!--<style lang="scss">
    @import "vue-select/dist/vue-select.css";
</style>-->
<style lang="scss" scoped>
    // @import "@vueform/multiselect/themes/default.css";
    // @import "vue-search-select/dist/VueSearchSelect.css";
    @import "../../../assets/scss/_variables";

    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }

    .bt-br-gray {
        border-top: 1px solid #ebeef5;
    }

    .bb-br-gray {
        border-bottom: 1px solid #ebeef5;
    }

    .el-select {
        display: inline-block;
        position: relative;
        vertical-align: middle;
        line-height: 32px;
        width: 100%;
    }

    .accent-text {
        color: #006EFB;
    }
</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }

    .el-form-item.is-required:not(.is-no-asterisk).asterisk-left > .el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk).asterisk-left > .el-form-item__label-wrap > .el-form-item__label:before {
        content: none !important;
    }
</style>
