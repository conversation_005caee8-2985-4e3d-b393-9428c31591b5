<template #default="scope">
  <div class="scrollable only-y p-2">
    <div class="page-header">
      <h1>Customer Email</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
        <el-breadcrumb-item>Customer</el-breadcrumb-item>
        <el-breadcrumb-item>Customer Email</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="card-base card-shadow--medium search-card scrollable only-y mb-5 p-10">
      <el-form ref="form" class="mb-50 bb-br-gray" :model="form" label-width="120px">
        <el-col v-if="actualCustomer" class="demo-form-inline flex" :span="24">
          <el-col :span="24" class="demo-form-inline">
            <el-form-item label="Customer">
              <el-input
                  placeholder="Customer"
                  :model-value="actualCustomer?.company_name"
                  disabled
              />
            </el-form-item>
          </el-col>
        </el-col>
        <el-col v-if="actualCustomer" class="demo-form-inline flex" :span="24">
          <el-col :span="12" class="demo-form-inline">
            <el-form-item label="Agreement">
              <el-select
                  disabled
                  v-model="agreement_no"
                  placeholder="Select agreement"
                  style="width: 100%"
              >
                <el-option :label="actualCustomer?.agreement_no" :value="actualCustomer?.agreement_no"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="demo-form-inline">
            <el-form-item label="Video">
              <el-select disabled v-model="video_id" @change="videoDetails()" filterable placeholder="Select a Video"
                         style="width: 100%" :loading="loading">
                <el-option
                    v-for="video in videoList"
                    :key="video.id"
                    :label="video.name"
                    :value="video.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col>
          <el-col :span="24">
            <el-form-item label="Email   ">
              <vue-quill-editor
                  v-model="summary"
                  :content="summary"
                  contentType="html"
                  ref="myQuillEditor1"
                  theme="bubble"
              >
              </vue-quill-editor>
            </el-form-item>
          </el-col>
        </el-col>
        <el-form-item>
          <button
              @click="submitForm"
              :disabled="buttonLoading"
              class="el-button el-button--submit btn-blue-bg"
              type="button"
          >
            {{ buttonLoading ? "Please wait ..." : "Send" }}
          </button>
          <el-button class="btn-blue-bg" @click="mailTemplate = true">Preview</el-button>
          <!-- <a class="el-button el-button--submit btn-blue-bg" href="/mail-template">Preview</a>  -->
          <el-button @click="$router.back()">Cancel</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog class="cai-dialog-wrapper" title="" v-model="dialogTableVisible">
      <CustomerAgreementInvoiceDialog :data="loadPrintData" :detail="printDetail"/>
    </el-dialog>
    <el-dialog title="" v-model="mailTemplate" class="p-0">
      <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">
        <el-form ref="form" :model="form" class="mb-50 bb-br-gray" label-width="120px">
          <!-- <Form class="form-box" @submit="onSubmit" :validation-schema="schema"> -->
          <div id="template">
            <div style="padding: 0 20px" class="stack-column">
              <table cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td style="padding: 20px 0px">
                    <table
                        cellspacing="0"
                        cellpadding="0"
                        border="0"
                        width="100%"
                        style="font-size: 14px; text-align: left"
                    >
                      <tr>
                        <td>
                          <div>
                            <img
                                style="width: 130px"
                                src="@/assets/images/Logo2.png"
                                alt="img"
                            />
                          </div>
                        </td>
                        <td>
                          <!--                                                    <span
                                                                                  style="
                                                                                      float: right;
                                                                                      display: flex;
                                                                                      align-items: center;
                                                                                      gap: 10px;
                                                                                      font-size: 14px;
                                                                                      letter-spacing: 1px;
                                                                                  "
                                                                              ><img
                                                                                  style="width: 16px; height: auto"
                                                                                  src="@/assets/images/globe.png"
                                                                                  alt="icon"
                                                                              />
                                                                                  <a style="text-decoration: none; color: #313131" href="#">
                                                                                      www.shipvagoo.com</a
                                                                                  ></span
                                                                              >-->
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </div>

            <table
                cellspacing="0"
                cellpadding="0"
                border="0"
                width="100%"
                class="data-tbl"
                style="
                                background: url('/src/assets/images/banner-bg.png');
                                padding: 20px 0;
                                border-radius: 20px;
                            "
            >
              <tr>
                <td>
                  <div style="padding: 32px; font-size: 16px">
<!--                    <h1 style="text-align: center; font-size: 28px; line-height: 26px">
                      Welcome To <span style="color: #1476cc">Shipvagoo</span>
                    </h1>-->
                    <span
                    >Velkommen
                                            <h3 style="display: inline; font-size: 22px">{{
                                                actualCustomer?.company_name
                                              }}</h3></span
                    >
                    <p style="margin-bottom: 10px">
                      <span v-html="summary"></span>
                    </p>
                  </div>
                </td>
              </tr>
            </table>

            <table
                cellspacing="0"
                cellpadding="0"
                border="0"
                width="100%"
                class="data-tbl"
                style="padding: 20px 0"
            >
              <tr>
                <td>
                  <ul
                      style="
                                            list-style: none;
                                            margin: 0;
                                            margin: 40px;
                                            padding: 0;
                                            border: 1px solid #ccc;
                                            border-radius: 10px;
                                        "
                  >
                    <li
                        style="
                                                border-bottom: 1px solid #ccc;
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;
                                                padding: 30px;
                                            "
                    >
                      <div>
                        <h3
                            style="
                                                        color: #1476cc;
                                                        margin: 0;
                                                        font-size: 22px;
                                                        line-height: 26px;
                                                        display: flex;
                                                        align-items: center;
                                                    "
                        >
                          Din fragtaftale
                          <img
                              src="@/assets/images/parcel.png"
                              style="width: 30px; margin-left: 20px"
                              alt="icon"
                          />
                        </h3>
                        <p>
                          <a
                              v-loading.fullscreen.lock="fullscreenLoading"
                              href="#" @click="getInvoiceData()" style="color: #006efb; margin-right: 3px">Klik her</a>
                          for at se din fragtaftale
                        </p>
                      </div>
                      <a href="#" style="display: block"
                      ><img style="width: 55px" src="@/assets/images/pdf.png" alt=""/>
                      </a>
                    </li>
                    <li
                        style="
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;
                                                padding: 30px;
                                            "
                    >
                      <div>
                        <h3
                            style="
                                                        color: #1476cc;
                                                        margin: 0;
                                                        font-size: 22px;
                                                        line-height: 26px;
                                                        display: flex;
                                                        align-items: center;
                                                    "
                        >
                          Kom i gang
                          <img
                              src="@/assets/images/rocket.png"
                              style="width: 30px; margin-left: 20px"
                              alt="icon"
                          />
                        </h3>
                        <p>
                          <a
                              href="#"
                              style="color: #006efb; margin-right: 3px"
                          > Klik her</a
                          >
                          for at oprette din
                        </p>
                      </div>
                      <a style="display: block" href="#"
                      ><img style="width: 55px" src="@/assets/images/onborad.png" alt=""
                      /></a>
                    </li>
                  </ul>
                </td>
              </tr>
            </table>

            <table
                cellspacing="0"
                cellpadding="0"
                border="0"
                width="100%"
                class="data-tbl"
                style="padding: 20px 0"
            >
              <tr>
                <td>
                  <div class="bg-sec">
                    <img v-if="preview.video_thumbnail" :src="preview.video_thumbnail" alt="img"/>
                    <img v-else src="@/assets/images/bg-sec.png" alt="img"/>
                    <a
                        style="
                                                display: flex;
                                                align-items: center;
                                                max-width: 160px;
                                                padding: 12px 12px;
                                                background-color: #006efb;
                                                color: #eff6ff;
                                                border-radius: 30px;
                                                position: absolute;
                                                left: 50%;
                                                top: 50%;
                                                transform: translate(-50%, -50%);
                                                width: 100%;
                                                cursor: pointer;
                                            "
                        :href="preview.video_url" target="_blank"><img
                        src="@/assets/images/play.png"
                        style="width: 35px; margin-right: 10px"
                        alt=""
                    />
                      Watch Now</a
                    >
                  </div>
                </td>
              </tr>
            </table>

            <table
                cellspacing="0"
                cellpadding="0"
                border="0"
                width="100%"
                class="data-tbl"
                style="padding: 20px 0px"
            >
              <tr>
                <td>
                  <div class="text-center" v-html="actualCustomer?.footer">

                  </div>
                </td>
              </tr>
            </table>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Affix from "@/components/Affix.vue"
import {defineComponent} from "@vue/runtime-core"
import {loadingService, merchantService, ubsendService, videoService} from "@/services/_singletons"
import {ElLoading} from "element-plus"
import VueQuillEditor from "@/components/vue-quill-editor.vue"
import CustomerAgreementInvoiceDialog from "../agreement/CustomerAgreementInvoiceDialog.vue"
import {useMainStore} from "@/stores/main"
import {find} from "lodash";
import {useHelper} from "@/composeable/Helper";

const {downloadAgreementPdf} = useHelper();
export default defineComponent({
  name: "CustomerEmail",
  props: {
    customer: {
      type: Object,
      required: false
    }
  },
  async created() {
    this.editData = useMainStore().getMerchant
    this.merchant = this.editData
    await this.init()
    this.countryList = useMainStore().getCountryList
    await this.getVideoList();
  },
  /*  setup(props) {
      return {
        customer: props.customer
      }
    },*/

  data() {
    return {
      fullscreenLoading: false,
      width: 0,
      height: "auto",
      merchantId: this.$route.params.merchantId,
      actualCustomer: {},
      customerId: this.$route.params.customerId,
      agreement_no: "",
      video_id: "",
      summary: "",
      buttonLoading: false,
      dialogTableVisible: false,
      mailTemplate: false, //Majid
      affixEnabled: true,
      videoList: [],
      editorOption1: {
        theme: "snow"
      },
      form: {
        video: "",
        agreement_no: "",
        company_name: "",
        body: ""
      },
      list: [],
      loading: false,
      printDetail: {},
      printData: [],
      editData: [],
      merchant: [],
      countryList: [],
      preview: {
        video_url: null,
        video_thumbnail: null
      },
    }
  },
  components: {
    VueQuillEditor,
    Affix,
    CustomerAgreementInvoiceDialog
  },
  computed: {
    loadPrintData() {
      return this.printData
    }
  },
  methods: {
    async submitForm() {
      const data = {
        user_code: this.merchantId,
        summary: this.summary,
        video_id: this.video_id
      }
      try {
        this.buttonLoading = true
        const res = await merchantService.postCustomerEmail(data)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        this.buttonLoading = false
        // await this.$router.push({ path: "/customer/customer-list" })
        await this.$router.back()
      } catch (e) {
        this.buttonLoading = false
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-right"
          })
          this.loading = false
          return
        } else if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.message ?? e.message,
            position: "top-right"
          })
          this.loading = false
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0] ?? e.response.data.error,
          position: "top-right"
        })
        this.loading = false
      }
    },
    async getVideoList() {
      // display form values on success
      try {
        const res = await videoService.getVideoList([{key: 'is_enabled', value: 1}])
        this.videoList = res.data.videos.map((item) => {
          return {
            id: item.id,
            name: item.name,
            url: item.url,
            video_thumbnail: item.video_thumbnail
          }
        });
        // this.video_id = this.merchant.agreement.video_id ? parseInt(this.merchant.agreement?.video_id) : null
        if (this.video_id == null || this.video_id == '') {
          const video_link_id = this.actualCustomer ? (this.actualCustomer.email_template ? parseInt(this.actualCustomer.email_template.email_video_link) : null) : null;
          const index = this.videoList.findIndex((item) => item.id == video_link_id);
          if (index > -1) {
            this.video_id = video_link_id;
          }
        }
        this.videoDetails();
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }

        this.$notify.error({
          title: "Error",
          message: "An Error has ocurred",
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    setCarrier() {
      //Majid
      this.mailTemplate = true

    },
    closeModal() {
      //Majid
      this.mailTemplate = false
      this.clearForm()
    },
    getCountryByIso(country) {
      let city = this.countryList.find(el => el.name.toLowerCase() === country.toLowerCase())
      if (city) {
        return city.iso
      } else {
        return false;
      }
    },
    async getInvoiceData() {
      try {
        const value = this.editData
        if (value.has_agreement === 0) {
          return
        }
        this.fullscreenLoading = true
        const userCode = value.user_code;
        await downloadAgreementPdf(userCode)
      } catch (e) {
      } finally {
        this.fullscreenLoading = false;
      }
      /* const value = this.editData
       if (value.has_agreement === 0) {
         return
       }
       this.printData = []
       let pricing = this.editData.agreement.pricing
       let modifyPricing = []
       let finalRes = []
       let date = moment(value.agreement.date).format("yyyy-MM-DD")

       pricing.forEach((res, idx) => {
         let toCountry = this.getCountryByIso(res.shipvagoo_pricing.ubsend_price.to_country)
         if (toCountry) {
           this.printData.push({
             desc: `DK >${toCountry} - ${res.price_group.carrier} - ${res.shipvagoo_pricing.ubsend_price.shipvagoo_prodct}  ${res.shipvagoo_pricing.ubsend_price.weight_class_from} - ${res.shipvagoo_pricing.ubsend_price.weight_class_to} kg`,
             price: Number(res.shipvagoo_pricing.ship_standard_list_price - res.discount).toFixed(2)
           })
         }
       })
       this.printDetail = {}
       this.printDetail = {
         name: value.company_name,
         date: value.agreement.date,
         summary: value.agreement.summary,
         footer: value.agreement.footer,
         address: value.address
       }
       this.dialogTableVisible = true*/
    },
    async init() {
      try {
        this.loading = true
        const res = await merchantService.getCustomerEmail({user_code: this.merchantId})
        this.actualCustomer = res.data.data
        this.agreement_no = res.data.data.agreement_no
        this.summary = this.merchant.agreement?.agreement_summary ? this.merchant.agreement?.agreement_summary : res.data.data.email_template.summary
        this.loading = false
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0] ?? e.response.data.error,
            position: "top-right"
          })
          this.loading = false
          return
        } else if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.message ?? e.message,
            position: "top-right"
          })
          this.loading = false
          return
        }
        if (e.response) {
          this.$notify.error({
            title: "Error",
            message: e.response?.data.error[0] ?? e.response?.data.error,
            position: "top-right"
          })
        }
        this.loading = false
      }
    },
    async onSubmit(values) {
      this.isDisabled = true
    },
    async videoDetails() {
      if (this.video_id) {
        const videoObject = find(this.videoList, {id: this.video_id});
        this.preview.video_url = videoObject.url
        this.preview.video_thumbnail = videoObject.video_thumbnail
      }


    }
  }
})
</script>

<style scoped type="text/css">
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@300;400;600;700;800;900&display=swap");

html,
body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  width: 100% !important;
  overflow-x: hidden;
  font-family: "Montserrat", sans-serif;
  color: #313131;
}

#template {
  max-width: 1140px;
}

p {
  font-family: "Nunito Sans", sans-serif;
}

/* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
table {
  border-spacing: 0 !important;
  border-collapse: collapse !important;
  table-layout: fixed !important;
  margin: 0 auto !important;
}

table table table {
  table-layout: auto;
}

.footer-list {
  margin: 40px;
  padding: 0;
  text-align: center;
  font-family: "Nunito Sans", sans-serif;
  list-style: decimal;
}

.footer-list li {
  display: inline-block;
  margin: 0 10px;
  position: relative;
}

.footer-list li::after {
  position: absolute;
  content: "";
  width: 6px;
  height: 6px;
  background-color: #313131;
  top: 8px;
  right: -15px;
  border-radius: 50%;
}

.footer-list li:last-of-type::after {
  display: none;
}

.footer-list li a {
  text-decoration: none;
  color: #313131;
}

.bg-sec {
  margin: 0 40px;
  position: relative;
}

.bg-sec img {
  display: block;
  height: auto;
  width: 100%;
}

#template {
  margin: auto;
}

@media screen and (max-width: 1200px) {
  #template {
    max-width: 100%;
  }
}

@media screen and (max-width: 580px) {
  h1 {
    font-size: 18px !important;
  }

  h3 {
    font-size: 16px !important;
  }

  p {
    font-size: 14px !important;
  }
}

.page-table {
  padding: 0% !important;
  margin-top: 2% !important;
  padding-right: 15px;
  padding-bottom: 20px;
  border: 1px solid #eeeeee;
  border-radius: 5px;
}

.table-box {
  overflow: auto;
}

.Font-Size1 {
  font-size: 14px !important;
}

.seacr-box {
  width: 58rem;
  max-width: 100%;
  padding: 10px 30px;
  border-radius: 5px;
  border: 1px solid #eeeeee;
  outline: none;
}

.mdi {
  padding-top: 2px;
  padding-left: 4px;
  color: #bbbbbb !important;
}

.form-box .el-form-item__label {
  width: 100px !important;
}

.quill-editor {
  width: 100% !important;
}
</style>
