<template>
  <div>
    <el-col class="demo-form-inline flex" :span="24" v-for="(data, index) in dynamicList" :key="index">
      <el-col class="demo-form-inline" :span="7">
        <el-form-item label="Country From">
          <el-select
              v-model="data.country_from"
              filterable
              placeholder=" "
          >
            <!--   @click="checkDuplicateHandle(data.country_from,'from')"              -->
            <el-option
                v-for="item in countryList"
                :key="item.country_code"
                :label="item.name"
                :value="item.country_code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col class="demo-form-inline" :span="7">
        <el-form-item label="Country To">
          <el-select
              v-model="data.country_to"
              filterable
              placeholder=" "
          >
            <el-option
                v-for="item in countryList"
                :key="item.id"
                :label="item.name"
                :value="item.country_code"
            />
          </el-select>
          <!--  @click="checkDuplicateHandle(data.country_to,'to')"            -->
        </el-form-item>
      </el-col>
      <el-col class="demo-form-inline" :span="8">
        <el-form-item label="Parcel Per Year">
          <el-col class="demo-form-inline">
            <el-input min="0" max="999999" v-model="data.parcels" type="number" placeholder=""></el-input>
          </el-col>
        </el-form-item>
      </el-col>
      <el-col class="demo-form-inline ml-6" :span="8">
        <i
            class="widget-icon mdi mdi-delete-circle-outline fs-25 cursor-pointer"
            style="color: #b22234"
            @click="deleteExperience(index)"
        ></i>
      </el-col>
    </el-col>
  </div>
</template>

<script>
export default {
  name: "CountryListAgreement",
  props: ['countryList', 'dynamicList'],
  setup(props) {
    return {
      countryList: props.countryList,
      dynamicList: props.dynamicList
    }
  },
  methods: {
    deleteExperience(index) {
      console.log('delete', index)
      this.$emit('deleteExperience', index)
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>