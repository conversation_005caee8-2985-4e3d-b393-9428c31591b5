<template>
    <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
        <div class="page-header">
            <h1>Customer Listing</h1>
            <!-- <h4>simple table</h4> -->
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                <el-breadcrumb-item>Customer</el-breadcrumb-item>
                <el-breadcrumb-item>Customer Listing</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="flex m-2">
            <div class="col-10 flex justify-flex-end pr-10">
                <el-checkbox-group v-model="selectedCheckedOptions">
                    <el-checkbox v-for="data in merchantChecked"
                                 :key="data.value"
                                 :label="data.label"
                                 :checked="data.checked"
                    >{{ data.label }}
                    </el-checkbox>
                </el-checkbox-group>

                <el-checkbox-group v-model="selectedStatus" class="ml-25">
                    <el-checkbox v-for="data in statusOptions"
                                 :key="data.value"
                                 :label="data.label"
                                 :checked="data.checked"
                    >{{ data.label }}
                    </el-checkbox>
                </el-checkbox-group>
            </div>
            <div class="col-2 flex justify-flex-end">
                <el-button class="btn-blue-bg" @click="goAdd">Create Customer</el-button>
            </div>
        </div>
        <div class="toolbar-box flex align-center">
            <div class="box grow">
                <search-box @search="(value)=>search=value"></search-box>
                <!--        <el-input placeholder="Search..." v-model="search" clearable></el-input>-->
            </div>
        </div>

        <div class="card-shadow--medium box grow" v-loading="!ready">
            <el-table
                    :data="list"
                    :height="height"
                    v-if="ready"
                    @selection-change="handleSelectionChange"
            >
                <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
                <el-table-column label="Customer ID" min-width="70" prop="id" :fixed="!isMobile">
                    <template #default="scope">
                        <span class="sel-string"
                              v-html="$options.filters.selected(scope.row.company_code, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="URL" min-width="120" prop="company_url" :fixed="!isMobile">
                    <template #default="scope">
                        <span class="sel-string"
                              v-html="$options.filters.selected(scope.row.company_url, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="Company Name" min-width="120" prop="company_name" :fixed="!isMobile">
                    <template #default="scope">
                        <router-link @click="setMerchant(scope.row)"
                                     :to="`/detail/${scope.row.user_code}/${scope.row.company_code}`"><span
                                class="sel-string"
                                v-html="$options.filters.selected(scope.row.company_name, search)"></span>
                        </router-link>
                        <!-- <a href="/detail"> <span class="sel-string" v-html="$options.filters.selected(scope.row.company_name, search)"></span> </a> -->
                    </template>
                </el-table-column>
                <!-- <el-table-column label="Phone" min-width="140" prop="phone">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.row.phone_no, search)"></span>
                    </template>
                </el-table-column> -->
                <el-table-column label="Onboarding Status" prop="onboarding_status" min-width="100">
                    <template #default="scope">
                        <span v-if="scope.row.onboarding_status ===0" plain>....</span>
                        <el-button v-if="scope.row.onboarding_status ===1" type="info" plain>Draft</el-button>
                        <el-button v-if="scope.row.onboarding_status ===2" type="primary" plain>Pending</el-button>
                        <el-button v-if="scope.row.onboarding_status ===3" :type="'success'" plain>Completed</el-button>
                    </template>
                </el-table-column>

                <el-table-column label="Agreement" prop="job_title" min-width="70">
                    <template #default="scope">
                        <el-tooltip
                                class="box-item"
                                effect="dark"
                                :content="scope.row.has_agreement ?'Agreement Added':'Agreement is not yet created. Please create agreement'"
                                placement="top-start"
                        >
                            <i
                                    v-loading.fullscreen.lock="fullscreenLoading"
                                    @click="getInvoiceData(scope.row)"
                                    :class="!scope.row.has_agreement ?'mdi mdi-file disable-icon ':'mdi mdi-file ' "></i>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="Status" prop="is_active" min-width="70">
                    <template #default="scope">
                        <el-switch v-model="scope.row.is_active"
                                   :active-value="1"
                                   :inactive-value="0"
                                   @change="updateCustomerStatus(scope.row)"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column label="Action">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>
                        <el-dropdown trigger="hover" @command="onCommand">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <!-- <el-dropdown-item @click="setMerchant(scope.row)" :command="'/customer/edit-customer/'+scope.row.user.user_code" divided>
                                         Assign Price Group
                                    </el-dropdown-item> -->
                                    <!--
                                     <el-dropdown-item v-if="scope.row.has_agreement ===false" @click="createAgreement(scope.row,false)"
                                               :command="'/customer/add-customer-agreement'" divided>
                               Create Agreement
                             </el-dropdown-item>

                             <el-dropdown-item v-else @click="createAgreement(scope.row,true)"
                                                                 :command="`/customer/update-customer-agreement/${scope.row.agreement.id}`"
                                                                 divided>
                                                 Update Agreement
                                               </el-dropdown-item>
                                               <el-dropdown-item @click="setMerchant(scope.row)"
                                                                 v-if="scope.row.is_email_sent ===false && (scope.row.onboarding_status===1 || scope.row.onboarding_status===2)"
                                                                 :command="'/customer/customer-email/'+scope.row.user_code+'/'+scope.row.id">
                                                 Send Onboarding Email
                                               </el-dropdown-item>
                                               -->
                                    <el-dropdown-item @click="setMerchant(scope.row)"
                                                      :command="'/customer/edit-customer/'+scope.row.id"
                                                      divided>
                                        Update Customer
                                    </el-dropdown-item>
                                    <!--                  <el-dropdown-item @click="setMerchant(scope.row)"
                                                                        v-if="scope.row.is_email_sent ===true && (scope.row.onboarding_status===1 || scope.row.onboarding_status===2)"
                                                                        :command="'/customer/customer-email/'+scope.row.user_code+'/'+scope.row.id" divided>
                                                        {{ scope.row.onboarding_status == 1 ? 'Send Onboarding Email' : 'Resend Onboarding Email' }}
                                                      </el-dropdown-item>-->
                                    <el-dropdown-item @click="deleteMerchant(scope.row)" :command="'skip'" divided>
                                        Delete
                                    </el-dropdown-item>
                                    <!-- <el-dropdown-item   @click="deleteMerchant(scope.row.user.user_code)" :command="'skip'" divided>
                                        Delete
                                    </el-dropdown-item> -->
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>

            <!--<el-pagination
                    class="flex"
                    ref="pgi"
                    v-if="ready"
                    :small="pagination.small"
                    v-model:current-page="pagination.page"
                    :page-sizes="pagination.sizes"
                    v-model:page-size="pagination.size"
                    :layout="pagination.layout"
                    :total="total"
            ></el-pagination>-->
            <el-pagination
                    class="flex"
                    ref="pagin"
                    v-if="ready"
                    small="false"
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :current-page="pagination.page"
                    :page-size="pagination.size"
                    layout="total, ->, prev, pager, next, jumper"
                    :total="pagination.total"
            ></el-pagination>
        </div>
        <el-dialog class="cai-dialog-wrapper" title="" v-model="dialogTableVisible">
            <CustomerAgreementInvoiceDialog :data="loadPrintData" :detail="printDetail"/>
        </el-dialog>
    </div>
</template>

<script>
    // import users from "@/assets/data/USERS_MOCK_DATA.json"
    import _ from "lodash"
    import dayjs from "dayjs"
    import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
    import {
        merchantService,
        loadingService, carrierService
    } from "../../../services/_singletons"
    import {useMainStore} from "@/stores/main"
    import {defineComponent} from "@vue/runtime-core"
    import CustomerAgreementInvoiceDialog from "../agreement/CustomerAgreementInvoiceDialog.vue"
    import moment from "moment/moment";
    import {useHelper} from "../../../composeable/Helper";

    const {downloadAgreementPdf} = useHelper();

    export default defineComponent({
        name: "MerchantList",
        data() {
            return {
                fullscreenLoading:false,
                isMobile: false,
                ready: false,
                width: 0,
                height: "auto",
                loading: false,
                search: "",
                countryList: [],
                pagination: {
                    page: 1,
                    size: 20,
                    sizes: [10, 15, 20, 30, 50, 100],
                    layout: "total, ->, prev, pager, next, jumper, sizes",
                    small: false,
                    total: 10
                },
                list: [],
                editMode: false,
                itemsChecked: [],
                printData: [],
                dialogUserVisible: false,
                currentId: 0,
                dayjs,
                checkAll: false,
                selectedCheckedOptions: [],
                selectedStatus: [],
                ckoptions: ["Not Started", "Draft", "Pending", "Completed", "All"],
                statusOptions: [
                    {
                        label: 'Active',
                        value: 1,
                        checked: false,
                    },
                    {
                        label: 'Inactive',
                        value: 0,
                        checked: false,
                    },
                ],
                merchantChecked: [
                    {
                        label: 'Not Started',
                        value: 0,
                        checked: false,
                    },
                    {
                        label: 'Draft',
                        value: 1,
                        checked: false,
                    },
                    {
                        label: 'Pending',
                        value: 2,
                        checked: false,
                    },
                    {
                        label: 'Completed',
                        value: 3,
                        checked: false,
                    }
                ],
                isIndeterminate: true,
                dialogTableVisible: false,
                printDetail: {},
                footer: null,
            }
        },
        computed: {

            listFiltered() {
                return this.list.filter(obj => {
                    let ctrl = false
                    for (let k in obj) {
                        if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                    }
                    return ctrl
                })
            },
            listSortered() {
                let prop = this.sortingProp
                let order = this.sortingOrder
                return [].concat(
                    this.listFiltered.sort((item1, item2) => {
                        let val1 = ""
                        let val2 = ""

                        val1 = item1[prop]
                        val2 = item2[prop]
                        if (order === "descending") {
                            return val2 < val1 ? -1 : 1
                        }
                        return val1 < val2 ? -1 : 1
                    })
                )
            },
            listInPage() {
                let from = (this.currentPage - 1) * this.itemPerPage
                let to = from + this.itemPerPage * 1
                //return this.listSortered.slice(from, to)
                return this.listFiltered.slice(from, to)
            },
            total() {
                return this.listFiltered.length
            },
            currentPage: {
                get() {
                    return this.pagination.page
                },
                set(val) {
                    this.pagination.page = val
                }
            },
            itemPerPage() {
                return this.pagination.size
            },
            selectedItems() {
                return this.itemsChecked.length || 0
            },
            getCheckedValue() {
                return this.selectedCheckedOptions
            },
            loadPrintData() {
                return this.printData
            },
        },
        watch: {
            selectedCheckedOptions(newSelected, oldSelected) {
                this.prepareFilterAndStatus(Object.values(this.selectedStatus), Object.values(newSelected), {
                    currentPage: 1,
                    perPage: this.pagination.size
                });
            },
            selectedStatus(newSelected, oldSelected) {
                this.prepareFilterAndStatus(Object.values(newSelected), Object.values(this.selectedCheckedOptions), {
                    currentPage: 1,
                    perPage: this.pagination.size
                });
            },
            itemPerPage(val) {

                this.currentPage = 1

                setTimeout(() => {
                    this.ready = true
                }, 500)
            },
            search(val) {
                if (this.selectedCheckedOptions.length || this.selectedStatus.length) {
                    this.prepareFilterAndStatus(Object.values(this.selectedStatus), Object.values(this.selectedCheckedOptions), {
                        currentPage: this.pagination.page,
                        perPage: this.pagination.size,
                        search: val
                    });
                } else {
                    this.getMerchantList(1, this.pagination.size, val);
                }
            }
        },
        methods: {
            handleSizeChange(size) {
                if (this.selectedCheckedOptions.length || this.selectedStatus.length) {
                    this.prepareFilterAndStatus(Object.values(this.selectedStatus), Object.values(this.selectedCheckedOptions), {
                        currentPage: this.pagination.page,
                        perPage: this.pagination.size
                    });
                } else {
                    this.getMerchantList(this.pagination.page, size)
                }
            },
            changeCurrentPage(page) {
                if (this.selectedCheckedOptions.length || this.selectedStatus.length) {
                    this.prepareFilterAndStatus(Object.values(this.selectedStatus), Object.values(this.selectedCheckedOptions), {
                        currentPage: page,
                        perPage: this.pagination.size
                    });
                } else {
                    this.getMerchantList(page, this.pagination.size)
                }
            },
            async prepareFilterAndStatus(statuses, filters, paginationPayload = {
                currentPage: 1,
                perPage: 10,
                search: ''
            }) {
                let formData = new FormData();
                let length = 0;
                if (statuses.length > 0) {
                    statuses.map((row, i) => {
                        this.statusOptions.map(function (data, key) {
                            if (row == data.label) {
                                formData.append(`is_active[${data.value}]`, data.value)
                                length = length + 1;
                            }
                        });
                    })
                }
                if (filters.length > 0) {
                    filters.map((row, i) => {
                        this.merchantChecked.map(function (data, key) {
                            if (row == data.label) {
                                formData.append(`filters[${data.value}]`, data.value)
                                length = length + 1;
                            }
                        });
                    })
                }
                formData.append('page', paginationPayload.currentPage);
                formData.append('perpage', paginationPayload.perPage);
                formData.append('search', this.search);
                if (length > 0) {
                    await this.handleMerchantChange(formData)
                } else {
                    await this.handleMerchantChange();
                }
                console.log("Length ", length)
            },
            async updateCustomerStatus(item) {
                const requestPayload = {user_code: item.user_code, is_active: item.is_active}
                const res = await merchantService.updateCustomerStatus(requestPayload)
                this.$notify({
                    title: "Success",
                    message: res.message,
                    type: "success"
                })
            },
            unCheckedStatus(value) {
                let options = [];
                this.ckoptions.map((row, index) => {
                    if (row !== value) {
                        options.push(row);
                    }
                })
                return options;
            },
            getKeyByStatus(value) {
                let obj = {};
                this.merchantChecked.map((row, key) => {
                    if (row.label === value) {
                        obj = {value: row.value, index: key};
                    }
                })
                return obj;
            },
            async getInvoiceData(value) {
                if (value.has_agreement === 0) {
                    return;
                }
                try {
                    this.fullscreenLoading = true
                    const userCode = value.user_code;
                    await downloadAgreementPdf(userCode)
                } catch (e) {
                } finally {
                    this.fullscreenLoading = false;
                }
                /* this.printData = []
                 let pricing = [...value.agreement.pricing]
                 pricing.forEach((res, idx) => {
                     // res.
                     let toCountry = this.getCountryByIso(res.shipvagoo_pricing.ubsend_price.to_country)
                     if (toCountry) {
                         const price = Number(res.shipvagoo_pricing.ship_standard_list_price - res.discount).toFixed(2);
                         const obj = {
                             desc: `DK >${toCountry} - ${res.price_group.carrier} - ${res.shipvagoo_pricing.ubsend_price.shipvagoo_prodct}  ${res.shipvagoo_pricing.ubsend_price.weight_class_from} - ${res.shipvagoo_pricing.ubsend_price.weight_class_to} kg`,
                             price: price
                         };
                         this.printData.push(obj)
                     }
                 })
                 this.printDetail = {}
                 this.printDetail = {
                     name: value.company_name,
                     date: value.agreement.date,
                     summary: value.agreement.summary,
                     footer: this.footer,
                     address: value.address
                 }
                 this.dialogTableVisible = true*/
            },
            getCountryByIso(country) {
                let city = this.countryList.find(el => el.name.toLowerCase() === country.toLowerCase())
                if (city) {
                    return city.iso
                } else {
                    return false;
                }
            },
            async handleMerchantChange(formData) {
                this.ready = false
                try {
                    const res = await merchantService.getMerchantListFilter(formData);
                    this.pagination.page = res.data.currentPage
                    this.pagination.size = res.data.perPage
                    this.pagination.total = res.data.total;
                    this.list = res.data.companies ?? []
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    this.ready = true;
                    loadingService.stopLoading("main-loader:login")
                }

            },
            handleCheckedCitiesChange(value) {

                let checkedCount = value.length;
                // this.selectedCheckedOptions = checkedCount === this.ckoptions.length;
                // console.log(value,value.includes('all'))
                // this.isIndeterminate = checkedCount > 0 && checkedCount < this.ckoptions.length;
                // if(value.length === this.ckoptions.length)
                // {
                //     console.log(value,this.selectedCheckedOptions)
                // }
                if (value.includes('All')) {
                    this.selectedCheckedOptions = this.ckoptions
                }
                if (!value.includes('All') && value.length <= 2) {
                    this.selectedCheckedOptions = value.filter(item => item !== 'All')
                    // value.filter(item => item !=='all
                } else if (value.includes('Completed') && value.includes('Pending') && !value.includes('All')) {
                    this.selectedCheckedOptions = this.ckoptions

                } else if (value.includes('All') && value.includes('Completed') || value.includes('All') && value.includes('Pending')) {
                    // if(value.includes('all')){
                    this.selectedCheckedOptions = value.filter(item => item !== 'All')
                }
                // else{
                //     this.selectedCheckedOptions= this.ckoptions
                // }

            },
            calcDims() {
                const tableWrapper = document.getElementById("table-wrapper")
                if (tableWrapper) this.width = tableWrapper.clientWidth

                if (!this.isMobile && tableWrapper) {
                    this.height = tableWrapper.clientHeight - 44
                }

                if (this.width < 480) {
                    this.pagination.small = true
                    this.pagination.layout = "prev, pager, next"
                } else if (this.width >= 480 && this.width < 700) {
                    this.pagination.small = false
                    this.pagination.layout = "prev, pager, next, ->, sizes"
                } else {
                    this.pagination.small = false
                    this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
                }

                // this.ready = true
            },
            handleResize: _.throttle(function (e) {
                this.ready = false
                this.width = 0
                setTimeout(this.calcDims, 1000)
            }, 500),
            handleSelectionChange(val) {
                this.itemsChecked = val
            },
            init() {
                this.getMerchantList()
                if (window.innerWidth <= 768) this.isMobile = true
            },
            createAgreement(data, update) {
                data['update'] = update
                useMainStore().setCustomer(data)
            },
            viewAllAgreement(data) {
                const all = this.list
                this.list = this.list.filter(item => item.company_name == data.company_name)
            },
            customerEmail(data) {
                useMainStore().setCustomer(data)
            },
            async getMerchantList(page = 1, perPage = 10) {
                // display form values on success
                try {
                    this.ready = false;
                    loadingService.startLoading('main-loader:login');
                    const params = {
                        page: page,
                        perPage: perPage,
                        search: this.search
                    }
                    const res = await merchantService.getMerchantList(params);
                    this.pagination.page = res.data.currentPage
                    //this.lastPage = res.data.lastPage
                    this.pagination.size = res.data.perPage
                    this.pagination.total = res.data.total;

                    this.list = res.data.companies
                    this.footer = res.data.footer;
                    console.log("This List ", this.list)
                } catch (e) {
                    console.log("Error ", e)
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    this.ready = true
                    loadingService.stopLoading("main-loader:login")
                }

            },
            goAdd() {
                this.$router.push({name: "AddMerchantPage"})
            },
            setMerchant(item) {
                console.log("Item ", item);
                useMainStore().setCustomer(item)
                useMainStore().setMerchant(item)
            },
            async delete(user_code) {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                try {
                    const res = await merchantService.deleteMerchant({'user_code': user_code});
                    this.$notify({
                        title: "Success",
                        message: res.message,
                        type: "success"
                    })
                    this.list = this.list.filter((el) => el.user_code !== user_code)
                    loading.close();
                } catch (e) {
                    loading.close();
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async deleteMerchant(data) {
                try {
                    await this.$confirm("All Price Groups and other data linked to this UBsend account will be deleted. Do you really wants to delete this account?", "Warning", {
                        confirmButtonText: "Yes",
                        cancelButtonText: "Cancel",
                        type: "warning",
                        center: true
                    })
                        .then(async () => {
                            await this.delete(data.user_code)
                        })
                        .catch(() => {
                        })


                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            onCommandLang(lang) {
                if (lang.charAt(0) === "/") this.onCommand(lang)
                else this.lang = lang
            },
            onCommand(path) {
                if (path !== 'skip')
                    this.$router.push(path)
            },
            async getCountryList() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await merchantService.getCountryList()
                    this.countryList = res.data.countries
                    this.allCountries = this.countryList
                    useMainStore().setCountryList(res.data.countries)
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
        },
        filters: {
            selected: function (value, sel) {
                if (!value) return ""
                if (!sel) return value

                value = value.toString()
                sel = sel.toString()

                const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
                if (startIndex !== -1) {
                    const endLength = sel.length
                    const matchingString = value.substr(startIndex, endLength)
                    return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
                }
                //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
                return value
            }
        },
        created() {
            this.init()
            this.getCountryList()
        },
        mounted() {
            //ie fix
            if (!window.Number.parseInt) window.Number.parseInt = parseInt
            this.calcDims()
        },
        components: {ResizeObserver, CustomerAgreementInvoiceDialog}
    })
</script>

<style lang="scss" scoped>
    @import "../../../assets/scss/_variables";


    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }
</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }
</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }

    .el-pagination {
        .elpagination__rightwrapper {
            display: flex !important;
            --flex-grid: 12;
        }
    }

    button:disabled,
    button[disabled] {
        border: 1px solid #999999;
        background-color: #cccccc;
        color: #666666;
    }


    .disable-icon {
        color: lightgrey;
    }

    // .disabled-icon {
    //   display: inline-block;
    //   position: relative;
    // //   border: $line-width solid currentColor;
    //   border-radius: 50%;
    //   font-size: 0.4px;
    //   width: 50em;
    //   height: 50em;
    //   color: #cccccc;

    //   &::after {
    //     content: '';
    //     width: 34em;
    //     position: absolute;
    //     left: 50%;
    //     top: 50%;
    //     margin-left: -17em;
    //     margin-top: -2em;
    //     background-color: currentColor;
    //     border-radius: 3px;
    //   }

    //   &--big {
    //     font-size: 0.85px;
    //   }
    // }
</style>
