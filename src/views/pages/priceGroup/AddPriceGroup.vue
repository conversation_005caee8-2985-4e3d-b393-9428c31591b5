<template>
    <div class="scrollable only-y p-2">
        <div class="toolbar-box flex align-center">
            <div class="page-header">
                <h1 class="ml-2">Price Group</h1>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                    <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
                    <el-breadcrumb-item>Price Group</el-breadcrumb-item>
                    <el-breadcrumb-item>Add Price Group</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>
        <div class="card-base card-shadow--medium p-15">
            <div class="bb-bt-br">
                <div class="search-card scrollable only-y mt-2">
                    <el-form ref="form" :model="form" label-width="120px">
                        <!-- <Form class="form-box" @submit="onSubmit" :validation-schema="schema"> -->
                        <h4 class="ml-12">Price Group</h4>
                        <el-col class="demo-form-inline flex" :span="24">
                            <el-col class="demo-form-inline" :span="12">
                                <el-form-item label="Price Group Id">
                                    <el-input :disabled="disableFd" v-model="form.price_group_name"
                                              placeholder="Price Group Id"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col class="demo-form-inline" :span="12">
                                <el-form-item label="Type">
                                    <el-select :disabled="$route.params.id" filterable v-model="form.type"
                                               placeholder="Type"
                                               class="w-100">
                                        <template v-for="data in typeList" :key="data">
                                            <el-option :label="data.name" :value="data.id"></el-option>
                                        </template>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-col>

                        <el-col class="demo-form-inline" :span="24">
                            <el-form-item label="UBsend Account">
                                <el-select
                                        :disabled="disableFd"
                                        v-model="form.ubsend_account_id"
                                        placeholder="UBsend Account"
                                        @change="getCarriersList()"
                                        class="w-100"
                                >
                                    <template v-for="data in ubsendList" :key="data">
                                        <el-option :label="data.account_name" :value="data.id"></el-option>
                                    </template>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col class="demo-form-inline flex" :span="24">
                            <el-col class="demo-form-inline" :span="12">
                                <el-form-item label="Carrier">
                                    <el-select
                                            :disabled="disableFd"
                                            v-model="form.carrier"
                                            placeholder="Carrier"
                                            @change="getPriceList()"
                                            class="w-100"
                                    >
                                        <template v-for="data in carrierList" :key="data">

                                            <el-option
                                                    :label="data.carrier"
                                                    :value="data.carrier"
                                                    :class="data.isPriceGroupExists ===true ?'noclick is-disabled' :''"
                                                    v-bind:disabled="data.isPriceGroupExists ===1 ?'disabled' :false"
                                            >
                                            </el-option>
                                        </template>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col class="demo-form-inline" :span="12">
                                <el-form-item label="Description">
                                    <el-input v-model="form.description" placeholder="Description"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-col>

                        <div class="bt-br"></div>
                        <div v-if="loading2 === false">
                            <div
                                    v-for="(record, index) in pricesList"
                                    :key="index"
                                    class="card-base card-shadow--medium demo-box bg-white mb-5"
                            >
                                <el-collapse value="index">
                                    <el-collapse-item
                                            :title="'Denmark - ' + index"
                                            name="index"
                                            class="el-collapse-bg-gray"
                                    >
                                        <div
                                                class="table-box card-base card-shadow--medium box grow"
                                                id="table-wrapper"
                                                v-loading="!ready"
                                        >
                                            <el-table
                                                    :data="record"
                                                    style="width: 100%"
                                                    :height="height"
                                                    v-if="ready"
                                                    table-layout="auto"
                                                    @selection-change="handleSelectionChange"
                                            >
                                                <el-table-column
                                                        label="SN"
                                                        min-width="45"
                                                        prop="s_no"
                                                        :fixed="!isMobile"
                                                >
                                                    <template #default="scope">
                                                        <span class="sel-string" v-html="scope.$index + 1"></span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column
                                                        label="Monty Product"
                                                        prop="monty_product"
                                                        min-width="150"
                                                        :fixed="!isMobile"
                                                ></el-table-column>
                                                <el-table-column
                                                        label="Shipvagoo Product"
                                                        prop="shipvagoo_prodct"
                                                        min-width="150"
                                                        :fixed="!isMobile"
                                                ></el-table-column>
                                                <el-table-column
                                                        label="Weight From"
                                                        prop="weight_class_from"
                                                        :fixed="!isMobile"
                                                        min-width="80"
                                                >
                                                    <template #default="scope">
                                                        <span
                                                                class="sel-string"
                                                                v-html="scope.row.weight_class_from"
                                                        ></span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column
                                                        label="Weight To"
                                                        prop="weight_class_to"
                                                        min-width="80"
                                                        :fixed="!isMobile"
                                                >
                                                    <template #default="scope">
                                                        <span
                                                                class="sel-string"
                                                                v-html="scope.row.weight_class_to"
                                                        ></span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column
                                                        label="Cost Price"
                                                        prop="cost_price"
                                                        min-width="65"
                                                        :fixed="!isMobile"
                                                >
                                                    <template #default="scope">
                                                        <span
                                                                class="sel-string"
                                                                v-html="formatFixedDecimal(scope.row.cost_price) ?? 0"
                                                        ></span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column
                                                        :fixed="!isMobile"
                                                        label="Currency"
                                                        prop="currency"
                                                        min-width="100"
                                                ></el-table-column>
                                                <el-table-column
                                                        label="Lead Time (Days)"
                                                        prop="lead_time"
                                                        min-width="100"
                                                ></el-table-column>
                                                <el-table-column label="Markup %" prop="markup" min-width="110">
                                                    <template #default="scope">
                                                        <!--   @input="scope.row.markup=formatFixedDecimal(scope.row.markup)"
                                                             -->
                                                        <el-input
                                                                type="number" step="any"
                                                                v-model="scope.row.markup"
                                                                min="0"
                                                                v-on:blur="addToForm(scope.row)"
                                                        ></el-input>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column
                                                        label="Min. List Price excl. VAT"
                                                        prop="cost_price_plus_markup"
                                                        min-width="120"
                                                >
                                                    <template #default="scope">
                                                       <span
                                                               class="sel-string"
                                                               v-html="scope.row.cost_price_plus_markup =(parseFloat(scope.row.cost_price) + parseFloat( scope.row.cost_price*scope.row.markup/100)).toFixed(2)"
                                                       ></span>
                                                    </template>
                                                </el-table-column>

                                                <el-table-column
                                                        label="List Price excl. VAT"
                                                        prop="ship_standard_list_price"
                                                        min-width="120"
                                                >
                                                    <template #default="scope">
                                                        <!-- onkeypress="return event.key === 'Enter' || (Number(event.key) >= 0 && Number(event.key) <= 9)" -->
                                                        <!-- @input="scope.row.customer_price = scope.row.customer_price <= scope.row.cost_price_plus_markup ? scope.row.cost_price_plus_markup:scope.row.customer_price" -->
                                                        <!--  @blur="scope.row.customer_price = scope.row.customer_price <= scope.row.cost_price_plus_markup ? formatFixedDecimal(scope.row.cost_price_plus_markup):formatFixedDecimal(scope.row.customer_price)"
                                                            @input="scope.row.customer_price = scope.row.customer_price <= scope.row.cost_price_plus_markup ? formatFixedDecimal(scope.row.cost_price_plus_markup):formatFixedDecimal(scope.row.customer_price)" -->
                                                        <!-- <el-input
                                                            type="number" step="any"
                                                            :class="{'error-boarder' : true}"
                                                            v-model="scope.row.customer_price"
                                                            name="customer_price"
                                                            min="0"
                                                        ></el-input> -->

                                                        <div class="el-input" data-v-3dd33bdf="">
                                                            <div
                                                                    :class="scope.row.ship_standard_list_price < scope.row.cost_price_plus_markup?'input-wrapper-error':'el-input__wrapper'"
                                                            >
                                                                <input
                                                                        class="el-input__inner"
                                                                        v-model="scope.row.ship_standard_list_price"
                                                                        step="any" min="0" type="number"
                                                                        v-on:blur="addToForm(scope.row)"
                                                                        autocomplete="off"
                                                                        tabindex="0"
                                                                >

                                                            </div>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column label="Gross Profit" prop="gross_profit"
                                                                 min-width="120">
                                                    <template #default="scope">
                                                        <span class="sel-string"
                                                              v-html="scope.row.gross_profit = ((scope.row.ship_standard_list_price-scope.row.cost_price).toFixed(2))  >0? formatFixedDecimal(scope.row.ship_standard_list_price -scope.row.cost_price):'0'">
                                                        </span>
                                                    </template>
                                                </el-table-column>
                                                <!-- <el-table-column
                                                    label="Control Condition(Price excl. Vat >Cost Price +Markup"
                                                    prop="control_condition"
                                                    min-width="150"
                                                >
                                                    <template #default="scope">
                                                        <el-input
                                                            v-model="scope.row.control_condition"
                                                            placeholder="control Condition"
                                                        ></el-input>
                                                    </template>
                                                </el-table-column> -->
                                            </el-table>
                                        </div>
                                    </el-collapse-item>
                                </el-collapse>
                            </div>
                        </div>
                        <div v-else>
                            <el-form-item v-loading="loading2"></el-form-item>
                        </div>
                    </el-form>
                    <el-form-item v-if="ready && !isAgreement" class="mt-30">
                        <el-button class="btn-blue-bg" @click="onSubmit">Save</el-button>
                        <el-button @click="onCanel">Cancel</el-button>
                    </el-form-item>
                    <el-form-item v-else-if="ready" class="mt-30">
                        <el-button @click="onCanel">Go Back</el-button>
                    </el-form-item>

                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import _ from "lodash"
    import dayjs from "dayjs"
    import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
    import {useMainStore} from "@/stores/main"
    import {defineComponent} from "@vue/runtime-core"
    import {priceGroupService, ubsendService, loadingService} from "../../../services/_singletons"
    import {Form, Field, ErrorMessage} from "vee-validate"
    import * as Yup from "yup"

    const schema = Yup.object().shape({
        ship_standard_list_price: Yup.number().required("ClientID shold be greater then zero or minimum three digit").test('len', 'ClientID shold be greater then zero or minimum three digit', val => val.toString().length >= 3),
    })

    export default defineComponent({
        name: "PriceGroup",
        data() {
            return {
                schema,
                isMobile: false,
                ready: false,
                width: 0,
                height: "auto",
                loading: false,
                loading2: false,
                search: "",
                edit: null,
                list: [],
                editMode: false,
                itemsChecked: [],
                ubsendList: [],
                carrierList: [],
                pricesList: [],
                costPrice: 0,
                typeList: [
                    {
                        id: 1,
                        name: "Pickup"
                    },
                    {
                        id: 2,
                        name: "DropOff"
                    }
                ],
                dialogUserVisible: false,
                isAgreement: false,
                currentId: 0,
                updateCarrier: null,
                defulatPercantage: 0.3,
                dayjs,
                form: {
                    price_group_name: "DKPG",
                    type: "",
                    ubsend_account_id: "",
                    carrier: "",
                    description: "",
                    pricing: [],
                    client_id: "1",
                    username: "ashento",
                    password: "Aurora",
                    price_group_id: this.$route.params.id ? this.$route.params.id : ""
                },
                pagination: {
                    page: 1,
                    size: 20,
                    sizes: [10, 15, 20, 30, 50, 100],
                    layout: "total, ->, prev, pager, next, jumper, sizes",
                    small: false
                }
            }
        },
        computed: {
            listFiltered() {
                return this.list.filter(obj => {
                    let ctrl = false
                    for (let k in obj) {
                        if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                    }
                    return ctrl
                })
            },
            listInPage() {
                let from = (this.currentPage - 1) * this.itemPerPage
                let to = from + this.itemPerPage * 1
                //return this.listSortered.slice(from, to)
                return this.listFiltered.slice(from, to)
            },
            total() {
                return this.listFiltered.length
            },
            currentPage: {
                get() {
                    return this.pagination.page
                },
                set(val) {
                    this.pagination.page = val
                }
            },
            itemPerPage() {
                return this.pagination.size
            },
            editData() {
                return useMainStore().getPriceGroup
            },
            disableFd() {
                return this.$route.params.id != undefined ? true : false
            }
        },
        watch: {},
        methods: {
            formatFixedDecimal(value) {
                return parseFloat(value).toFixed(2).replace(/(\d)(?=(\d{3})+(?:\.\d+)?$)/g, "$1,")
                // return parseFloat(value).toFixed(2).replace(/\.00$/, '')
            },
            getMarkup(num1) {
                return num1 + num1 * 9
            },
            getProfit(num1, num2) {
                return num1 - num2
            },
            onEdit(id) {
                this.edit = this.edit !== id ? id : null
            },
            handleSelectionChange(val) {
                this.itemsChecked = val
            },
            calculatePrice(ship_standard_list_price, rowObj) {
                // this.itemsChecked = val
                console.log(ship_standard_list_price, rowObj)
                if (ship_standard_list_price >= rowObj.costPriceWithMarkup) {
                    this.pricesList[rowObj.title].ship_standard_list_price = ship_standard_list_price
                } else {
                    this.pricesList[rowObj.title].ship_standard_list_price = rowObj.costPriceWithMarkup
                }
            },
            init() {
                if (window.innerWidth <= 768) this.isMobile = true
            },

            async onSubmit(values) {
                // display form values on success
                try {
                    let pricing = []
                    Object.keys(this.pricesList).forEach((value, index) => {
                        pricing.push(...this.pricesList[value])
                    })
                    let ret = false
                    this.form.pricing.forEach((val, idx) => {
                        if (val.ship_standard_list_price <= val.cost_price_plus_markup) {
                            ret = true;
                        }
                    })

                    if (ret === true) {
                        this.$confirm("List price excl. VAT should not be less than Min. List Price excl. VAT", "Warning", {
                            confirmButtonText: "OK",
                            cancelButtonText: "Cancel",
                            type: "errorr",
                            center: true
                        })
                            .then(() => {
                                return

                            })
                            .catch(() => {
                            })
                        return
                    }

                    this.addPriceGroup()
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })

                    // notificationService.showError('Error during login');
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },

            async addPriceGroup() {
                try {
                    loadingService.startLoading("main-loader:login")
                    if (this.$route.params.id) {
                        this.update()
                        return
                    }
                    // let res = await priceGroupService.addPriceGroup(this.form)
                    // this.$notify({
                    //     title: "Success",
                    //     message: res.message,
                    //     type: "success"
                    // })
                    // await this.$router.push({ path: "/price-group/list" })
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getCarriersList() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await priceGroupService.getCarrierList({ubsend_account_id: this.form.ubsend_account_id})
                    this.carrierList = res.data.carriers
                    if (this.$route.params.id) {
                        const selectedCarrier = this.carrierList.filter(item =>
                            item.carrier === this.editData["carrier"] ? item.carrier : null
                        )
                        this.form.carrier = selectedCarrier[0].carrier
                        this.updateCarrier = selectedCarrier[0].carrier

                        // this.getPriceList()
                    }

                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getPriceList() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    this.loading2 = true
                    const res = await priceGroupService.getPriceList({
                        ubsend_account_id: this.form.ubsend_account_id,
                        carrier: this.form.carrier
                    })
                    const resp = res.data.prices
                    let newRs = []
                    Object.keys(resp).map(index => {
                        let country = resp[index]
                        const result = country.map((value, idx) => {
                            let cp = parseInt(value.cost_price)
                            let min_ship_st_price = 0
                            let customer_price = 0
                            let markup = 0
                            let costPrice = cp === 0 || typeof cp === "string" ? 0 : cp + cp * this.defulatPercantage
                            let marginPer =
                                min_ship_st_price === 0 || costPrice === 0 || cost_price === "string"
                                    ? 0
                                    : min_ship_st_price - costPrice
                            // customer_price is List Price excl. VAT
                            // Gross Profit  = List Price excl. VAT - Cost Price
                            let grossProfit = customer_price === 0 ? 0 : customer_price - cp;
                            // costPriceWithMarkup  is Min List Price exc. VAT
                            //  Min List Price exc. VAT  = Cost Price + (Cost Price * Markup%)
                            let costPriceWithMarkup = cp + (cp * markup)

                            return {
                                title: index,
                                id: value.id,
                                ubsend_price_group_id: typeof value.ubsend_price_group_id === 'undefined' ? value.id : value.ubsend_price_group_id,
                                weight_class_from: value.weight_class_from,
                                weight_class_to: value.weight_class_to,
                                cost_price: value.cost_price,
                                markup: 0,
                                monty_product: value.monty_product,
                                shipvagoo_prodct: value.shipvagoo_prodct,
                                service: value.service,
                                currency: value.currency,
                                lead_time: value.lead_time,
                                // cost_price_plus_markup: costPrice,
                                // grossProfit: grossProfit,
                                margin: marginPer,
                                cost_price_plus_markup: costPriceWithMarkup,
                                min_ship_standard_list_price: value.min_ship_standard_list_price,
                                ship_standard_list_price: value.ship_standard_list_price,
                                control_condition: value.control_condition,
                                gross_profit: value.margin,  // grossProift is margin
                                cost_price_plus_markup: value.cost_price_plus_markup,
                                shipvagoo_price_group_id: value.shipvagoo_price_group_id,
                            }
                        })
                        newRs[index] = result
                    })
                    console.log(newRs)
                    this.ready = true
                    this.loading2 = false
                    this.pricesList = {...newRs}
                } catch (e) {
                    this.loading2 = false
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getPriceGroupById() {
                console.log(this.$route.params.id, this.$route.params)
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    this.loading2 = true
                    const res = await priceGroupService.getPriceGroupById({
                        price_group_id: this.$route.params.id,
                    })
                    const resp = res.data.pricing
                    let newRs = []
                    Object.keys(resp).map(index => {
                        let country = resp[index]
                        const result = country.map((value, idx) => {
                            let cp = parseInt(value.cost_price)
                            let min_ship_st_price = 0
                            let customer_price = 0
                            let markup = 0
                            let costPrice = cp === 0 || typeof cp === "string" ? 0 : cp + cp * this.defulatPercantage
                            let marginPer =
                                min_ship_st_price === 0 || costPrice === 0 || cost_price === "string"
                                    ? 0
                                    : min_ship_st_price - costPrice
                            // customer_price is List Price excl. VAT
                            // Gross Profit  = List Price excl. VAT - Cost Price
                            let grossProfit = customer_price === 0 ? 0 : customer_price - cp;
                            // costPriceWithMarkup  is Min List Price exc. VAT
                            //  Min List Price exc. VAT  = Cost Price + (Cost Price * Markup%)
                            let costPriceWithMarkup = cp + (cp * markup)

                            return {
                                title: index,
                                id: value.id,
                                ubsend_price_group_id: typeof value.ubsend_price_group_id === 'undefined' ? value.id : value.ubsend_price_group_id,
                                weight_class_from: value.ubsend_price.weight_class_from,
                                weight_class_to: value.ubsend_price.weight_class_to,
                                cost_price: value.ubsend_price.cost_price,
                                markup: value.markup,
                                monty_product: value.ubsend_price.monty_product,
                                shipvagoo_prodct: value.ubsend_price.shipvagoo_prodct,
                                service: value.ubsend_price.service,
                                currency: value.ubsend_price.currency,
                                lead_time: value.ubsend_price.lead_time,
                                // cost_price_plus_markup: costPrice,
                                margin: value.margin,
                                min_ship_standard_list_price: value.min_ship_standard_list_price,
                                ship_standard_list_price: value.ship_standard_list_price,
                                control_condition: value.control_condition,
                                gross_profit: value.margin,  // grossProift is margin
                                cost_price_plus_markup: value.cost_price_plus_markup,
                                shipvagoo_price_group_id: value.shipvagoo_price_group_id,
                            }
                        })
                        newRs[index] = result
                    })
                    this.ready = true
                    this.loading2 = false
                    this.pricesList = {...newRs}

                } catch (e) {
                    this.loading2 = false
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },

            async update(values) {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    if (!this.form.description) {
                        delete this.form.description
                    }
                    if (this.form.pricing.length === 0) {
                        delete this.form.pricing
                    }

                    //this.$forceUpdateform.ubsend_account_id   =this.editData["ubsend_acubsend_account_idcount_id"]
                    //this.form.carrier  = this.updateCarrier
                    const res = await priceGroupService.updatePriceGroup(this.form)
                    this.$notify({
                        title: "Success",
                        message: res.message,
                        type: "success"
                    })
                    await this.$router.push({path: "/price-group/list"})
                } catch (e) {
                    console.log(e)
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async getUbsendList() {
                // display form values on success
                try {
                    // loadingService.startLoading('main-loader:login');
                    const res = await ubsendService.getUbsentList()
                    this.ubsendList = res.data.list
                    if (this.$route.params.id) {
                        const selectedUBsend = this.ubsendList.filter(item =>
                            item.id === this.editData["ubsend_account_id"] ? item.id : null
                        )
                        this.form.ubsend_account_id = selectedUBsend[0].id
                        this.getCarriersList()
                    }
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    if (e.response?.status === 500) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.message,
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
            async onCanel() {
                await this.$router.push({path: "/price-group/list"})
            },
            async addToForm(row) {
                this.form.pricing.push({
                    id: row.id,
                    markup: row.markup,
                    ship_standard_list_price: row.ship_standard_list_price,
                    min_ship_standard_list_price: row.min_ship_standard_list_price,
                })
            }
        },
        created() {
            this.init()
            this.getUbsendList()
            if (this.$route.params.id && this.editData) {
                this.getPriceGroupById()
                console.log(this.editData)
                this.form.price_group_name = this.editData["price_group_name"]
                this.form.type = this.editData["type"]
                this.form.description = this.editData["description"]
                this.isAgreement = this.editData["is_agreement_created"]
            }
        },
        mounted() {
            //ie fix
            if (!window.Number.parseInt) window.Number.parseInt = parseInt
        },
        components: {
            ResizeObserver,
            Form,
            Field,
            ErrorMessage
        }
    })
</script>

<style lang="scss" scoped>
    @import "../../../assets/scss/_variables";


    .el-collapse-bg-gray {
        background: $text-color-primary !important;
        color: #ebeef5;
    }

    .w-100 {
        width: 100%;
    }

    .page-table {
        &.overflow {
            overflow: auto;
        }

        .toolbar-box {
            &.hidden {
                visibility: hidden;
            }
        }

        .table-box {
            overflow: hidden;

            &.hidden {
                visibility: hidden;
            }
        }
    }


    /* For Firefox */

    input[type='number'] {
        -moz-appearance: textfield;
    }


    /* Webkit browsers like Safari and Chrome */

    input[type=number]::-webkit-inner-spin-button,
    input[type=number]::-webkit-outer-spin-button {
        -webkit-appearance: none !important;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        /* display: none; <- Crashes Chrome on hover */
        -webkit-appearance: none !important;
        margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
    }
</style>

<style lang="scss">
    @import "../../../assets/scss/_variables";

    .el-table .cell {
        white-space: nowrap !important;
    }

    .el-collapse-item__header {
        background: $text-color-primary !important;
        color: #ebeef5;
        padding: 3px;
    }

    .page-table {
        padding: 20px;

        .toolbar-box {
            margin-bottom: 10px;
            margin-top: 0;
        }

        .clickable {
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
        }

        .sel-string {
            .sel {
                background: transparentize($text-color-primary, 0.8);
                border-radius: 5px;
                //text-transform: uppercase;
            }
        }
    }

    .bb-bt-br {
        border-bottom: 1px solid #ebeef5;
        border-top: 1px solid #ebeef5;
    }

    .bt-br {
        border-top: 1px solid #ebeef5;
    }

    @media (max-width: 768px) {
        .page-table {
            .toolbar-box {
                display: block;
                overflow: hidden;
                font-size: 80%;
                padding-bottom: 10px;

                & > * {
                    display: inline-block;
                    min-width: 120px;
                    height: 22px;
                    //background: rgba(0, 0, 0, 0.04);
                    margin-bottom: 16px;
                }
            }
        }
    }

    .el-form-item__label {
        font-family: "Nunito Sans";
        font-weight: 400;
        font-size: 14px;
        // line-height: 19.1px;
    }

    li.noclick {
        pointer-events: none;
    }

    .error-boarder {
        border-color: red;
        box-shadow: 0 0 0 1px #d11e1e inset;
    }

    .is-disabled {
        pointer-events: none;
    }

    .input-wrapper-error {
        display: inline-flex;
        flex-grow: 1;
        align-items: center;
        justify-content: center;
        padding: 1px 11px;
        background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
        background-image: none;
        border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
        transition: var(--el-transition-box-shadow);
        box-shadow: 0 0 0 1px #d7195d
    }
</style>
