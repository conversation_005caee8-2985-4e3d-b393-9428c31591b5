<template>
    <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
        <div class="page-header">
            <h1>Price Group</h1>
            <!-- <h4>simple table</h4> -->
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                <el-breadcrumb-item>Price Group</el-breadcrumb-item>
                <el-breadcrumb-item>Price Group List</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="flex m-2">
            <div class="col-4"></div>
            <div class="col-4"></div>
            <div class="col-3"></div>
            <!-- <el-button class="btn-blue-bg" @click="goAdd">Create</el-button> -->
        </div>
        <div class="toolbar-box flex align-center">
            <div class="box grow">
                <!-- <el-input placeholder="Search..." v-model="search" clearable></el-input> -->
            </div>
        </div>

        <resize-observer @notify="handleResize" />

        <div class="table-box card-base card-shadow--medium box grow" id="table-wrapper" v-loading="!ready">
            <el-table
                :data="listInPage"
                style="width: 100%"
                :height="height"
                v-if="ready"
                @selection-change="handleSelectionChange"
            >
                <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
                <el-table-column label="ID" min-width="30" prop="full_name" :fixed="!isMobile">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.$index + 1, search)"></span>
                    </template>
                </el-table-column>
                <el-table-column label="Price Group ID" prop="price_group_name" min-width="140"></el-table-column>
                <el-table-column label="Type" prop="type" min-width="50">
                    <template #default="scope">
                        {{ scope.row.type === 1 ? "Pickup" : "DropOff" }}
                    </template>
                </el-table-column>
                <el-table-column label="UBsend Account" prop="ubsend_account" min-width="120"></el-table-column>
                <el-table-column label="Carrier" prop="carrier" min-width="100"></el-table-column>
                <el-table-column label="Action">
                    <template #default="scope">
                        <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>
                        <el-dropdown trigger="hover" @command="onCommand">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        @click="setPriceGroup(scope.row)"
                                        v-if="scope.row.is_agreement_created === false"
                                        :command="'/price-group/edit/' + scope.row.id"
                                        divided
                                    >
                                     Edit/View
                                    </el-dropdown-item>
                                  <el-dropdown-item
                                      @click="setPriceGroup(scope.row)"
                                      v-else
                                      :command="'/price-group/edit/' + scope.row.id"
                                      divided
                                  >
                                    View
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                ref="pagin"
                v-if="ready"
                :small="pagination.small"
                v-model:current-page="pagination.page"
                :page-sizes="pagination.sizes"
                v-model:page-size="pagination.size"
                :layout="pagination.layout"
                :total="total"
            ></el-pagination>
        </div>
    </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"

import { defineComponent } from "@vue/runtime-core"
import { AxiosError } from "axios"
import {
    priceGroupService,
    loadingService
    // loggingService,
} from "../../../services/_singletons"
import { useMainStore } from "@/stores/main"

export default defineComponent({
    name: "PriceGroupList",
    data() {
        return {
            isMobile: false,
            ready: false,
            width: 0,
            height: "auto",
            loading: false,
            search: "",
            pagination: {
                page: 1,
                size: 20,
                sizes: [10, 15, 20, 30, 50, 100],
                layout: "total, ->, prev, pager, next, jumper, sizes",
                small: false
            },
            list: [],
            editMode: false,
            itemsChecked: [],
            dialogUserVisible: false,
            currentId: 0,
            dayjs,
            checkAll: false,
            checkedOptions: [],
            ckoptions: ["All", "Completed","Pending","Draft","Not Started"],
            isIndeterminate: true
        }
    },
    computed: {
        listFiltered() {
            return this.list.filter(obj => {
                let ctrl = false
                for (let k in obj) {
                    if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
                }
                return ctrl
            })
        },
        listSortered() {
            let prop = this.sortingProp
            let order = this.sortingOrder
            return [].concat(
                this.listFiltered.sort((item1, item2) => {
                    let val1 = ""
                    let val2 = ""

                    val1 = item1[prop]
                    val2 = item2[prop]
                    if (order === "descending") {
                        return val2 < val1 ? -1 : 1
                    }
                    return val1 < val2 ? -1 : 1
                })
            )
        },
        listInPage() {
            let from = (this.currentPage - 1) * this.itemPerPage
            let to = from + this.itemPerPage * 1
            //return this.listSortered.slice(from, to)
            return this.listFiltered.slice(from, to)
        },
        total() {
            return this.listFiltered.length
        },
        currentPage: {
            get() {
                return this.pagination.page
            },
            set(val) {
                this.pagination.page = val
            }
        },
        itemPerPage() {
            return this.pagination.size
        },
        selectedItems() {
            return this.itemsChecked.length || 0
        }
    },
    watch: {
        itemPerPage(val) {
            this.ready = false
            this.currentPage = 1

            setTimeout(() => {
                this.ready = true
            }, 500)
        },
        search(val) {
            this.currentPage = 1
        }
    },
    methods: {
        handleCheckedCitiesChange(value) {
            let checkedCount = value.length
            this.checkAll = checkedCount === this.options.length
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
        },
        goAdd() {
            this.$router.push("/price-group/add")
        },
        setPriceGroup(item) {
            console.log(item)
            useMainStore().setPriceGroup(item)
        },
        async deletePriceGroup(id) {
            try {
                await this.$confirm("Your are sure to delete record. Continue?", "Warning", {
                    confirmButtonText: "OK",
                    cancelButtonText: "Cancel",
                    type: "warning",
                    center: true
                })
                    .then(() => {
                        const res = priceGroupService.deletePriceGroup({ price_group_id: id })
                        this.$notify({
                            title: "Success",
                            message: res.message,
                            type: "success"
                        })
                        this.list = this.list.filter(el => el.id !== id)
                    })
                    .catch(() => {})
            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: "Fail to Delete",
                        position: "top-right"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: "Error getting Ubsend List",
                    position: "top-right"
                })
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        },
        onCommandLang(lang) {
            if (lang.charAt(0) === "/") this.onCommand(lang)
            else this.lang = lang
        },
        onCommand(path) {
            if (path !== "skip") this.$router.push(path)
        },
        calcDims() {
            const tableWrapper = document.getElementById("table-wrapper")
            if (tableWrapper) this.width = tableWrapper.clientWidth

            if (!this.isMobile && tableWrapper) {
                this.height = tableWrapper.clientHeight - 44
            }

            if (this.width < 480) {
                this.pagination.small = true
                this.pagination.layout = "prev, pager, next"
            } else if (this.width >= 480 && this.width < 700) {
                this.pagination.small = false
                this.pagination.layout = "prev, pager, next, ->, sizes"
            } else {
                this.pagination.small = false
                this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
            }

            this.ready = true
        },
        handleResize: _.throttle(function (e) {
            this.ready = false
            this.width = 0
            setTimeout(this.calcDims, 1000)
        }, 500),
        handleSelectionChange(val) {
            this.itemsChecked = val
        },
        init() {
            if (window.innerWidth <= 768) this.isMobile = true
        },
        async getPriceGroupList() {
            // display form values on success
            try {
                const res = await priceGroupService.getPriceGroupList()
                this.list = res.data.pricing
            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-right"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0],
                    position: "top-right"
                })
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        }
    },
    filters: {
        selected: function (value, sel) {
            if (!value) return ""
            if (!sel) return value

            value = value.toString()
            sel = sel.toString()

            const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
            if (startIndex !== -1) {
                const endLength = sel.length
                const matchingString = value.substr(startIndex, endLength)
                return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
            }
            //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
            return value
        }
    },
    created() {
        this.init()
        this.getPriceGroupList()
    },
    mounted() {
        //ie fix
        if (!window.Number.parseInt) window.Number.parseInt = parseInt

        this.calcDims()
    },
    components: { ResizeObserver }
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.page-table {
    &.overflow {
        overflow: auto;
    }

    .toolbar-box {
        &.hidden {
            visibility: hidden;
        }
    }

    .table-box {
        overflow: hidden;

        &.hidden {
            visibility: hidden;
        }
    }
}
</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
    padding: 20px;

    .toolbar-box {
        margin-bottom: 10px;
        margin-top: 0;
    }

    .clickable {
        cursor: pointer;
        text-decoration: underline;
        font-weight: bold;
    }

    .sel-string {
        .sel {
            background: transparentize($text-color-primary, 0.8);
            border-radius: 5px;
            //text-transform: uppercase;
        }
    }
}

@media (max-width: 768px) {
    .page-table {
        .toolbar-box {
            display: block;
            overflow: hidden;
            font-size: 80%;
            padding-bottom: 10px;

            & > * {
                display: inline-block;
                min-width: 120px;
                height: 22px;
                //background: rgba(0, 0, 0, 0.04);
                margin-bottom: 16px;
            }
        }
    }
}
.el-pagination {
    .el-pagination__rightwrapper {
        display: flex;
    }
}
</style>
