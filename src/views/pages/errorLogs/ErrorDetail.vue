<template>
  <div class="scrollable only-y">
    <div>
      <h1>Error Logs</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
        <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
        <el-breadcrumb-item>Error Logs</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div>
      <div class="mt-15 error-details-table">
        <div class="error-details-heading label-heading">
          Error Details
        </div>
        <div class="flex error-detail-bottom-border">
          <div class="col-6 p-15">
            <div class="flex">
              <div class="col-4 label-heading">Date:</div>
              <div class="col-8 label-value">{{ dayjs(error.created_at).format('DD-MM-YYYY HH:mm:ss') }}</div>
            </div>
          </div>
          <div class="col-6 p-15 error-detail-left-border">
            <div class="flex">
              <div class="col-4 label-heading">Transaction ID:</div>
              <div class="col-8 label-value">{{ error.transaction_code ?? '-' }}</div>
            </div>
          </div>
        </div>
        <div class="flex error-detail-bottom-border">
          <div class="col-6 p-15">
            <div class="flex">
              <div class="col-4 label-heading">Customer ID:</div>
              <div class="col-8 label-value">{{ error.company_code }}</div>
            </div>
          </div>
          <div class="col-6 p-15 error-detail-left-border">
            <div class="flex">
              <div class="col-4 label-heading">Shipment ID:</div>
              <div class="col-8 label-value">{{ error.shipment_code ?? '-' }}</div>
            </div>
          </div>
        </div>
        <div class="flex error-detail-bottom-border">
          <div class="col-6 p-15">
            <div class="flex">
              <div class="col-4 label-heading">Customer Name:</div>
              <div class="col-8 label-value">{{ error.company?.company_name }}</div>
            </div>
          </div>
          <div class="col-6 p-15 error-detail-left-border">
            <div class="flex">
              <div class="col-4 label-heading">Payment ID:</div>
              <div class="col-8 label-value">{{ error.payment_id ?? '-' }}</div>
            </div>
          </div>
        </div>
        <div class="flex error-detail-bottom-border">
          <div class="col-6 p-15">
            <div class="flex">
              <div class="col-4 label-heading">Error From:</div>
              <div class="col-8 label-value">{{ error.platform }}</div>
            </div>
          </div>
          <div class="col-6 p-15 error-detail-left-border">
            <div class="flex">
              <div class="col-4 label-heading">Invoice ID:</div>
              <div class="col-8 label-value">{{ error.sales_invoice_code ?? '-' }}</div>
            </div>
          </div>
        </div>
        <div class="flex error-detail-bottom-border">
          <div class="col-6 p-15 ">
            <div class="flex">
              <!--              <div class="col-4 label-heading">Exception (Error):</div>
                            <div class="col-8 label-value">{{ error.platform }} Error</div>-->
            </div>
          </div>
          <div class="col-6 p-15 error-detail-left-border">
            <div class="flex">
              <div class="col-4 label-heading">Credit Note ID:</div>
              <div class="col-8 label-value">{{ error.sales_invoice_code ?? '-' }}</div>
            </div>
          </div>
        </div>
        <div class="flex error-detail-bottom-border p-15">
          <div class="col-2 label-heading">
            Description:
          </div>
          <div class="col-10 " style="margin-left: -3px;">
            <div class="flex justify-space-between">
              <div class="label-value mr-5">
                {{ error.description }}
              </div>
              <div>
                <img class="cursor-pointer"
                     src="@/assets/images/copy.svg"
                     alt="copy" @click="onClickCopy('description')"/>
              </div>
            </div>
          </div>
        </div>
        <div class="flex p-15 error-detail-bottom-border">
          <div class="col-2 label-heading">
            Request:
          </div>
          <div class="col-10 " style="margin-left: -3px;">
            <div class="flex justify-space-between">
              <div class="label-value mr-5">
                {{ error.request_params }}
              </div>
              <div v-if="error.request_params">
                <img class="cursor-pointer"
                     src="@/assets/images/copy.svg"
                     alt="copy" @click="onClickCopy('request_params')"/>
              </div>
            </div>
          </div>
        </div>

        <div class="flex p-15">
          <div class="col-2 label-heading">
            Exception (Error):
          </div>
          <div class="col-10 " style="margin-left: -3px;">
            <div class="flex justify-space-between">
              <div class="label-value mr-5">
                {{ error.exception }}
              </div>
              <div v-if="error.exception">
                <img class="cursor-pointer"
                     src="@/assets/images/copy.svg"
                     alt="copy" @click="onClickCopy('exception')"/>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <el-button class="mt-15" type="primary" @click="$router.back()">Back</el-button>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: "ErrorDetails",
  data() {
    return {
      error: {},
      dayjs,
    }
  },
  methods: {
    onClickCopy(type) {
      let message = null;
      if (type == 'exception') {
        navigator.clipboard.writeText(JSON.stringify(this.error.exception));
        message = 'Exception copied on clipboard'
      } else if (type == 'description') {
        navigator.clipboard.writeText(this.error.description);
        message = 'Description copied on clipboard'
      } else if (type == 'request_params') {
        navigator.clipboard.writeText(JSON.stringify(this.error.request_params));
        message = 'Request params copied on clipboard'
      }
      this.$notify({
        title: "Success",
        message: message,
        type: "success"
      })
    }
  },
  mounted() {
    this.error = JSON.parse(localStorage.getItem('errorLog',))
  }
}
</script>

<style scoped>
.error-details-table {
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 5px;
}

.error-details-heading {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  padding: 15px;
  color: #212121;
  background-color: rgba(245, 245, 245, 1);
}

.label-heading {
  color: rgba(74, 89, 106, 1);
  font-weight: bold;
}

.label-value {
  color: rgba(74, 89, 106, 1);
  font-weight: 300;
}

.error-detail-left-border {
  border-left: 1px solid rgba(221, 221, 221, 1);
}

.error-detail-bottom-border {
  border-bottom: 1px solid rgba(221, 221, 221, 1);
}
</style>