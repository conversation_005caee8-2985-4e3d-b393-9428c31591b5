<template>
  <div class="page-table column scrollable only-y" :class="{ flex: !isMobile, overflow: isMobile }">
    <div class="page-header">
      <h1>System User</h1>
      <!-- <h4>simple table</h4> -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
        <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
        <el-breadcrumb-item>System User</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="flex m-2">
      <div class="col-10"></div>
      <div class="col-2 flex justify-flex-end">
        <el-button class="btn-blue-bg" @click="openModal">Add</el-button>
      </div>

    </div>
    <div class="toolbar-box flex align-center">
      <div class="box grow">
        <!-- <el-input placeholder="Search..." v-model="search" clearable></el-input> -->
      </div>
    </div>

    <resize-observer @notify="handleResize"/>

    <div class="table-box card-base card-shadow--medium box grow" id="table-wrapper" v-loading="!ready">
      <el-table
          :data="listInPage"
          style="width: 100%"
          :height="height"
          v-if="ready"
          @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="34" fixed></el-table-column> -->
        <el-table-column label="ID" min-width="90" prop="id" :fixed="!isMobile">
          <template #default="scope">
            <span class="sel-string" v-html="$options.filters.selected(scope.$index + 1, search)"></span>
          </template>
        </el-table-column>

        <el-table-column label="Name" prop="name" min-width="100"></el-table-column>
        <el-table-column label="Email" prop="email" min-width="100"></el-table-column>
        <el-table-column label="Role" prop="role_name" min-width="100">
          <template #default="scope">
            <span class="sel-string">{{ scope.row.role_name !== null ? scope.row.role_name : '' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="Action">
          <template #default="scope">
            <span class="sel-string" v-html="$options.filters.selected(scope.row.full_name, search)"></span>
            <el-dropdown trigger="hover" @command="onCommand">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                      @click="setSystemUser(scope.row)"
                      :command="'skip'"
                      divided
                  >
                    Edit
                  </el-dropdown-item>
                  <el-dropdown-item @click="deleteSystemUser(scope.row.id)" :command="'skip'" divided>
                    Delete
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
          ref="pagin"
          v-if="ready"
          :small="pagination.small"
          v-model:current-page="pagination.page"
          :page-sizes="pagination.sizes"
          v-model:page-size="pagination.size"
          :layout="pagination.layout"
          :total="total"
      ></el-pagination>
    </div>

    <el-dialog title="" v-model="dialogFormVisible" class="p-0">

      <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">
        <el-form ref="form" :model="form" class="mb-50 bb-br-gray" label-width="120px">
          <!-- <Form class="form-box" @submit="onSubmit" :validation-schema="schema"> -->
          <h4 class="ml-12">System User</h4>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Name">
                <el-input v-model="form.name" placeholder=""></el-input>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Email">
                <el-input type="email" v-model="form.email" placeholder=""></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Role">
                <el-select
                    v-model="form.role_id"
                    filterable
                    placeholder=""
                    :loading="loading"
                >
                  <el-option
                      v-for="item in roleList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="11" v-if="!editMode">
              <el-form-item label="Password">
                <el-input type="password" v-model="form.password" placeholder=""></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col class="demo-form-inline" :span="24">
            <el-form-item label="User Status">
              <el-switch v-model="form.is_active"></el-switch>
            </el-form-item>
          </el-col>

          <div class="bt-br"></div>
          <el-form-item v-if="ready" class="mt-30 dialog-footer">
            <el-button class="btn-blue-bg" @click="onSubmit" v-loading.fullscreen.lock="fullscreenLoading">
              {{ selectedRow === true ? 'Update' : 'Save' }}
            </el-button>
            <el-button class="btn" @click="closeModal()">Cancel</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"

import {defineComponent} from "@vue/runtime-core"
import {loadingService, roleService, systemUserService} from "../../../services/_singletons"

export default defineComponent({
  name: "SystemUserList",
  data() {
    return {
      isMobile: false,
      dialogFormVisible: false,
      selectedRow: false,
      ready: false,
      width: 0,
      height: "auto",
      loading: false,
      search: "",
      pagination: {
        page: 1,
        size: 20,
        sizes: [10, 15, 20, 30, 50, 100],
        layout: "total, ->, prev, pager, next, jumper, sizes",
        small: false
      },
      list: [],
      editMode: false,
      itemsChecked: [],
      dialogUserVisible: false,
      currentId: 0,
      dayjs,
      checkAll: false,
      checkedOptions: [],
      ckoptions: ["all", "Completed", "Pending"],
      isIndeterminate: true,
      fullscreenLoading: false,
      form: {
        id: "",
        name: "",
        email: "",
        password: "",
        is_active: false,
        role_id: "",
      },
      fileList: [],
      roleList: [],
      file: null,
      content: [],
      parsed: false,
      base_url: null,
      showDropzone: true
    }
  },
  computed: {
    listFiltered() {
      return this.list.filter(obj => {
        let ctrl = false
        for (let k in obj) {
          if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
        }
        return ctrl
      })
    },
    listSortered() {
      let prop = this.sortingProp
      let order = this.sortingOrder
      return [].concat(
          this.listFiltered.sort((item1, item2) => {
            let val1 = ""
            let val2 = ""

            val1 = item1[prop]
            val2 = item2[prop]
            if (order === "descending") {
              return val2 < val1 ? -1 : 1
            }
            return val1 < val2 ? -1 : 1
          })
      )
    },
    listInPage() {
      let from = (this.currentPage - 1) * this.itemPerPage
      let to = from + this.itemPerPage * 1
      //return this.listSortered.slice(from, to)
      return this.listFiltered.slice(from, to)
    },
    total() {
      return this.listFiltered.length
    },
    currentPage: {
      get() {
        return this.pagination.page
      },
      set(val) {
        this.pagination.page = val
      }
    },
    itemPerPage() {
      return this.pagination.size
    },
    selectedItems() {
      return this.itemsChecked.length || 0
    }
  },
  watch: {
    itemPerPage(val) {
      this.ready = false
      this.currentPage = 1

      setTimeout(() => {
        this.ready = true
      }, 500)
    },
    search(val) {
      this.currentPage = 1
    }
  },
  methods: {
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.options.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
    },
    setSystemUser(item) {
      this.editMode = true;
      this.dialogFormVisible = true
      this.selectedRow = true
      this.form.id = item.id
      this.form.name = item.name
      this.form.email = item.email
      this.form.password = item.password
      //  this.form.role = this.roleList.find((el) => el.name === item.role_name)
      this.form.role_id = this.roleList.find((el) => el.name === item.role_name)?.id
      this.form.is_active = item.is_active
      console.log(JSON.stringify(this.form))
    },
    updateSelectedRow(item) {
      console.log(item)
      this.selectedRow = true
      this.form.id = item.id
      this.form.name = item.name
      this.form.email = item.email
      this.form.password = item.password
      //  this.form.role = item.role_id
      this.form.role_id = item.role_id
      this.form.is_active = item.is_active
      this.onSubmit()
    },
    clearForm() {
      this.form.id = ''
      this.form.name = ''
      this.form.email = ''
      this.form.password = ''
      // this.form.role = ''
      this.form.is_active = false
    },
    closeModal() {
      this.dialogFormVisible = false
      this.clearForm()
    },
    openModal() {
      this.editMode = false;
      this.dialogFormVisible = true
      this.clearForm()
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handleStart(file) {
      this.file = file.raw
    },
    handlePreview(file) {
      this.file = file
    },
    handleExceed(files, fileList) {
      this.$message.warning(
          "The limit is 3, you selected" +
          files.length +
          " files this time, add up to " +
          files.length +
          fileList.length +
          "totally"
      )
    },
    beforeRemove(file, fileList) {
      return this.$confirm(file.name + "?")
    },
    handleUpload(file, fileList) {
      this.file = file.target.files[0]
    },
    async deleteSystemUser(id) {
      try {
        await this.$confirm("Your are sure to delete Carrier. Continue?", "Warning", {
          confirmButtonText: "OK",
          cancelButtonText: "Cancel",
          type: "warning",
          center: true
        })
            .then(async () => {
              const res = await systemUserService.deleteSystemUser({id: id})
              this.$notify({
                title: "Success",
                message: res.message,
                type: "success"
              })
              this.list = this.list.filter(el => el.id !== id)
            })
            .catch(() => {
            })
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: "Fail to Delete",
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: "Error getting Ubsend List",
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async onSubmit(values) {
      // display form values on success
      const is_active = this.form.is_active ? 1 : 0;
      try {
        this.fullscreenLoading = true;
        const formData = new FormData();
        Object.entries(this.form).forEach(([key, value]) => {
          formData.append(key, value);
        });
        formData.append('is_active', is_active);
        if (this.selectedRow === true) {
          await this.update()
          return
        }
        const res = await systemUserService.addSystemUser(formData)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        this.dialogFormVisible = false
        this.clearForm()
        await this.getSystemUserList()
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.message,
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async update() {
      const is_active = this.form.is_active ? 1 : 0;
      try {
        this.fullscreenLoading = true;
        const formData = new FormData();
        Object.entries(this.form).forEach(([key, value]) => {
          formData.append(key, value);
        });
        formData.append('is_active', is_active)
        const res = await systemUserService.updateSystemUser(formData)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        this.selectedRow = false
        await this.getSystemUserList()
        this.dialogFormVisible = false
        this.clearForm()
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 500);
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    onCommandLang(lang) {
      if (lang.charAt(0) === "/") this.onCommand(lang)
      else this.lang = lang
    },
    onCommand(path) {
      if (path !== "skip") this.$router.push(path)
    },
    calcDims() {
      const tableWrapper = document.getElementById("table-wrapper")
      if (tableWrapper) this.width = tableWrapper.clientWidth

      if (!this.isMobile && tableWrapper) {
        this.height = tableWrapper.clientHeight - 44
      }

      if (this.width < 480) {
        this.pagination.small = true
        this.pagination.layout = "prev, pager, next"
      } else if (this.width >= 480 && this.width < 700) {
        this.pagination.small = false
        this.pagination.layout = "prev, pager, next, ->, sizes"
      } else {
        this.pagination.small = false
        this.pagination.layout = "total, ->, prev, pager, next, jumper, sizes"
      }

      this.ready = true
    },
    handleResize: _.throttle(function (e) {
      this.ready = false
      this.width = 0
      setTimeout(this.calcDims, 1000)
    }, 500),
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      if (window.innerWidth <= 768) this.isMobile = true
    },
    async getSystemUserList() {
      // display form values on success
      try {
        const res = await systemUserService.getSystemUserList()
        this.base_url = res.data.base_url
        this.list = res.data.Users.map((item) => {
          return {
            id: item.id,
            name: item.name,
            email: item.email,
            role_name: item.role_name,
            role: item.role_id,
            is_active: item.status == 1 ? true : false
          }
        });

      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },

    async getRoleList() {
      // display form values on success
      try {
        const res = await roleService.getRoleList()
        this.base_url = res.data.base_url
        this.roleList = res.data.Roles

      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
  },
  filters: {
    selected: function (value, sel) {
      if (!value) return ""
      if (!sel) return value

      value = value.toString()
      sel = sel.toString()

      const startIndex = value.toLowerCase().indexOf(sel.toLowerCase())
      if (startIndex !== -1) {
        const endLength = sel.length
        const matchingString = value.substr(startIndex, endLength)
        return value.replace(matchingString, `<span class="sel">${matchingString}</span>`)
      }
      //return value.toString().replace(new RegExp(sel,"gim"), `<span class="sel">${sel}</span>`)
      return value
    }
  },
  created() {
    this.init()
    this.getRoleList()
    this.getSystemUserList()
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt

    this.calcDims()
  },
  components: {ResizeObserver}
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.el-dialog__body {
  padding: 0px;
}

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}
</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.el-pagination {
  .el-pagination__rightwrapper {
    display: flex;
  }
}

.bt-br-gray {
  border-top: 1px solid #ebeef5;
}

</style>
