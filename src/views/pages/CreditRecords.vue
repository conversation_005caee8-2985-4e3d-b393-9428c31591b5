<template>
    <div>
        <div class="page-header">
            <h1>Credit Records</h1>
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">
                    <i class="mdi mdi-home-outline"></i>
                </el-breadcrumb-item>
                <el-breadcrumb-item>Credit Records</el-breadcrumb-item>
            </el-breadcrumb>
        </div>

        <div class="flex justify-flex-end" style="gap: 20px">
            <div>
                <el-select
                    v-model="filters.customerId"
                    style="width: 250px"
                    class="management-selection"
                    placeholder="Select Customer"
                    clearable
                    @change="onFilterChange"
                >
                    <el-option
                        v-for="customer in customers"
                        :key="customer.value"
                        :label="customer.label"
                        :value="customer.value"
                    />
                </el-select>
            </div>

            <div>
                <el-select
                    v-model="filters.paymentStatus"
                    style="width: 200px"
                    class="management-selection"
                    placeholder="Select Payment Status"
                    clearable
                    @change="onFilterChange"
                >
                    <el-option
                        v-for="status in paymentStatuses"
                        :key="status.value"
                        :label="status.label"
                        :value="status.value"
                    />
                </el-select>
            </div>
        </div>

        <div class="page-table scrollable only-y">
            <div class="table-box card-base card-shadow--medium scrollable only-x" v-loading="loading">
                <table class="css-serial styled striped">
                    <thead>
                        <tr class="Font-Size1">
                            <th>Created At</th>
                            <th>Customer Name</th>
                            <th>Invoice Number</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Due Date</th>
                            <th>Month</th>
                            <th>Year</th>
                            <th>Payment Status</th>
                            <th class="text-right">Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(invoice, index) in creditRecords" :key="index">
                            <td>
                                {{ dayjs(String(invoice.created_at)).format("DD-MM-YYYY") }}<br />
                                {{ dayjs(String(invoice.created_at)).format("HH:mm:ss") }}
                            </td>
                            <td>{{ invoice.company?.company_name || "N/A" }}</td>

                            <td>
                                {{ HelperService.formatId(invoice.id) }}
                            </td>
                            <td>{{ dayjs(String(invoice.start_date)).format("DD-MM-YYYY ") }}</td>
                            <td>
                                {{ dayjs(String(invoice.end_date)).format("DD-MM-YYYY ") }}
                            </td>
                            <td>
                                {{ dayjs(String(invoice.due_date)).format("DD-MM-YYYY ") }}
                            </td>
                            <td>
                                {{ invoice.month }}
                            </td>
                            <td>{{ invoice.year }}</td>
                            <td>
                                {{ invoice.payment_status }}
                            </td>
                            <td class="text-right">DKK {{ danishNumberFormat(invoice.total_amount ?? 0) }}</td>
                            <td>
                                <el-dropdown trigger="hover" @command="onCommand">
                                    <span class="el-dropdown-link">
                                        <i class="mdi mdi-dots-vertical"></i>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item @click="onClickCreateSettlement(invoice)">
                                                Create settlement
                                            </el-dropdown-item>
                                            <el-dropdown-item>View invoice</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <el-pagination
                    class="flex"
                    ref="pgi"
                    v-if="!loading"
                    small="false"
                    @current-change="changeCurrentPage"
                    @size-change="handleSizeChange"
                    :current-page="currentPage"
                    v-model:page-size="perPage"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, ->,sizes, prev, pager, next, jumper"
                    :total="total"
                ></el-pagination>
            </div>
        </div>

        <!-- Settlement Dialog -->
        <SettlementDialog
            v-model:visible="settlementDialogVisible"
            :invoice="selectedRecord"
            @save="onSaveSettlement"
            @close="onCloseSettlement"
        />
    </div>
</template>

<script>
import { getDateFormat } from "../../helpers"
import { loadingService, merchantService } from "@/services/_singletons"
import { danishNumberFormat } from "@/helpers"
import dayjs from "dayjs"
import HelperService from "../../services/helper.service"
import { useHelper } from "../../composeable/Helper"
import SettlementDialog from "@/components/dialogs/CreateSettlementDialog.vue" // Adjust path as needed

const { downloadTransactionPdf } = useHelper()

export default {
    name: "CreditRecords",
    components: {
        SettlementDialog
    },
    methods: {
        danishNumberFormat,
        onClickCreateSettlement(invoice) {
            this.selectedRecord = invoice
            this.settlementDialogVisible = true
        },
        async onSaveSettlement(settlementData) {
            try {
                console.log("Settlement data:", settlementData)

                await merchantService.createSettlement(settlementData)

                this.$notify.success({
                    title: "Success",
                    message: "Settlement created successfully",
                    position: "top-right"
                })

                this.getCreditRecords(this.currentPage, this.perPage)

                this.onCloseSettlement()
            } catch (error) {
                console.error("Error creating settlement:", error)
                this.$notify.error({
                    title: "Error",
                    message: error.message || "Failed to create settlement",
                    position: "top-right"
                })
            }
        },
        onCloseSettlement() {
            this.settlementDialogVisible = false
            this.selectedRecord = null
        },
        onFilterChange() {
            this.getCreditRecords(1, this.perPage, this.searchTerm)
        },
        search(value) {
            this.searchTerm = value
            this.getCreditRecords(1, this.perPage, value)
        },
        handleSizeChange(size) {
            this.getCreditRecords(this.currentPage, size, this.searchTerm)
        },
        changeCurrentPage(page) {
            this.getCreditRecords(page, this.perPage, this.searchTerm)
        },
        getDateFormat,
        async getCustomers() {
            try {
                const res = await merchantService.getEconomicMerchants()
                console.log("RES: ", res)
                this.customers = res.data.customers.map(customer => ({
                    value: customer.company_code,
                    label: customer.company_name
                }))
            } catch (error) {
                console.error("Error fetching customers:", error)
                this.$notify.error({
                    title: "Error",
                    message: "Failed to load customers",
                    position: "top-right"
                })
            }
        },
        async getCreditRecords(page = 1, perPage = 10, filter = "") {
            this.loading = true
            try {
                const params = [
                    { key: "page", value: page },
                    { key: "perpage", value: perPage }
                ]
                if (this.filters.customerId) {
                    params.push({ key: "company_code", value: this.filters.customerId })
                }
                if (this.filters.paymentStatus) {
                    params.push({ key: "payment_status", value: this.filters.paymentStatus })
                }

                const res = await merchantService.getMerchantBillingInvoices(params)
                console.log("Res: ", res)

                if (res.entities) {
                    this.currentPage = res.currentPage
                    this.lastPage = res.lastPage
                    this.perPage = res.perPage
                    this.total = res.totalRecords
                    this.creditRecords = res.entities
                    this.loading = false
                } else {
                    this.currentPage = res.data.currentPage
                    this.lastPage = res.data.lastPage
                    this.perPage = res.data.perPage
                    this.total = res.data.totalRecords
                    this.creditRecords = res.data.invoice || res.data.creditRecords
                    this.loading = false
                }
            } catch (e) {
                console.log("ERROR ", e)
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0] ?? e.response.data.error,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                } else if (e.response.status === 500) {
                    this.$notify.error({
                        title: "Error",
                        message: e.message ?? e.message,
                        position: "top-right"
                    })
                    this.loading = false
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0] ?? e.response.data.error,
                    position: "top-right"
                })
                this.loading = false
            } finally {
                loadingService.stopLoading("main-loader:credit-records")
                this.loading = false
            }
        }
    },
    data() {
        return {
            creditRecords: [],
            loading: false,
            currentPage: 1,
            lastPage: 0,
            perPage: 10,
            total: 0,
            searchTerm: "",
            settlementDialogVisible: false,
            selectedRecord: null,
            filters: {
                customerId: null,
                paymentStatus: null
            },
            customers: [],
            paymentStatuses: [
                { value: "PAID", label: "Paid" },
                { value: "PENDING", label: "Pending" },
                { value: "DUE", label: "Due" },
                { value: "CANCELLED", label: "Cancelled" }
            ],
            dayjs,
            HelperService
        }
    },
    async created() {
        await this.getCustomers()
        this.getCreditRecords(this.currentPage, this.perPage)
    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables";

.page-table {
    padding: 0% !important;
    margin-top: 2% !important;
    padding-right: 15px;
    padding-bottom: 20px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
}

.table-box {
    overflow: auto;
    margin-bottom: 11px;
}

.Font-Size1 {
    font-size: 14px !important;
}

.page-header {
    margin-bottom: 15px;
}

.toolbar-box {
    margin-bottom: 15px;
}

.management-selection {
    .el-input__inner {
        border-radius: 4px;
    }
}

.el-dropdown-link {
    cursor: pointer;
    color: #409eff;
}

.mdi {
    padding-top: 2px;
    padding-left: 4px;
    color: #bbbbbb !important;
}

.pdf-assets {
    height: 0;
    overflow: hidden;
}

@media (max-width: 768px) {
    .toolbar-box {
        display: block !important;

        & > div:first-child {
            margin-bottom: 15px;
            display: block;

            & > div {
                margin-bottom: 10px;
            }
        }
    }
}
</style>
