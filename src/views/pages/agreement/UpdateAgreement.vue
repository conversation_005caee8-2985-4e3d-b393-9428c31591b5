<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Customer Agreement</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Customer Agreement</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <div class="card-base card-shadow--medium search-card scrollable only-y mb-5 p-10">
      <el-form ref="form" class="mb-50 bb-br-gray" :model="form" label-width="120px">
        <h4 class="ml-12">New Agreement</h4>

        <el-col class="demo-form-inline flex" :span="24">
          <el-col class="demo-form-inline" :span="12">
            <el-form-item label="Customer">
              <el-select v-model="form.merchant_id" disabled filterable placeholder="" :loading="loading">
                <el-option
                    v-for="item in customerList"
                    :key="item.user_code"
                    :label="item.company_name"
                    :value="item.user_code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col class="demo-form-inline" :span="12">
            <el-form-item label="Date">
              <el-date-picker style="width: 100%" v-model="form.date" type="date" placeholder="">
              </el-date-picker>
              <!-- <el-input v-model="form.date" type="date" placeholder=""></el-input> -->
            </el-form-item>
          </el-col>
        </el-col>

        <el-form-item label="Address">
          <el-input disabled v-model="form.address"></el-input>
        </el-form-item>

        <el-form-item label="Summary">
          <el-input type="textarea" rows="5" v-model="form.summary"></el-input>
        </el-form-item>

        <el-form-item label="Footer" class="d-none" hidden>
          <el-input disabled type="textarea" rows="5" v-model="form.footer"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="card-base card-shadow--medium search-card scrollable only-y mt-15 p-10">
      <el-form ref="agreementForm" class="mb-50 bb-br-gray" :model="form" label-width="120px">
        <h4 class="ml-12">Add Price Group</h4>
        <el-form-item label="Carrier">
          <el-col class="demo-form-inline" :span="12">
            <el-col class="demo-form-inline">
              <el-select
                  class="full_width"
                  @change="getPriceGroups()"
                  v-model="form.carrier"
                  placeholder="Carrier"
              >
                <template v-for="data in carrierList" :key="data">
                  <el-option :label="data" :value="data"></el-option>
                </template>
              </el-select>
            </el-col>
          </el-col>
          <el-col class="demo-form-inline" :span="12">
            <el-form-item label="Price Group">
              <el-select
                  class="full_width"
                  @change="getPriceList()"
                  v-model="form.price_group_id"
                  placeholder="Price Group"
              >
                <template v-for="data in priceGrouptList" :key="data">
                  <el-option :label="data.price_group_name" :value="data.id"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-button
              v-if="form.price_group_id !== '' && form.carrier !== '' && allowPriceGroupbtn === true"
              class="btn-blue-bg"
              @click="addGroup()"
          >Add Group
          </el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div v-if="loading2 === false" class="mt-15 mb-15">
      <div
          v-for="(parentList, idx) in pricesListing"
          :key="idx"
          class="card-base card-shadow--medium demo-box bg-white mb-5"
          style="background: #151526 !important"
      >
        <el-collapse value="index" class="collapse-parent">
          <el-collapse-item :title="parentList.title" name="index" class="collapse-parent-item">
            <el-col class="demo-form-inline flex m-10" :span="24">
              <el-col class="demo-form-inline ml-10 mr-20" :span="11">
                <el-form-item label="Country From">
                  <el-select
                      filterable
                      v-model="parentList.country_from"
                      placeholder=""
                      :loading="loading"
                      disabled
                  >
                    <el-option
                        v-for="item in countryList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col class="demo-form-inline mr-5" :span="12">
                <el-form-item label="Country To">
                  <el-select
                      filterable
                      multiple
                      placeholder=" "
                      v-model="parentList.country_to"
                      :value-key="parentList.country_to"
                      :loading="loading"
                      @change="setPricingCollection(parentList, idx)"
                  >
                    <el-option
                        v-for="item in countryListNew"
                        :key="item"
                        :label="item"
                        :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <!--  -->
            <div v-if="parentList.showList === true" class="m-10">
              <div
                  v-for="(record, index) in parentList.result"
                  :key="index"
                  class="card-base card-shadow--medium demo-box bg-white mb-5"
                  style="background: #151526 !important"
              >
                <el-collapse value="index" class="collapse-child">
                  <el-collapse-item
                      :title="'Denmark - ' + index"
                      name="index"
                      class="collapse-child-item"
                  >
                    <div
                        class="table-box card-base card-shadow--medium box grow"
                        id="table-wrapper"
                        v-loading="!ready"
                    >
                      <el-table
                          :data="record"
                          style="width: 100%"
                          :height="height"
                          v-if="ready"
                          @selection-change="handleSelectionChange"
                      >
                        <el-table-column type="selection" width="34" fixed></el-table-column>
                        <el-table-column
                            label="Id"
                            min-width="45"
                            prop="s_no"
                            :fixed="!isMobile"
                        >
                          <template #default="scope">
                            <span class="sel-string" v-html="scope.$index + 1"></span>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="Shipvagoo Product"
                            prop="shipvagoo_prodct"
                            min-width="150"
                            :fixed="!isMobile"
                        ></el-table-column>
                        <el-table-column
                            label="Weight To"
                            prop="weight_class_to"
                            min-width="80"
                            :fixed="!isMobile"
                        >
                          <template #default="scope">
                                                        <span
                                                            class="sel-string"
                                                            v-html="scope.row.weight_class_to"
                                                        ></span>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="Weight From"
                            prop="weight_class_from"
                            :fixed="!isMobile"
                            min-width="95"
                        >
                          <template #default="scope">
                                                        <span
                                                            class="sel-string"
                                                            v-html="scope.row.weight_class_from"
                                                        ></span>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="Min.Sv List Price excl. VAT"
                            prop="cost_price_plus_markup"
                            min-width="100"
                        >
                          <template #default="scope">
                                                        <span
                                                            class="sel-string"
                                                            v-html="
                                                                (scope.row.cost_price_plus_markup = (
                                                                    parseFloat(scope.row.cost_price) +
                                                                    parseFloat(
                                                                        (scope.row.cost_price * scope.row.markup) / 100
                                                                    )
                                                                ).toFixed(2))
                                                            "
                                                        ></span>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="SV List Price excl. VAT"
                            prop="ship_standard_list_price"
                            min-width="110"
                        >
                          <template #default="scope">
                            {{ scope.row.ship_standard_list_price }}
                          </template>
                        </el-table-column>
                        <el-table-column label="Discount (DKK)" prop="discount" min-width="110">
                          <template #default="scope">
                            <div class="el-input" data-v-3dd33bdf="">
                              <div
                                  :class="
                                                                    parseFloat(scope.row.gross_profit) >=
                                                                        parseFloat(scope.row.cost_price_plus_markup) ||
                                                                    scope.row.discount === 0
                                                                        ? 'el-input__wrapper'
                                                                        : 'el-input__wrapper-focus-danger'
                                                                "
                              >
                                <input
                                    v-on:blur="setDiscount(scope.row)"
                                    type="number"
                                    class="el-input__inner"
                                    step="any"
                                    v-model="scope.row.discount"
                                    min="0"
                                />
                              </div>
                            </div>

                            <!-- ></el-input> -->
                          </template>
                        </el-table-column>

                        <el-table-column
                            label="Customer Price excl. VAT"
                            prop="gross_profit"
                            min-width="120"
                        >
                          <template #default="scope">
                                                        <span
                                                            class="sel-string"
                                                            v-html="
                                                                (scope.row.gross_profit =
                                                                    (
                                                                        scope.row.ship_standard_list_price -
                                                                        scope.row.discount
                                                                    ).toFixed(2) > 0
                                                                        ? (
                                                                              scope.row.ship_standard_list_price -
                                                                              scope.row.discount
                                                                          ).toFixed(2)
                                                                        : 0)
                                                            "
                                                        >
                                                        </span>
                          </template>
                        </el-table-column>
                      </el-table>
                      <div class="table-footer">
                        Group Discount (DKK)
                        <el-input
                            class="discount-percent-input"
                            type="number"
                            v-model="golobalDiscount"
                            step="any"
                            min="0"
                            value="0"
                        ></el-input>
                        <el-button class="btn-blue-bg" @click="applyGlobalDiscount(record)"
                        >Apply
                        </el-button
                        >
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div v-else>
      <el-form-item v-loading="loading2"></el-form-item>
    </div>
    <div class="mt-15">
      <el-form-item>
        <el-button class="btn-blue-bg" @click="onSubmit()" v-loading.fullscreen.lock="fullscreenLoading"
        >Save
        </el-button
        >
        <el-button
            v-if="printbtn === true"
            class="btn-blue-bg"
            @click="
                        dialogTableVisible = true;
                        setPrintData()
                    "
        >Preview
        </el-button
        >
        <el-button @click="onCanel">Cancel</el-button>
      </el-form-item>
    </div>
    <el-dialog class="cai-dialog-wrapper" title="" v-model="dialogTableVisible">
      <AgreementInvoiceDialog :data="loadPrintData" :detail="printDetail"/>
    </el-dialog>
  </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
import {merchantService, agreementService, templateService, loadingService} from "../../../services/_singletons"
import {useMainStore} from "@/stores/main"
import {defineComponent} from "@vue/runtime-core"
import AgreementInvoiceDialog from "./AgreementInvoiceDialog.vue"
import moment from "moment/moment";

export default defineComponent({
  name: "UpdateAgreementPage",
  data() {
    return {
      isMobile: false,
      ready: false,
      width: 0,
      height: "auto",
      loading: false,
      dialogTableVisible: false,
      loading2: false,
      search: "",
      pagination: {
        page: 1,
        size: 20,
        sizes: [10, 15, 20, 30, 50, 100],
        layout: "total, ->, prev, pager, next, jumper, sizes",
        small: false
      },
      list: [],
      editMode: false,
      golobalDiscount: 0,
      loading: false,
      itemsChecked: [],
      printData: [],
      selectedCountry: null,
      selectedPriceGroup: null,
      fullscreenLoading: false,
      allowPriceGroupbtn: false,
      printbtn: false,
      printDetail: {},
      currentId: 0,
      dayjs,
      country_from: [],
      country_to: [],
      countryList: [],
      carrierList: [],
      platformList: [],
      countryList: [],
      countryListNew: [],
      allCountries: [],
      customerList: [],
      merchantList: [],
      priceGrouptList: [],
      pricesCollection: [],
      pricesList: [],
      oldPricesList: [],
      tblCountryList: [],
      itemsChecked: [],
      form: {
        company_name: "",
        price_group_id: "",
        merchant_id: "",
        date: moment().format('yyyy-MM-DD'),
        carrier: "",
        address: "",
        country_id: "",
        platform_id: "",
        summary: "",
        footer: "",
        pricing: "",
        agreement_id: ""
      }
    }
  },
  computed: {
    editData() {
      return useMainStore().getCustomer
    },
    pricesListing() {
      return this.pricesList
    },
    dynamicList() {
      return this.form.merchant_current_agreement
    },
    listFiltered() {
      return this.list.filter(obj => {
        let ctrl = false
        for (let k in obj) {
          if (obj[k] && obj[k].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) ctrl = true
        }
        return ctrl
      })
    },
    listSortered() {
      let prop = this.sortingProp
      let order = this.sortingOrder
      return [].concat(
          this.listFiltered.sort((item1, item2) => {
            let val1 = ""
            let val2 = ""

            val1 = item1[prop]
            val2 = item2[prop]
            if (order === "descending") {
              return val2 < val1 ? -1 : 1
            }
            return val1 < val2 ? -1 : 1
          })
      )
    },
    listInPage() {
      let from = (this.currentPage - 1) * this.itemPerPage
      let to = from + this.itemPerPage * 1
      //return this.listSortered.slice(from, to)
      return this.listFiltered.slice(from, to)
    },
    total() {
      return this.listFiltered.length
    },
    currentPage: {
      get() {
        return this.pagination.page
      },
      set(val) {
        this.pagination.page = val
      }
    },
    itemPerPage() {
      return this.pagination.size
    },
    selectedItems() {
      return this.itemsChecked.length || 0
    },
    loadPrintData() {
      return this.printData
    },
    loadDiscount() {
      return this.golobalDiscount
    }
  },
  watch: {
    itemPerPage(val) {
      this.ready = false
      this.currentPage = 1

      setTimeout(() => {
        this.ready = true
      }, 500)
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    init() {
      this.getMerchantList()
      this.getPlatformList()
      if (window.innerWidth <= 768) this.isMobile = true
    },
    formatFixedDecimal(value) {
      return parseFloat(value)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?:\.\d+)?$)/g, "$1,")
    },
    addExperience(idx) {
      this.form.merchant_current_agreement[idx].show = false
      this.form.merchant_current_agreement.push({
        country_from: "",
        country_to: "",
        parcelPerYear: "",
        show: true
      })
    },
    deleteExperience(idx) {
      this.form.merchant_current_agreement.splice(idx, 1)
    },
    //  this is for the discount validation
    setDiscount(row) {
      const gross = row.ship_standard_list_price - row.discount
      // const cst = row.cost_price_plus_markup - gross
      const val = (row.discount = gross >= row.cost_price_plus_markup ? row.discount : 0)
      return val;
    },
    //  this is for the applying the bluk discount
    applyGlobalDiscount(records) {
      console.log(this.loadDiscount)
      // if(this.golobalDiscount > 0)
      // {
      if (this.itemsChecked.length > 0) {
        return records.forEach((value, index) => {
          this.itemsChecked.forEach((val, idx) => {
            if (value['ubsend_price_group_id'] === val['ubsend_price_group_id'])
              value["discount"] = this.loadDiscount
          })
        })
      }
      return records.forEach((value, index) => {

        value["discount"] = this.loadDiscount
      })
      // }
    },
    getCountryByIso(country) {
      console.log(country)

      let city = this.countryList.find(el => el.name.toLowerCase() === country.toLowerCase())
      console.log(city)
      return city.iso
    },
    // setting the printing data
    async setPrintData() {
      // { desc: "DK > DK - GLS Denmark - ShopDelivery 0-1 kg", price: 3540 },
      this.printData = []
      let pricing = [...this.pricesList]
      let modifyPricing = []
      let finalRes = []
      let res = []
      pricing.forEach((value, index) => {
        modifyPricing.push(value.result)
      })
      console.log(pricing, modifyPricing)
      modifyPricing.forEach((value, index) => {
        console.log(value)
        Object.keys(value).forEach((vl, index) => {
          finalRes.push(value[vl])
        })
      })

      console.log(finalRes.flat())
      res = [...finalRes.flat()]
      res.forEach((res, idx) => {
        console.log(res)
        // res.
        let toCountry = this.getCountryByIso(res.title)
        this.printData.push({
          desc: `DK >${toCountry} - ${res.carrier} - ${res.shipvagoo_prodct}  ${res.weight_class_from} - ${res.weight_class_to} kg`,
          price: res.gross_profit
        })
      })
      const selectCustomer = this.customerList.filter(el => el.user_code === this.editData["user_code"])
      this.printDetail = {}
      this.printDetail = {
        name: selectCustomer[0].company_name,
        date: this.form.date,
        summary: this.form.summary,
        address: this.editData["address"]
      }
      console.log(this.printData)
    },
    async onSubmit(values) {
      // display form values on success
      try {
        this.fullscreenLoading = true
        let pricing = [...this.pricesList]
        let modifyPricing = []
        let finalRes = []

        pricing.forEach((value, index) => {
          modifyPricing.push(value.result)
        })
        console.log(pricing, modifyPricing)

        modifyPricing.forEach((value, index) => {
          console.log(value)
          Object.keys(value).forEach((vl, index) => {
            finalRes.push(value[vl])
          })
        })

        console.log(finalRes.flat())
        this.form.pricing = [...finalRes.flat()]
        // loadingService.startLoading('main-loader:login');
        if (this.$route.params.id) {
          this.update()
          return
        }
        const res = await agreementService.updateAgreement(this.form)
        console.log(res)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        await this.$router.push({path: "/customer/customer-list"})
        setTimeout(() => {
          this.fullscreenLoading = false
        }, 500)
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false
        }, 500)
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e,
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
        // notificationService.showError('Error during login');
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async setPricingCollection(collection, index) {
      const orginalArr = this.oldPricesList
      const orginalArrIndex = this.oldPricesList[index].result
      const searchRes = {...collection}
      const res = searchRes.result
      const collectionKey = Object.keys(this.oldPricesList[index].result)
      const filterKey = [...searchRes.country_to]
      const newKeys = []

      collectionKey.forEach((v, i) => {
        filterKey.forEach((vl, i) => {
          console.log(v.toLowerCase(), v, vl.toLowerCase(), res, orginalArr)
          if (v.toLowerCase() === vl.toLowerCase() && filterKey.length === 1) {
            newKeys[v] = [...res[v]]
          }
          if (v.toLowerCase() === vl.toLowerCase() && filterKey.length > 1) {
            newKeys[v] = orginalArr[index].result[v]
          }
        })
      })
      searchRes.result = {...newKeys}
      console.log("collectionKey", collectionKey)
      console.log("filterKey", filterKey)
      console.log("newKeys", newKeys)
      console.log(searchRes, searchRes.country_to.length, newKeys)

      if (searchRes.country_to.length === 0) {
        console.log(1)

        setTimeout(() => {
          this.pricesList = []
          return (this.pricesList = this.oldPricesList.map(item => {
            return {
              title: item.title,
              result: item.result,
              country_from: 58,
              country_to: "",
              showList: false
            }
          }))
        }, 500)
        this.printbtn = false
      }
      if (Object.keys(newKeys).length === 0 && searchRes.country_to.length > 0) {
        console.log(2)

        setTimeout(() => {
          return (this.pricesList = this.oldPricesList.map(item => {
            return {
              title: item.title,
              result: [],
              country_from: 58,
              country_to: item.country_to,
              showList: true
            }
          }))
        }, 500)
        this.printbtn = true
      }
      if (Object.keys(newKeys).length > 0 && searchRes.country_to.length > 0) {
        console.log(3)

        this.loading2 = true
        setTimeout(() => {
          console.log(searchRes)
          return (this.pricesList = this.oldPricesList.map(item => {
            return {
              title: item.title,
              result: item.title.toLowerCase() === searchRes.title.toLowerCase() ? {...searchRes.result} : item.result,
              country_from: 58,
              country_to: searchRes.country_to,
              showList: true
            }
          }))
        }, 500)
        console.log('after', this.pricesList)
        this.printbtn = true
        this.loading2 = false
      }

      // }
    },
    async getCarriersList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await agreementService.getCarrierList()
        this.carrierList = res.data.carriers
        if (this.$route.params.id) {
          const selectedCarrier = this.carrierList.filter(item =>
              item.carrier === this.editData["carrier"] ? item.carrier : null
          )
          this.form.carrier = selectedCarrier[0].carrier
        }
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-left"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getPriceGroups() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        this.form.price_group_id = ''
        const res = await agreementService.getPriceGroups({carrier: this.form.carrier})
        this.priceGrouptList = res.data
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-left"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getPriceList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const t = this.priceGrouptList.find(el => el.id === this.form.price_group_id)
        this.selectedPriceGroup = {...t}

        this.loading2 = true
        this.allowPriceGroupbtn = false
        const res = await agreementService.getPriceList({
          price_group_id: this.form.price_group_id
        })
        const resp = res.data.pricing
        let modifyResponse = []
        this.countryListNew.push(...Object.keys(resp))
        Object.keys(resp).map(index => {
          let country = resp[index]
          const result = country.map((value, idx) => {
            let cp = parseInt(value.cost_price)
            let min_ship_st_price = 0
            let customer_price = 0
            let markup = 0
            let costPrice = cp === 0 || typeof cp === "string" ? 0 : cp + cp * this.defulatPercantage
            let marginPer =
                min_ship_st_price === 0 || costPrice === 0 || cost_price === "string"
                    ? 0
                    : min_ship_st_price - costPrice
            // customer_price is List Price excl. VAT
            // Gross Profit  = List Price excl. VAT - Cost Price
            let grossProfit = customer_price === 0 ? 0 : customer_price - cp
            // costPriceWithMarkup  is Min List Price exc. VAT
            //  Min List Price exc. VAT  = Cost Price + (Cost Price * Markup%)
            let costPriceWithMarkup = cp + cp * markup
            return {
              title: index,
              carrier: this.form.carrier,
              priceGroup: this.selectedPriceGroup.price_group_name,
              ubsend_price_group_id:
                  typeof value.ubsend_price_group_id === "undefined"
                      ? value.id
                      : value.ubsend_price_group_id,
              weight_class_from: value.ubsend_price.weight_class_from,
              weight_class_to: value.ubsend_price.weight_class_to,
              cost_price: value.ubsend_price.cost_price,
              markup: value.markup,
              monty_product: value.ubsend_price.monty_product,
              shipvagoo_prodct: value.ubsend_price.shipvagoo_prodct,
              price_group_id: value.shipvagoo_price_group_id,
              shipvagoo_pricing_id: value.id,
              service: value.service,
              currency: value.ubsend_price.currency,
              lead_time: value.ubsend_price.lead_time,
              margin: marginPer,
              min_ship_standard_list_price: value.min_ship_standard_list_price,
              ship_standard_list_price: value.ship_standard_list_price,
              control_condition: value.control_condition,
              gross_profit: value.margin, // grossProift is margin
              discount: 0, // grossProift is margin
              cost_price_plus_markup: value.cost_price_plus_markup,
              shipvagoo_price_group_id: value.shipvagoo_price_group_id
            }
          })
          modifyResponse[index] = result
        })
        console.log(modifyResponse, modifyResponse.keys())
        this.ready = true
        this.loading2 = false

        const newList = {
          title: this.selectedPriceGroup.price_group_name + "-" + this.form.carrier,
          result: {...modifyResponse},
          country_from: 58,
          country_to: "",
          showList: false
        }
        // newList[this.form.carrier + "-" + this.selectedPriceGroup.price_group_name] = { ...modifyResponse }
        console.log(newList)

        this.pricesCollection = newList
        setTimeout(() => {
          this.fullscreenLoading = false
          this.allowPriceGroupbtn = true
        }, 500)

      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false
        }, 500)
        this.loading2 = false
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        if (e.response?.status === 500) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.message,
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e,
          position: "top-right"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    addGroup() {
      if (this.pricesList.length === 0) {
        this.fullscreenLoading = true
        setTimeout(async () => {
          await this.pricesList.push({...this.pricesCollection})
          this.pricesCollection = []
          this.oldPricesList = this.pricesList
          this.fullscreenLoading = false
        }, 500)
      } else {
        this.pricesList.push({...this.pricesList, ...this.pricesCollection})
        this.fullscreenLoading = true
        setTimeout(() => {
          this.oldPricesList = this.pricesList
          this.fullscreenLoading = false
        }, 500)
      }
    },

    async onCanel() {
      await this.$router.push({path: "/customer/customer-list"})
    },
    handleSelect(item) {
      this.search = item.name
      this.form.country_id = item.id
    },
    querySearch(queryString, cb) {
      var list = this.countryList
      var results = queryString ? list.filter(this.createFilter(queryString)) : list
      // call callback function to return suggestions
      cb(results)
    },
    createFilter(queryString) {
      return link => {
        return link.name.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
      }
    },
    async getMerchantList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await merchantService.getMerchantList()
        this.customerList = res.data.companies
        const selectCustomer = this.customerList.filter(el => el.user_code === this.editData["user_code"])
        this.form.merchant_id = selectCustomer[0].user_code
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getPlatformList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await merchantService.getPlatformList()
        this.platformList = res.data
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getCountryList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await merchantService.getCountryList()
        this.countryList = res.data.countries
        this.allCountries = this.countryList
        if (this.$route.params.id) {
          let countryId = this.countryList.filter(item => item.id === this.editData["country_code"])
          this.form.country_id = countryId[0].id
        }
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getTemplate() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await templateService.getTemplateList({type: "AGREEMENT"})
        const template = res.data.template
        this.form.summary = template.summary
        this.form.footer = template.agreement_footer
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getAgreementDetail() {
      this.fullscreenLoading = true
      // display form values on success
      try {
        console.log(this.editData['update'])
        if (this.editData['update'] === false) {
          return;
        }
        this.form.agreement_id = this.editData['agreement']["id"]
        // loadingService.startLoading('main-loader:login');
        const res = await agreementService.getAgreementById({agreement_id: this.editData['agreement']["id"]})
        const resp = res.data.pricing
        let modifyResponse = []
        this.countryListNew.push(...Object.keys(resp))
        var carrier = ''
        var priceGroupFirst = ''
        Object.keys(resp).map(index => {
          let country = resp[index]
          const result = country.map((value, idx) => {
            console.log(index, value)
            console.log(value.agreement_pricing[0]['price_group']['carrier'])
            let cp = parseInt(value.cost_price)


            if (idx === 0) {
              carrier = value.agreement_pricing[0]['price_group']['carrier']
              priceGroupFirst = value.agreement_pricing[0]['price_group']
              console.log(carrier, priceGroupFirst)
            }

            return {
              title: index,
              carrier: carrier,
              priceGroup: priceGroupFirst.price_group_name,
              ubsend_price_group_id:
                  typeof value.ubsend_price_group_id === "undefined"
                      ? value.id
                      : value.ubsend_price_group_id,
              weight_class_from: value.ubsend_price.weight_class_from,
              weight_class_to: value.ubsend_price.weight_class_to,
              cost_price: value.ubsend_price.cost_price,
              markup: value.markup,
              monty_product: value.ubsend_price.monty_product,
              shipvagoo_prodct: value.ubsend_price.shipvagoo_prodct,
              price_group_id: value.shipvagoo_price_group_id,
              shipvagoo_pricing_id: value.id,
              service: value.service,
              currency: value.ubsend_price.currency,
              lead_time: value.ubsend_price.lead_time,
              margin: value.margin,
              min_ship_standard_list_price: value.min_ship_standard_list_price,
              ship_standard_list_price: value.ship_standard_list_price,
              control_condition: value.control_condition,
              gross_profit: value.margin, // grossProift is margin
              discount: 0, // grossProift is margin
              cost_price_plus_markup: value.cost_price_plus_markup,
              shipvagoo_price_group_id: value.shipvagoo_price_group_id
            }
          })
          modifyResponse[index] = result
        })
        console.log(modifyResponse, modifyResponse.keys(), priceGroupFirst)


        const newList = {
          title: priceGroupFirst.price_group_name + "-" + carrier,
          result: {...modifyResponse},
          country_from: 58,
          country_to: "",
          showList: false
        }
        // newList[this.form.carrier + "-" + this.selectedPriceGroup.price_group_name] = { ...modifyResponse }
        console.log('newList', newList)

        this.pricesCollection = newList
        this.ready = true

        this.addGroup()
      } catch (e) {
        setTimeout(() => {
          this.fullscreenLoading = false
        }, 500)
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }

        this.$notify.error({
          title: "Error",
          message: e,
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    }
  },
  created() {
    this.init()
    this.getAgreementDetail()
    this.getCountryList()
    this.getCarriersList()
    this.getTemplate()
    if (!_.isNull(this.editData)) {

      this.form.address = this.editData["address"]
    }
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt
  },
  components: {ResizeObserver, AgreementInvoiceDialog}
})
</script>
<!--<style>
@import "vue-select/dist/vue-select.css";
</style>-->
<style lang="scss" scoped>
// @import "@vueform/multiselect/themes/default.css";
// @import "vue-search-select/dist/VueSearchSelect.css";
@import "../../../assets/scss/_variables";


textarea {
  .el-textarea__inner {
    height: 114px !important;
  }
}

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}

.bt-br-gray {
  border-top: 1px solid #ebeef5;
}

.bb-br-gray {
  border-bottom: 1px solid #ebeef5;
}

.el-select {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  line-height: 32px;
  width: 100%;
}

.accent-text {
  color: #006efb;
}

.el-collapse-item .el-collapse-bg-gray {
  background: #151526 !important;
}

.el-collapse-item__header {
  background-color: #151526 !important;
  background: #151526 !important;
}
</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.collapse-parent-item {
  .el-collapse-item__header {
    padding-left: 24px;
    padding-right: 17px;
    background-color: #151526;
    color: #fff;
  }

  .collapse-child-item {
    .el-collapse-item__header {
      background: #4a596a;
    }
  }
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  align-items: center;
  gap: 10px;

  .discount-percent-input {
    width: 70px;
  }
}

.cai-dialog-wrapper {
  &.el-dialog {
    margin-bottom: 120px !important;
  }
}

@media print {
  .cai-dialog-wrapper {
    &.el-dialog {
      width: 100% !important;
      margin-top: 0 !important;
    }
  }
  .download-pdf-button-wrapper {
    display: none;
  }
}

.el-input__wrapper.is-focus-danger {
  box-shadow: 0 0 0 1px red inset;
}

.el-input__wrapper-focus-danger {
  box-shadow: 0 0 0 1px red inset;
}

.d-none {
  display: none
}

// box-shadow: 0 0 0 1px red inset;
</style>
