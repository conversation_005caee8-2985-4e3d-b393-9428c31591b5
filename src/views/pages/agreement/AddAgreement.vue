<template>
    <div class="scrollable only-y p-2" >
        <div class="toolbar-box flex align-center">
            <div class="page-header">
                <h1 class="ml-2">Add Agreement</h1>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                    <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
                    <el-breadcrumb-item  :to="{ path: '/agreement/add-agreement' }">Assign Agreement Pricing</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>

        <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">    
            <el-form ref="agreementForm" class="mb-50 bb-br-gray" :model="form" label-width="120px">
                <h4  class="ml-12">New Agreement</h4>
                <el-form-item label="Merchant">
                    <el-col class="demo-form-inline" :span="24">
                        <el-select class="full_width" v-model="form.merchant" placeholder="Merchant">
                            <template v-for="data  in merchantList" :key="data">
                                <el-option  :label="data.company_name" :value="data.user.id"></el-option>
                            </template>
                        </el-select>
                    </el-col>    
                </el-form-item>
                <el-form-item label="Carrier">
                    <el-col class="demo-form-inline" :span="12">
                        <el-col class="demo-form-inline" >
                            <el-select class="full_width" @change="getPriceGroups()" v-model="form.carrier" placeholder="Carrier">
                                <template v-for="data  in carrierList" :key="data">
                                    <el-option  :label="data" :value="data"></el-option>
                                </template>
                            </el-select>
                        </el-col>    
                    </el-col>   
                    <el-col class="demo-form-inline" :span="12">
                        <el-form-item label="Price Group">
                            <el-select class="full_width" @change="getPriceGroup()" v-model="form.price_group" placeholder="Price Group">
                                <template v-for="data  in priceGrouptList" :key="data">
                                    <el-option  :label="data.price_group_name" :value="data.id"></el-option>
                                </template>
                            </el-select>
                        </el-form-item> 
                        </el-col>       
                </el-form-item>


                <el-form-item>
                    <el-button class="btn-blue-bg" type="submit">Add Group</el-button>
                    <el-button @click="onCanel">Reset</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bt-br"></div>
            <div v-if="loading2 === false" class="p-3">
                <div
                    v-for="(record, index) in pricingList"
                    :key="index"
                    class="card-base card-shadow--medium demo-box bg-white mb-5"
                >
                    <el-collapse value="index">
                        <el-collapse-item :title="index" name="index" class="el-collapse-bg-gray">
                            <div
                                class="table-box card-base card-shadow--medium box grow"
                                id="table-wrapper"
                                v-loading="!ready"
                            >
                                <el-table
                                    :data="record"
                                    style="width: 100%"
                                    :height="height"
                                    v-if="ready"
                                    @selection-change="handleSelectionChange"
                                >
                                    <el-table-column
                                        label="SN"
                                        min-width="45"
                                        prop="s_no"
                                        :fixed="!isMobile"
                                    >
                                        <template #default="scope">
                                            <span class="sel-string" v-html="scope.$index + 1"></span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="Service"
                                        prop="service"
                                        min-width="150"
                                        :fixed="!isMobile"
                                    >
                                    <template #default="scope">
                                        <span
                                        class="sel-string"
                                        v-html="
                                            scope.row.ubsend_price.service 
                                        "
                                    ></span>
                                    </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="Weight From"
                                        prop="weight_class_from" 
                                        :fixed="!isMobile"
                                        min-width="80" >
                                        <template #default="scope">
                                            <span
                                                class="sel-string"
                                                v-html="
                                                    scope.row.ubsend_price.weight_class_from 
                                                "
                                            ></span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="Weight To" prop="weight_class_to" min-width="80"  :fixed="!isMobile">
                                        <template #default="scope">
                                            <span
                                                class="sel-string"
                                                v-html="
                                                    scope.row.ubsend_price.weight_class_to 
                                                "
                                            ></span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="Cost Price" prop="cost_price" min-width="65"  :fixed="!isMobile">
                                        <template #default="scope">
                                            <span
                                                class="sel-string"
                                                v-html="
                                                    scope.row.ubsend_price.cost_price 
                                                "
                                            ></span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="Shipping Product" prop="shipping_product" min-width="300">
                                        <template #default="scope">
                                            <el-input
                                                    type="number"
                                                v-model="scope.row.ubsend_price.shipping_product"
                                                placeholder="shipping_product"
                                            ></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="Markup %" prop="markup" min-width="80">
                                        <template #default="scope">
                                            <el-input
                                                    type="number"
                                                v-model="scope.row.markup"
                                                placeholder="markup"
                                            ></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="Cost Price (Ubsend) +Markup"
                                        prop="cost_price_plus_markup"
                                        min-width="100"
                                    >
                                    </el-table-column>
                                    <el-table-column label="Margin" prop="margin" min-width="80">
                                    </el-table-column>
                                    <el-table-column
                                        label="Min Shipvagoo Std List Price excl. Vat"
                                        prop="min_ship_standard_list_price"
                                        min-width="100"
                                    >
                                        <template #default="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.min_ship_standard_list_price"
                                                placeholder="minShip Standard List Price"
                                            ></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="Customer Price excl. Vat"
                                        prop="customer_price"
                                        min-width="100"
                                    >
                                        <template #default="scope">
                                            <el-input
                                                v-model="scope.row.customer_price"
                                                placeholder="customer_price"
                                            ></el-input>
                                        </template>
                                    </el-table-column>
                                    <!-- <el-table-column
                                        label="Control Condition(Price excl. Vat >Cost Price +Markup"
                                        prop="control_condition"
                                        min-width="150"
                                    >
                                        <template #default="scope">
                                            <el-input
                                                v-model="scope.row.control_condition"
                                                placeholder="control Condition"
                                            ></el-input>
                                        </template>
                                    </el-table-column> -->
                                </el-table>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </div>
            <div v-else>
                <el-form-item v-loading="loading2"> </el-form-item>
            </div>
    </div>
</template>

<script>
import _ from "lodash"
import dayjs from "dayjs"
import ResizeObserver from "@/components/vue-resize/ResizeObserver.vue"
import {
    agreementService,
    merchantService,
    loadingService
} from "../../../services/_singletons"
import { defineComponent } from "@vue/runtime-core"

export default defineComponent({
    name: "AddAgreement",
    data() {
        return {
            isMobile: false,
            ready: false,
            loading2: false,
            width: 0,
            height: "auto",
            loading: false,
            search: "",
            editMode: false,
            itemsChecked: [],
            merchantList: [],
            carrierList: [],
            priceGrouptList: [],
            pricingList: [],
            dialogUserVisible: false,
            currentId: 0,
            dayjs,
            form: {
                merchant: "",
                carrier: "",
                price_group: ""
            },
        }
    },
    computed: {},
    watch: {},
    methods: {
        handleSelectionChange(val) {
            this.itemsChecked = val
        },
        init() {
            if (window.innerWidth <= 768) this.isMobile = true
        },
        onCanel(){
            this.$refs.agreementForm.resetFields()
            var self = this;
            Object.keys(this.form).forEach(function(key,index) {
                self.form[key] = '';
            });
        },
        async onSubmit(values) {
            // display form values on success
            try {
                // loadingService.startLoading('main-loader:login');
                await merchantService.addMerchant(this.form)
                this.$notify({
                    title: "Success",
                    message: "Merchant Added Successfully",
                    type: "success"
                })
                await this.$router.push({path:"/"})
            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: "Merchant data failed to create",
                        position: "top-left"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: "Error during creating record",
                    position: "top-left"
                })
                // loggingService.error('Error during login', e);
                // notificationService.showError('Error during login');
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        },
        async getMerchantList() {
            // display form values on success
            try {
                // loadingService.startLoading('main-loader:login');
                const res = await merchantService.getMerchantList();
                this.merchantList = res.data.companies
                  
            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message:  e.response.data.error[0],
                        position: "top-left"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message:  e.response.data.error[0],
                    position: "top-left"
                })
                // loggingService.error('Error during login', e);
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        
        },
        async getCarriersList() {
            // display form values on success
            try {
                // loadingService.startLoading('main-loader:login');
                const res = await agreementService.getCarrierList()
                this.carrierList = res.data.carriers
                if(this.$route.params.id)
                {
                    const selectedCarrier = this.carrierList.filter((item) => item.carrier === this.editData["carrier"] ? item.carrier:null )
                    this.form.carrier =selectedCarrier[0].carrier
                    this.getPriceList()
                }

            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-left"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0],
                    position: "top-left"
                })
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        },
        async getPriceGroup() {
            // display form values on success
            try {
                // loadingService.startLoading('main-loader:login');
                this.loading2 = true
                const res = await agreementService.getPriceList({'price_group_id':this.form.price_group})
                this.pricingList = res.data.pricing
                const resp = res.data.pricing
                let newRs = []
                Object.keys(resp).map(index => {
                let country = resp[index]
                console.log(resp,country)

                const result = country.map((value, idx) => {
                    let costPrice =value.cost_price!==0 && value.cost_price !=='xx,xx'  ?value.cost_price+(value.cost_price*defulatPercantage):0
                    let marginPer =value.min_ship_standard_list_price!==0&&costPrice!==0 ? value.min_ship_standard_list_price-costPrice:0
                    console.log(value.cost_price,costPrice,marginPer)
                   
                        // shipvagoo_price_group_id
                        
                    return {
                        title: index,
                        ubsend_price_group_id: value.id,
                        weight_class_from: value.weight_class_from,
                        weight_class_to: value.weight_class_to,
                        cost_price: value.cost_price,
                        markup: 0,
                        shipping_product: value.shipping_product,
                        cost_price_plus_markup: "",
                        cost_price_plus_markup: costPrice,
                        margin: marginPer,
                        min_ship_standard_list_price: value.min_ship_standard_list_price,
                        ship_standard_list_price:value.ship_standard_list_price,
                        customer_price: 0,
                        control_condition: ""
                    }
                });
                  newRs[index] =result
                })
                console.log(newRs)
                this.ready = true
                this.loading2 = false
                this.pricesList ={...newRs}

            } catch (e) {
                this.loading2 = false
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-left"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0],
                    position: "top-left"
                })
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        },
        async getPriceGroups() {
            // display form values on success
            try {
                // loadingService.startLoading('main-loader:login');
                const res = await agreementService.getPriceGroups({'carrier':this.form.carrier})
                console.log(res)
                this.priceGrouptList = res.data
                if(this.$route.params.id)
                {
                    const selectedCarrier = this.carrierList.filter((item) => item.carrier === this.editData["carrier"] ? item.carrier:null )
                    this.form.priceGrouptList =selectedCarrier[0].carrier
                    this.getPriceList()
                }

            } catch (e) {
                if (e.response?.status === 422) {
                    this.$notify.error({
                        title: "Error",
                        message: e.response.data.error[0],
                        position: "top-left"
                    })
                    return
                }
                this.$notify.error({
                    title: "Error",
                    message: e.response.data.error[0],
                    position: "top-left"
                })
            } finally {
                loadingService.stopLoading("main-loader:login")
            }
        },
    },
    created() {
        this.init()
        this.getMerchantList()
        this.getCarriersList()
    },
    mounted() {
        //ie fix
        if (!window.Number.parseInt) window.Number.parseInt = parseInt
    },
    components: { ResizeObserver }
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.page-table {
    &.overflow {
        overflow: auto;
    }

    .toolbar-box {
        &.hidden {
            visibility: hidden;
        }
    }

    .table-box {
        overflow: hidden;

        &.hidden {
            visibility: hidden;
        }
    }
}
.bb-br-gray{
    border-bottom: 1px solid #ebeef5;
}

.el-collapse-bg-gray{
    background:#151526 !important;
}

.el-collapse-item__header{
    background:#151526 !important;
}


</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
    padding: 20px;

    .toolbar-box {
        margin-bottom: 10px;
        margin-top: 0;
    }

    .clickable {
        cursor: pointer;
        text-decoration: underline;
        font-weight: bold;
    }

    .sel-string {
        .sel {
            background: transparentize($text-color-primary, 0.8);
            border-radius: 5px;
            //text-transform: uppercase;
        }
    }
}

@media (max-width: 768px) {
    .page-table {
        .toolbar-box {
            display: block;
            overflow: hidden;
            font-size: 80%;
            padding-bottom: 10px;

            & > * {
                display: inline-block;
                min-width: 120px;
                height: 22px;
                //background: rgba(0, 0, 0, 0.04);
                margin-bottom: 16px;
            }
        }
    }
}

.full_width{
    width:100%;
}
</style>
