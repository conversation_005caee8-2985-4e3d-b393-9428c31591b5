<template>
    <div class="customer-agreement-dialog position-relative">
        <section id="customer-invoice" ref="DownloadComp">
            <div class="logo-wrapper logo" style="text-align: right">
                <img src="@/assets/images/logo-black.svg" class="logo" alt="logo" />
            </div>
            <div class="user-info">
                <h3 class="customer-name m-0 black-text" style="margin: 5px 0">{{ detail.name }}</h3>
                <p class="m-0 black-text" style="margin: 5px 0">{{ detail && detail.address }}</p>
                <!-- <p class="m-0 black-text" style="margin: 5px 0">DK-2200 Kobenhavn N</p> -->
                <p class="text-right date m-0 black-text" style="text-align: right">
                    <strong v-if="detail">Date:</strong> {{ getDate }}
                </p>
            </div>
            <div class="application">
                <p class="black-text">
                    Dear, <strong>"{{ detail.name }}"</strong>
                </p>
                <p v-if="detail" class="application-description m-0 black-text">
                    {{ detail && detail.summary }}
                </p>
            </div>
            <div class="table mt-10">
                <table style="width: 100%; border-collapse: collapse">
                    <thead style="border-bottom: 2px solid #8b8b8b">
                        <tr>
                            <td class="black-text" style="padding-bottom: 5px">
                                <strong style="padding-left: 10px"> Description </strong>
                            </td>
                            <td class="black-text" style="text-align: right; width: 160px; padding-bottom: 5px">
                                <strong style="padding-right: 10px"> Price excl. VAT </strong>
                            </td>
                        </tr>
                    </thead>
                    <tbody style="border-bottom: 2px solid #8b8b8b">
                        <tr v-for="(data, index) in getData" :key="index">
                            <td>
                                <p
                                    style="
                                        margin: 0;
                                        margin-bottom: 3px;
                                        margin-top: 3px;
                                        color: #bbbbbb;
                                        padding-left: 10px;
                                    "
                                    :style="`${index === 0 ? 'margin-top: 10px' : ''} ${
                                        index + 1 === tableData.length ? 'margin-bottom: 10px' : ''
                                    }`"
                                >
                                    {{ data.desc }}
                                </p>
                            </td>
                            <td style="text-align: right; color: #bbbbbb">
                                <p
                                    style="margin: 0; margin-bottom: 3px; margin-top: 3px; padding-right: 10px"
                                    :style="`${index === 0 ? 'margin-top: 10px' : ''} ${
                                        index + 1 === tableData.length ? 'margin-bottom: 10px' : ''
                                    }`"
                                >
                                    {{ data.price.replace(/./, ',') }}
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="invoice-footer">
                <!-- <p class="text-right black-text" style="text-align: right">Side 1 of 2</p> -->
                <div class="footer-info">
                    <p v-if="detail" style="text-align: center">
                        {{ detail && detail.footer }}
                        <!-- ShipVago Aps &#8226; Laplandsgade 4A &#8226; DK-2300 Kobenhavn S <br />
                        DK12345678 &#8226; support@shipvagoo &#8226; com &#8226; +45 12 345 678 -->
                    </p>
                </div>
            </div>
        </section>
        <div class="download-pdf-button-wrapper">
            <el-button class="btn-blue-bg" @click="printDownload">Downlaod PDF</el-button>
        </div>
    </div>
</template>
<script>
import moment from "moment/moment";

export default {
    name: "AgreementInvoiceDialog",
    props: {
        data:{
            type:Array,
            defualt:[],
        },
        detail:{
            type:Object,
            defualt:{},
        }
    }
    ,
    data() {
        return {
            tableData: [
                // { desc: "DK > DK - GLS Denmark - ShopDelivery 0-1 kg", price: 3540 },
            ]
        }
    },
    computed: {
        getDate() {
            if (this.detail.date) {
                return moment(this.detail.date).format('yyyy-MM-DD')
            } else {
                return moment().format('yyyy-MM-DD')
            }
        },
        getData(){
            return this.data
        }

    },
    methods: {
        printDownload() {
            let w = window.open()
            w.document.write(this.$refs.DownloadComp.innerHTML)
            w.document.close()
            w.setTimeout(function () {
                w.print()
            }, 200)
        }
    },
  
    destory(){
        this.data =[]
        this.detail =[]
    }
}
</script>
<style lang="scss" scoped>
:deep(.el-table) {
    thead {
        .cell {
            color: #000;
        }
    }
}
.download-pdf-button-wrapper {
    position: absolute;
    bottom: -70px;
    left: 50%;
    transform: translate(-50%);
}
</style>
