<template>
  <div>
    <div class="scrollable mws-custom-scroll only-y p-2">
      <div class="toolbar-box flex align-center">
        <div class="page-header">
          <h1 class="ml-2">Customer Agreement</h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
            <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
            <el-breadcrumb-item>Customer Agreement</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <div v-loading="pricingLoading" class="card-base card-shadow--medium search-card scrollable only-y mb-5 p-10">
        <el-form ref="form" class="mb-50 bb-br-gray" :model="form" label-width="120px">
          <h4 class="ml-12">New Agreement</h4>

          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Customer">
                <el-input disabled v-model="form.merchant_id"></el-input>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Date">
                <el-date-picker style="width: 100%" v-model="form.date" type="date" placeholder="">
                </el-date-picker>
                <!-- <el-input v-model="form.date" type="date" placeholder=""></el-input> -->
              </el-form-item>
            </el-col>
          </el-col>

          <el-form-item label="Address">
            <el-input disabled v-model="form.address"></el-input>
          </el-form-item>

          <el-form-item label="Summary">
            <el-input type="textarea" rows="5" v-model="form.summary"></el-input>
          </el-form-item>

          <el-form-item label="Footer" class="d-none" hidden>
            <el-input disabled type="textarea" rows="5" v-model="form.footer"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <div v-loading="pricingLoading" class="card-base card-shadow--medium search-card scrollable only-y mt-15 p-10">
        <el-form ref="agreementForm" class="mb-50 bb-br-gray" :model="form" label-width="120px">
          <h4 class="ml-12">Add Price Group</h4>
          <el-form-item label="Carrier">
            <el-col class="demo-form-inline" :span="12">
              <el-col class="demo-form-inline">
                <el-select
                    class="full_width"
                    @change="getPriceGroups()"
                    v-model="form.carrier"
                    placeholder="Carrier"
                >
                  <template v-for="data in pricing.carriers" :key="data">
                    <el-option :label="data" :value="data"></el-option>
                  </template>
                </el-select>
              </el-col>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label="Price Group">
                <el-select
                    class="full_width"
                    v-model="form.price_group_id"
                    placeholder="Price Group"
                >
                  <template v-for="data in priceGrouptList" :key="data">
                    <el-option :label="data.price_group_name" :value="data.id"></el-option>
                  </template>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>

          <el-form-item>
            <el-button
                v-if="form.price_group_id !== '' && form.carrier !== '' && allowPriceGroupbtn === true"
                class="btn-blue-bg"
            >Add Group
            </el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <div class="pricingLoading" v-loading="pricingLoading">
        <div
            v-for="(parentList, idx) in pricing.pricing"
            :key="idx"
            class="card-base card-shadow--medium demo-box bg-white mb-5 withDelete"
            style="background: #151526 !important"

        >
          <!--          @click="deleteGroup(idx)"-->
          <el-button
              @click="deleteGroup(idx)"
              class="btn-danger btnDelete"
          >Delete Group
          </el-button>
          <el-collapse value="index" class="collapse-parent " v-if="idx">
            <el-collapse-item :title="idx" name="index" class="collapse-parent-item">

              <el-col class="demo-form-inline flex m-10" :span="24">
                <el-col class="demo-form-inline ml-10 mr-20" :span="11">
                  <el-form-item label="Country From">
                    <el-select
                        filterable
                        disabled
                        placeholder=""
                        v-model="groups.country_from[mwsReplaceAll(idx,'-','_')]"
                    >
                      <el-option
                          v-for="item in countryList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col class="demo-form-inline mr-5" :span="12">
                  <el-form-item label="Country To">
                    <el-select
                        filterable
                        multiple
                        v-model="groups.country_to[mwsReplaceAll(idx,'-','_')]"
                        placeholder="Country To"
                        @change="newPricingGroup(mwsReplaceAll(idx,'-','_'),$event)"
                    >
                      <el-option
                          v-for="item in (countryList)"
                          :key="item.id"
                          :label="item.name"
                          :value="item.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-col>
              <!--   v-for="(record, index) in parentList" -->
              <div class="m-10">
                <div

                    v-for="option in groups.country_to[mwsReplaceAll(idx,'-','_')]"
                    :key="`Denmark - ${option}`"
                    class="card-base card-shadow--medium demo-box bg-white mb-5"
                    style="background: #151526 !important"
                >
                  <el-collapse value="index" class="collapse-child">
                    <el-collapse-item
                        :title="`Denmark - ${option}`"
                        name="index"
                        class="collapse-child-item"
                    >
                      <div
                          class="table-box card-base card-shadow--medium box grow"
                          id="table-wrapper"
                      >
                        <UpdateTableList
                            @latestData="latestFormData"
                            :option="option"
                            :lists="(parentList[`Denmark - ${option}`] !==undefined?parentList[`Denmark - ${option}`]:defaultGroup[mwsReplaceAll(idx,'-','_')])"/>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <div class="mt-15">
        <el-form-item>
          <el-button :disabled="lastData.length===0" class="btn-blue-bg" @click="onSubmit()" v-loading.fullscreen.lock="fullscreenLoading"
          >Save
          </el-button>
          <el-button @click="onCanel">Cancel</el-button>
        </el-form-item>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {agreementService, loadingService, merchantService} from "@/services/_singletons";
import {mwsReplaceAll} from "@/helpers";
import UpdateTableList from "@/views/pages/agreement/UpdateTableList.vue";

export default {
  name: "UpdateCustomerAgreement",
  components: {UpdateTableList},
  props: ['agreementId'],
  setup(props) {
    return {
      agreementId: props.agreementId
    }
  },
  data() {
    return {
      customerList: [],
      itemsChecked: [],
      golobalDiscount: 0,
      activeObject: [],
      groups: {
        country_to: {},
        country_from: {},
        shipvagoo_pricing_id: {},
        price_group_id: {},
        discount: {}
      },
      countryList: [],
      priceGrouptList: [],
      countryGroups: [],
      pricing: [],
      pricingLoading: false,
      defaultGroup: {},
      lastData: [],
      form: {
        company_name: '',
        price_group_id: "",
        merchant_id: "",
        date: '',
        carrier: "",
        address: "",
        country_id: "",
        platform_id: "",
        summary: "",
        footer: "",
        pricing: "",
        discount: {}
      }
    }
  },
  created() {
    this.getCustomerAgreement()
    this.getCountryList()
  },
  methods: {
    mwsReplaceAll,
    async getCustomerAgreement() {
      try {
        this.pricingLoading = true
        const res = await agreementService.getCustomerDetail({agreement_id: this.$route.params.agreementId})
        this.pricingLoading = false
        this.pricing = res.data ?? []
        let groups = [];
        let pricing = res.data.pricing;
        console.log('here')
        Object.keys(pricing).map((groupName, ii) => {
          let list = [];
          Object.keys(pricing[groupName]).map((name, i) => {
            if (i === 0) {
              try {
                pricing[groupName][name].map((row, index) => {
                  let data = {
                    "agreement_id": row.agreementId,
                    "price_group_id": row.price_group_id,
                    "shipvagoo_product": row.shipvagoo_product,
                    "shipvagoo_pricing_id": row.shipvagoo_pricing_id,
                    "discount": 0,
                    "markup": row.markup,
                    "margin": row.margin,
                    "min_ship_standard_list_price": row.min_ship_standard_list_price,
                    "ship_standard_list_price": row.ship_standard_list_price,
                    "weight_from": row.weight_from,
                    "weight_to": row.weight_to
                  }
                  this.activeObject.push(data);
                })
                this.defaultGroup[mwsReplaceAll(groupName, '-', '_')] = this.activeObject;
              } catch (e) {

                console.log(e)
              }
            }
            let key_array = name.split(' - ')
            list.push(key_array[1] ?? '')
            groups[groupName] = list;
          })
          this.groups.country_to[mwsReplaceAll(groupName, '-', '_')] = list
          this.groups.country_from[mwsReplaceAll(groupName, '-', '_')] = 58
        })
        this.countryGroups = groups
        this.form.merchant_id = this.pricing.agreement_detail.company_name
        this.form.address = this.pricing.agreement_detail.address
        this.form.summary = this.pricing.agreement_detail.summary
        this.form.footer = this.pricing.agreement_detail.footer
        this.form.date = moment(this.pricing.agreement_detail.date).format('yyyy-MM-DD')
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-left"
        })
        this.pricingLoading = false
      }
    },
    async getPriceGroups() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        this.form.price_group_id = ''
        const res = await agreementService.getPriceGroups({carrier: this.form.carrier})
        this.priceGrouptList = res.data
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-left"
        })
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async getCountryList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await merchantService.getCountryList()
        this.countryList = res.data.countries
        this.allCountries = this.countryList
        if (this.$route.params.id) {
          let countryId = this.countryList.filter(item => item.id === this.editData["country_code"])
          this.form.country_id = countryId[0].id
        }
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    newPricingGroup(priceGroup, e) {
      console.log(priceGroup, e)
    },
    async onCanel() {
      await this.$router.push({path: "/customer/customer-list"})
    },
    async onSubmit(values) {

      const agreementId = this.$route.params.agreementId;
      const formData = new FormData();
      formData.append(`agreement_id`,agreementId)
      this.lastData.map((row,i)=>{
        formData.append(`pricing[${i}][price_group_id]`,row.price_group_id)
        formData.append(`pricing[${i}][shipvagoo_pricing_id]`,row.shipvagoo_pricing_id)
        formData.append(`pricing[${i}][discount]`,row.discount)
      });

      try{
        const res = await agreementService.updateMerchantAgreement(formData)
        console.log(res,'res')
        this.$notify.success({
          title: "Success",
          message: "Agreement Update Successfully!",
          position: "top-left"
        })
        await this.$router.push({path: "/customer/customer-list"})
      }
      catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-left"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-left"
        })
      }
    },
    latestFormData(data) {
      data.map((row, index) => {
        this.lastData.push(row)
      })
    },
    async deleteGroup(idx) {
      await this.$confirm("Are you sure to delete this group. Continue?", "Warning", {
        confirmButtonText: "OK",
        cancelButtonText: "Cancel",
        type: "warning",
        center: true
      })
          .then(async () => {
            delete this.pricing.pricing[idx]
            this.lastData=[]

          })
          .catch(() => {
          })

    }
  }
}
</script>

<!--<style>
@import "vue-select/dist/vue-select.css";
</style>-->
<style lang="scss" scoped>
// @import "@vueform/multiselect/themes/default.css";
// @import "vue-search-select/dist/VueSearchSelect.css";
@import "../../../assets/scss/_variables";


textarea {
  .el-textarea__inner {
    height: 114px !important;
  }
}

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}

.bt-br-gray {
  border-top: 1px solid #ebeef5;
}

.bb-br-gray {
  border-bottom: 1px solid #ebeef5;
}

.el-select {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  line-height: 32px;
  width: 100%;
}

.accent-text {
  color: #006efb;
}

.el-collapse-item .el-collapse-bg-gray {
  background: #151526 !important;
}

.el-collapse-item__header {
  background-color: #151526 !important;
  background: #151526 !important;
}
</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.collapse-parent-item {
  .el-collapse-item__header {
    padding-left: 24px;
    padding-right: 17px;
    background-color: #151526;
    color: #fff;
  }

  .collapse-child-item {
    .el-collapse-item__header {
      background: #4a596a;
    }
  }
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  align-items: center;
  gap: 10px;

  .discount-percent-input {
    width: 70px;
  }
}

.cai-dialog-wrapper {
  &.el-dialog {
    margin-bottom: 120px !important;
  }
}

@media print {
  .cai-dialog-wrapper {
    &.el-dialog {
      width: 100% !important;
      margin-top: 0 !important;
    }
  }
  .download-pdf-button-wrapper {
    display: none;
  }
}

.el-input__wrapper.is-focus-danger {
  box-shadow: 0 0 0 1px red inset;
}

.el-input__wrapper-focus-danger {
  box-shadow: 0 0 0 1px red inset;
}

.d-none {
  display: none
}

.withDelete {
  position: relative;
}

.withDelete .el-collapse-item__header {
  padding-right: 130px;
  position: relative;
}

.withDelete .btnDelete {
  position: absolute;
  top: 0;
  right: 0px;
  margin: 8px;
  background: #f00;
  color: #fff;
  border-color: #f00;
  z-index: 99;
}

.scrollable.mws-custom-scroll {
  height: calc(100vh - 134px);
}

// box-shadow: 0 0 0 1px red inset;
</style>