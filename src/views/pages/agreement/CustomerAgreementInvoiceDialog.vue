<template>
    <div class="customer-agreement-dialog position-relative">
        <section id="customer-invoice" ref="DownloadComp">
            <div class="logo-wrapper logo" style="text-align: right">
                <img src="@/assets/images/logo-black.svg" class="logo" alt="logo"/>
            </div>
            <div class="user-info">
                <h3 class="customer-name m-0 black-text" style="margin: 5px 0">{{ detail.name }}</h3>
                <p class="m-0 black-text" style="margin: 5px 0">{{ detail && detail.address }}</p>
                <!-- <p class="m-0 black-text" style="margin: 5px 0">DK-2200 Kobenhavn N</p> -->
                <p class="text-right date m-0 black-text" style="text-align: right">
                    <strong v-if="detail">Date:</strong> {{ getDate }}
                </p>
            </div>
            <div class="application">
                <p class="black-text">
                    Dear, <strong>{{detail.name }}</strong>
                </p>
                <div v-if="detail" class="application-description m-0 black-text" style="margin-left: 8px;">
                    <div v-html="detail && detail.summary"></div>
                </div>
                <p></p>
                <p></p>
                <p></p>
            </div>
            <div class="table mt-10">
                <table style="width: 100%;border-collapse: collapse; table-layout:fixed">
                    <thead style="border-bottom: 2px solid #8b8b8b">
                    <tr>
                        <td class="black-text" style="padding-bottom: 5px">
                            <strong style="padding-left: 10px"> Description </strong>
                        </td>
                        <td class="black-text" style="text-align: right; width: 160px; padding-bottom: 5px">
                            <strong style="padding-right: 10px"> Price excl. VAT </strong>
                        </td>
                    </tr>
                    </thead>
                    <tbody style="border-bottom: 2px solid #8b8b8b">
                    <tr v-for="(data, index) in getData" :key="index">
                        <td style="
                          white-space:nowrap;
                          margin: 0;
                                        margin-bottom: 3px;
                                        margin-top: 3px;
                                        color: #bbbbbb;
                                        padding-left: 10px;"
                            :style="`${index === 0 ? 'margin-top: 10px' : ''} ${
                                        index + 1 === tableData.length ? 'margin-bottom: 10px' : ''
                                    }`">
                            {{ data.desc }}
                        </td>
                        <td style="text-align: right; color: #bbbbbb">
                            <p
                                    style="margin: 0; margin-bottom: 3px; margin-top: 3px; padding-right: 10px"
                                    :style="`${index === 0 ? 'margin-top: 10px' : ''} ${
                                        index + 1 === tableData.length ? 'margin-bottom: 10px' : ''
                                    }`"
                            >
                                {{ danishNumberFormat(data.price) }}
                                <!--                                    {{ data.price.replace(".", ",") }}-->
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="invoice-footer">
                <!-- <p class="text-right black-text" style="text-align: right">Side 1 of 2</p> -->
                <div class="footer-info">
                    <div v-if="detail" style="text-align: center">
                        <div v-html="detail.footer" style="line-height: 8px;">

                        </div>
                        <!-- ShipVago Aps &#8226; Laplandsgade 4A &#8226; DK-2300 Kobenhavn S <br />
                        DK12345678 &#8226; support@shipvagoo &#8226; com &#8226; +45 12 345 678 -->
                    </div>
                </div>
            </div>
        </section>
        <div class="download-pdf-button-wrapper">
            <el-button class="btn-blue-bg" @click="printDownload">Download PDF</el-button>
        </div>
    </div>
</template>
<script>
    import moment from "moment/moment"
    import {danishNumberFormat} from "../../../helpers";

    export default {
        name: "CustomerAgreementInvoiceDialog",
        props: {
            data: {
                type: Array,
                defualt: []
            },
            detail: {
                type: Object,
                defualt: {}
            }
        },
        data() {
            return {
                tableData: [
                    // { desc: "DK > DK - GLS Denmark - ShopDelivery 0-1 kg", price: 3540 },
                ]
            }
        },
        computed: {
            getDate() {
                if (this.detail.date) {
                    return moment(this.detail.date).format("DD-MM-yyyy")
                } else {
                    return moment().format("DD-MM-yyyy")
                }
            },
            getData() {
                return this.data
            }
        },
        methods: {
            danishNumberFormat,
            printDownload() {
                let w = window.open()
                w.document.write("<html><head><style>")
                w.document.write('@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans&display=swap");')
                w.document.write(
                    '@font-face { font-family: "MyCustomFont"; src: url(../../../../../assets/fonts/NunitoSans-ExtraLightItalic.woff2) format("woff2"); }'
                )
                w.document.write('body { font-family: "Nunito Sans", sans-serif !important; }')
                w.document.write("</style></head><body>")
                w.document.write(this.$refs.DownloadComp.innerHTML)
                w.document.write("</body></html>")
                w.document.close()
                w.setTimeout(function () {
                    w.print()
                }, 200)
            }
        },

        destory() {
            this.data = []
            this.detail = []
        },
        mounted() {
            console.log(this.detail)
        }
    }
</script>
<style lang="scss" scoped>
    :deep(.el-table) {
        thead {
            .cell {
                color: #000;
            }
        }
    }

    .download-pdf-button-wrapper {
        position: absolute;
        bottom: -70px;
        left: 50%;
        transform: translate(-50%);
    }
</style>
