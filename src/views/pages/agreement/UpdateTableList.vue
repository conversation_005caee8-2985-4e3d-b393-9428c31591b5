<template>
  <div>
    <el-table
        :data="lists"
        style="width: 100%"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="34" fixed></el-table-column>
      <el-table-column
          label="Id"
          min-width="45"
          prop="s_no"
          :fixed="!isMobile"
      >
        <template #default="scope">
          <span class="sel-string" v-html="scope.$index + 1"></span>
        </template>
      </el-table-column>
      <el-table-column
          label="Shipvagoo Product"
          prop="shipvagoo_prodct"
          min-width="150"
          :fixed="!isMobile"
      >
        <template #default="scope">
          <span class="sel-string" v-html="scope.row.shipvagoo_product"></span>
        </template>
      </el-table-column>
      <el-table-column
          label="Weight To"
          prop="weight_class_to"
          min-width="80"
          :fixed="!isMobile"
      >
        <template #default="scope">
          <span class="sel-string" v-html="scope.row.weight_to"></span>
        </template>
      </el-table-column>
      <el-table-column
          label="Weight From"
          prop="weight_class_from"
          :fixed="!isMobile"
          min-width="95"
      >
        <template #default="scope">
          <span class="sel-string" v-html="scope.row.weight_from"></span>
        </template>
      </el-table-column>
      <el-table-column
          label="Min.Sv List Price excl. VAT"
          prop="cost_price_plus_markup"
          min-width="100"
      >
        <template #default="scope">
          <span class="sel-string" v-html="scope.row.min_ship_standard_list_price"></span>
        </template>
      </el-table-column>
      <el-table-column
          label="SV List Price excl. VAT"
          prop="ship_standard_list_price"
          min-width="110"
      >
        <template #default="scope">
          {{ scope.row.ship_standard_list_price }}
        </template>
      </el-table-column>
      <el-table-column label="Discount (DKK)" prop="discount" min-width="110">
        <template #default="scope">
          <div class="el-input" data-v-3dd33bdf="">
            <div
                :class="parseFloat(scope.row.gross_profit) >=  parseFloat(scope.row.cost_price_plus_markup) || scope.row.discount === 0
                                                                        ? 'el-input__wrapper'
                                                                        : 'el-input__wrapper-focus-danger'"
            >
              <input
                  v-on:blur="setDiscount(scope.row)"
                  type="number"
                  class="el-input__inner"
                  step="any"
                  v-model="scope.row.discount"
                  min="0"
              />
            </div>
          </div>

          <!-- ></el-input> -->
        </template>
      </el-table-column>

      <el-table-column
          label="Customer Price excl. VAT"
          prop="gross_profit"
          min-width="120"
      >
        <template #default="scope">
                                                        <span
                                                            class="sel-string"
                                                            v-html="
                                                                (scope.row.ship_standard_list_price - scope.row.discount)
                                                            "
                                                        >
                                                        </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="table-footer">
      Group Discount (DKK)
      <el-input
          class="discount-percent-input"
          type="number"
          v-model="golobalDiscount"
          step="any"
          min="0"
          value="0"
      ></el-input>
      <el-button class="btn-blue-bg"
                 @click="applyGlobalDiscount(lists)"
      >Apply
      </el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "UpdateTableList",
  props: ['lists','option'],
  setup(props) {
    return {
      lists: props.lists,
      country:props.option
    }
  },
  data() {
    return {
      golobalDiscount: 0,
      itemsChecked: [],
      latestPrices: []
    }
  },
  created() {
    this.updatedAction(false,{});
  },
  computed: {
    selectedItems() {
      return this.itemsChecked.length || 0
    },
    loadDiscount() {
      return this.golobalDiscount
    }
  },
  watch: {},
  methods: {
    setDiscount(row) {
      const gross = row.ship_standard_list_price - row.discount
      console.log(gross, 'gross')
      // const cst = row.cost_price_plus_markup - gross
      this.updatedAction(true, row);
      return (row.discount = gross >= row.min_ship_standard_list_price ? row.discount : 0)

    },
    handleSelectionChange(val) {
      this.itemsChecked = val
    },
    applyGlobalDiscount(records) {
      // if(this.golobalDiscount > 0)
      // {
      if (this.itemsChecked.length > 0) {
        return records.forEach((value, index) => {
          this.itemsChecked.forEach((val, idx) => {
            if (value['ubsend_price_group_id'] === val['ubsend_price_group_id'])
              value["discount"] = this.loadDiscount
          })
        })
      }
      let groups = [];

      this.updatedAction(false,{});
      //this.$emit('')
      return records.forEach((value, index) => {

        value["discount"] = Number(this.loadDiscount)
      })
      // }
    },
    updatedGroup(groups, shipvagoo_pricing_id,price_group_id) {
      let j = -1;
      groups.map((row, i) => {
        if (row.shipvagoo_pricing_id === shipvagoo_pricing_id && row.price_group_id === price_group_id) {
          j = i
        }
      })
      return j
    },
    updatedAction(flag, row = {}) {
      if (flag) {
        let j = this.updatedGroup(this.latestPrices, row.shipvagoo_pricing_id,row.price_group_id)
        console.log(j, 'JJ')
        if (j !== -1) {
          this.latestPrices[j].discount = Number(row.discount)
          this.latestPrices[j].price_group_id = row.price_group_id
          this.latestPrices[j].shipvagoo_pricing_id = row.shipvagoo_pricing_id
          this.latestPrices[j].country = row.country
        } else {
          this.latestPrices.push({
            price_group_id: row.price_group_id,
            shipvagoo_pricing_id: row.shipvagoo_pricing_id,
            discount: Number(row.discount),
            country:this.country
          })
        }
      } else {
        this.lists.map((row, i) => {
          let j = this.updatedGroup(this.latestPrices, row.shipvagoo_pricing_id,row.price_group_id)
          if (j !== -1) {
            this.latestPrices[j].discount = Number(this.loadDiscount)
          } else {
            this.latestPrices.push({
              price_group_id: row.price_group_id,
              shipvagoo_pricing_id: row.shipvagoo_pricing_id,
              discount: Number(this.loadDiscount),
              country:this.country
            });
          }
        })
      }
      this.$emit('latestData', this.latestPrices)
    }
  }
}
</script>

<style scoped>

</style>