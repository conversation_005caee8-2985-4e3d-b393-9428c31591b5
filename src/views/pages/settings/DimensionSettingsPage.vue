<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Dimension Settings</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Dimensions Settings
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="flex">
      <div class="col-10"></div>
      <div class="col-2 flex justify-flex-end">
        <el-button class="btn-blue-bg" @click="onClickAdd">Add</el-button>
      </div>
    </div>
    <div class="card-base card-shadow--medium p-15 mt-2">
      <el-table
          :data="dimensions"
          style="width: 100%"
          height="auto"
      >
        <el-table-column label="ID" min-width="60" prop="id" fixed>
          <template #default="scope">
            <span class="sel-string">{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Carrier" prop="carrier" min-width="90">
          <template #default="scope">
            {{ scope.row.value.carrier?.name }}
          </template>
        </el-table-column>
        <el-table-column label="Carrier Product" prop="carrier_product" min-width="110">
          <template #default="scope">
            {{ scope.row.value.carrier_product?.name }}
          </template>
        </el-table-column>
        <el-table-column label="Length" prop="length" min-width="75">
          <template #default="scope">
            {{ scope.row.value.length }}
          </template>
        </el-table-column>

        <el-table-column label="Height" prop="height" min-width="75">
          <template #default="scope">
            {{ scope.row.value.height }}
          </template>
        </el-table-column>

        <el-table-column label="Width" prop="width" min-width="75">
          <template #default="scope">
            {{ scope.row.value.width }}
          </template>
        </el-table-column>

        <el-table-column label="Action">
          <template #default="scope">
            <el-dropdown trigger="hover">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                      @click="onClickEdit(scope.row)"
                      :command="'skip'"
                      divided
                  >
                    Edit
                  </el-dropdown-item>
                  <el-dropdown-item @click="onClickDeleteDimension(scope.$index,scope.row.id)"
                                    :command="'skip'" divided>
                    Delete
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>

      </el-table>
      <el-pagination
          @current-change="changeCurrentPage"
          @size-change="handleSizeChange"
          :small="false"
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          layout="total, ->, prev, pager, next, jumper"
          :total="pagination.total"
      ></el-pagination>
    </div>

    <el-dialog title="" v-model="addDimensionForm" class="p-0">

      <div class="card-base card-shadow&#45;&#45;medium search-card scrollable only-y mt-2 p-10">
        <el-form ref="form" class="mb-50 bb-br-gray" label-width="125px">
          <h4 class="ml-12">Add Dimension</h4>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Carrier">
                <el-select
                    @change="onChangeCarrier(carrier)"
                    v-model="carrier"
                    filterable
                    placeholder=""
                    :loading="loading"
                >
                  <el-option
                      v-for="item in carrierList"
                      :key="item.carrier_id_2"
                      :label="item.name"
                      :value="item.carrier_id_2"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Carrier Product">
                <el-select
                    v-model="carrier_product"
                    filterable
                    placeholder=""
                    :loading="carrierProductsLoading"
                >
                  <el-option
                      v-for="item in carrierProducts"
                      :key="item.product_id"
                      :label="item.name"
                      :value="item.product_id"
                  />
                </el-select>
              </el-form-item>
            </el-col>

          </el-col>

          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Length">
                <el-input type="number" min="0" v-model="length" placeholder=""></el-input>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Width">
                <el-input type="number" min="0" v-model="width" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Height">
                <el-input type="number" min="0" v-model="height" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>

          <div class="bt-br"></div>
          <el-form-item
              class="mt-30 dialog-footer">
            <el-button
                class="btn-blue-bg"
                @click="onClickSaveDimension"
                v-loading.fullscreen.lock="fullScreenLoading">
              Save
            </el-button>
            <el-button class="btn" @click="onClickCancel">Cancel</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onBeforeMount} from 'vue';
import {dimensionService, carrierService} from "../../../services/_singletons";
import {ElNotification} from "element-plus";
import {ElMessage, ElMessageBox} from 'element-plus'

const dimensions = ref([]);
const carrierList = ref([]);
const carrierProducts = ref([]);
const carrierProductsLoading = ref(false);
const pagination = ref({
  current_page: 1,
  total: 10,
  per_page: 100
})

/** these are form fields **/
const id = ref(null)
const carrier = ref('')
const carrier_product = ref('')
const length = ref(0)
const width = ref(0);
const height = ref(0);


const isEdit = ref(false);
const addDimensionForm = ref(false);
const fullScreenLoading = ref(false);

const changeCurrentPage = (page) => {
  console.log("Page ", page)
  pagination.value.current_page = page;
  console.log("Pagination ", JSON.stringify(pagination.value))
}
const handleSizeChange = (page) => {
  console.log("Page ", page)
  pagination.value.current_page = page;
  console.log("Pagination ", JSON.stringify(pagination.value))
}

const onClickCancel = () => {
  addDimensionForm.value = false;
  resetDimensionValidationForm();
}
const onClickAdd = () => {
  id.value = null;
  isEdit.value = false
  addDimensionForm.value = true;
}
const showPromptNotification = (msg, type = 'success') => {
  ElNotification[type]({
    title: type,
    message: msg,
  })
}
const onClickSaveDimension = async () => {
  if (!carrier.value) {
    showPromptNotification('Carrier is not selected.', 'error');
    return;
  } else if (!carrier_product.value) {
    showPromptNotification('Carrier product is not selected.', 'error');
    return;
  } else if (length.value < 0) {
    showPromptNotification('Invalid length values', 'error');
    return;
  } else if (height.value < 0) {
    showPromptNotification('Invalid height values', 'error');
    return;
  } else if (width.value < 0) {
    showPromptNotification('Invalid width values', 'error');
    return;
  }

  const selectedCarrier = carrierList.value.find((item) => item.carrier_id_2 == carrier.value);
  const selectedProduct = carrierProducts.value.find((item) => item.product_id == carrier_product.value);

  const data = {
    module: 'DIMENSION',
    id: id.value,
    key: `${carrier.value}-${carrier_product.value}`,
    value: {
      length: length.value,
      height: height.value,
      width: width.value,
      carrier: {
        carrier_id_2: carrier.value,
        name: selectedCarrier?.name,
        /*   id: selectedProduct?.id,
           carrier_code: selectedProduct?.carrier_code*/
      },
      carrier_product: {
        product_id: carrier_product.value,
        name: selectedProduct?.name,
        /* id: selectedProduct?.id,
         carrier_product_code: selectedProduct?.carrier_product_code*/
      }
    },
  }
  fullScreenLoading.value = true
  try {
    if (isEdit.value) {
      await dimensionService.updateDimension(data, id.value);
    } else {
      await dimensionService.createDimension(data);
    }
    ElNotification.success({
      title: 'Success',
      message: `Dimensions saved successfully.`,
    })
    await getDimensionsList();
    addDimensionForm.value = false;
    resetDimensionValidationForm()
  } catch (error) {
    console.log("Error  ", error)
    if (error.response.data) {
      ElNotification.error({
        title: 'Error',
        message: error.response.data.error[0],
      })
    }
  } finally {
    fullScreenLoading.value = false
  }
}
const resetDimensionValidationForm = () => {
  id.value = null;
  carrier.value = null;
  carrier_product.value = '';
  length.value = 0;
  width.value = 0
  height.value = 0;
}
const getDimensionsList = async () => {
  try {
    await dimensionService.getDimensionsList().then(response => {
      dimensions.value = response.data.dimensions.map((item) => {
        return {
          ...item,
          value: JSON.parse(item.value)
        }
      });
      pagination.value.total = dimensions.value.length;
    });
  } catch (e) {
    ElNotification.error({
      title: "Error",
      message: e.response.data.error[0],
      position: "top-right"
    })
  }
}

/** set edit attributes **/
const onClickEdit = (row) => {
  isEdit.value = true;
  id.value = row.id;
  carrier.value = row.value.carrier.carrier_id_2;
  carrier_product.value = row.value.carrier_product.product_id;
  length.value = row.value.length;
  height.value = row.value.height;
  width.value = row.value.width;
  addDimensionForm.value = true;
  onChangeCarrier(carrier.value, carrier_product.value);
}

const onClickDeleteDimension = (index, id) => {
  console.log("ID ", id)
  console.log("Index ", index)
  ElMessageBox.confirm(
      'Dimension will be deleted permanently. Continue?',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
  )
      .then(async () => {
        if (id) {
          await dimensionService.deleteDimension(id).then(response => {
            dimensions.value.splice(index, 1);
            ElMessage({
              type: 'success',
              message: 'Dimension deleted successfully!',
            })
          });
          await getDimensionsList();
        } else {
          dimensions.value.splice(index, 1);
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: 'Delete canceled',
        })
      })

  //
}

const getCarrierList = async () => {
  // loadingService.startLoading('main-loader:login');
  const res = await carrierService.getCarrierList();
  carrierList.value = res.data.carriers.filter((item) => item.is_active);
  if (carrier.value && carrier_product.value) {
    await onChangeCarrier(carrier.value, carrier_product.value)
  }

}
const onChangeCarrier = async (carrier_id_2, product_id = null) => {
  carrier_product.value = '';
  const id = carrierList.value.find((item) => item.carrier_id_2 == carrier_id_2)?.id;
  if (id) await getCarrierProducts(id, product_id);
}
const getCarrierProducts = async (id, product_id = null) => {
  carrierProductsLoading.value = true
  const res = await carrierService.getProductsByCarrier(id);
  carrierProducts.value = res.data.carrier_products;
  carrierProductsLoading.value = false;
  if (product_id) {
    carrier_product.value = product_id;
  }
}

onBeforeMount(() => {
  getDimensionsList()
  getCarrierList()
})
</script>
<style lang="scss">
.el-form-item__label:before {
  content: "" !important;
}
</style>