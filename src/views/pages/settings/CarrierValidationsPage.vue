<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Dimension Validation Settings</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Dimension Validations
            Settings
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="flex">
      <div class="col-10"></div>
      <div class="col-2 flex justify-flex-end">
        <el-button class="btn-blue-bg" @click="onClickAdd">Add</el-button>
      </div>
    </div>
    <div class="card-base card-shadow--medium p-15 mt-2">
      <el-table
          :data="carrierValidations"
          style="width: 100%"
          height="auto"
      >
        <el-table-column label="ID" min-width="35" prop="id" fixed>
          <template #default="scope">
            <span class="sel-string">{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Carrier" prop="carrier" min-width="80">
          <template #default="scope">
            {{ scope.row.value.carrier?.name }}
          </template>
        </el-table-column>
        <el-table-column label="Carrier Product" prop="carrier_product" min-width="100">
          <template #default="scope">
            {{ scope.row.value.carrier_product?.name }}
          </template>
        </el-table-column>
        <el-table-column label="Length" prop="length" min-width="100">
          <template #default="scope">
            {{ scope.row.value.length.min }}-{{ scope.row.value.length.max }}
          </template>
        </el-table-column>
        <el-table-column label="Height" prop="height" min-width="100">
          <template #default="scope">
            {{ scope.row.value.height.min }}-{{ scope.row.value.height.max }}
          </template>
        </el-table-column>
        <el-table-column label="Width" prop="width" min-width="100">
          <template #default="scope">
            {{ scope.row.value.width.min }}-{{ scope.row.value.width.max }}
          </template>
        </el-table-column>
        <el-table-column label="Action">
          <template #default="scope">
            <el-dropdown trigger="hover">
                            <span class="el-dropdown-link">
                                <i class="mdi mdi-dots-vertical"></i>
                            </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                      @click="onClickEdit(scope.row)"
                      :command="'skip'"
                      divided
                  >
                    Edit
                  </el-dropdown-item>
                  <el-dropdown-item @click="onClickDeleteCarrierValidation(scope.$index,scope.row.id)"
                                    :command="'skip'" divided>
                    Delete
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>

      </el-table>
      <el-pagination
          :small="false"
          v-model:current-page="pagination.current_page"
          :page-sizes="[10, 15, 20, 30, 50, 100]"
          v-model:page-size="pagination.per_page"
          layout="total, ->, prev, pager, next, jumper"
          :total="pagination.total"
      ></el-pagination>
    </div>

    <el-dialog title="" v-model="addCarrierValidationForm" class="p-0">

      <div class="card-base card-shadow&#45;&#45;medium search-card scrollable only-y mt-2 p-10">
        <el-form ref="form" class="mb-50 bb-br-gray" label-width="125px">
          <h4 class="ml-12">Add Dimension Validation</h4>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Carrier">
                <el-select
                    @change="onChangeCarrier(carrier)"
                    v-model="carrier"
                    filterable
                    placeholder=""
                    :loading="loading"
                >
                  <el-option
                      v-for="item in carrierList"
                      :key="item.carrier_code"
                      :label="item.name"
                      :value="item.carrier_id_2"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Carrier Product">
                <el-select
                    v-model="carrier_product"
                    filterable
                    placeholder=""
                    :loading="carrierProductsLoading"
                >
                  <el-option
                      v-for="item in carrierProducts"
                      :key="item.carrier_product_code"
                      :label="item.name"
                      :value="item.product_id"
                  />
                </el-select>
              </el-form-item>
            </el-col>

          </el-col>

          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Length Min">
                <el-input type="number" min="0" v-model.number="lengthMin" placeholder=""></el-input>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Length Max">
                <el-input type="number" min="0" v-model.number="lengthMax" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Width Min">
                <el-input type="number" min="0" v-model.number="widthMin" placeholder=""></el-input>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Width Max">
                <el-input type="number" min="0" v-model.number="widthMax" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>
          <el-col class="demo-form-inline flex" :span="24">
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Height Min">
                <el-input type="number" v-model.number="heightMin" min="0" placeholder=""></el-input>
              </el-form-item>
            </el-col>
            <el-col class="demo-form-inline" :span="12">
              <el-form-item label-width="160px" label="Height Max">
                <el-input type="number" v-model.number="heightMax" min="0" placeholder=""></el-input>
              </el-form-item>
            </el-col>

          </el-col>

          <div class="bt-br"></div>
          <el-form-item class="mt-30 dialog-footer">
            <el-button class="btn-blue-bg" @click="onClickSaveCarrierValidation"
                       v-loading.fullscreen.lock="fullScreenLoading">
              Save
            </el-button>
            <el-button class="btn" @click="onClickCancel">Cancel</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onBeforeMount, reactive} from 'vue';
import {carrierValidationService, carrierService} from "../../../services/_singletons";
import {ElNotification} from "element-plus";
import {ElMessage, ElMessageBox} from 'element-plus'

const carrierValidations = ref([]);
const carrierList = ref([]);
const carrierProducts = ref([]);
const carrierProductsLoading = ref(false);
const pagination = reactive({
  current_page: 1,
  total: 10,
  per_page: 100
})

/** these are form fields **/
const id = ref(null)
const carrier = ref('')
const carrier_product = ref('')
const lengthMin = ref(0)
const lengthMax = ref(0);
const heightMin = ref(0);
const heightMax = ref(0);
const widthMin = ref(0)
const widthMax = ref(0)


const isEdit = ref(false);
const addCarrierValidationForm = ref(false);
const fullScreenLoading = ref(false);
const onClickCancel = () => {
  addCarrierValidationForm.value = false
  resetDimensionValidationForm();
}
const onClickAdd = () => {
  id.value = null;
  isEdit.value = false
  addCarrierValidationForm.value = true;
}
const showPromptNotification = (msg, type = 'success') => {
  ElNotification[type]({
    title: type,
    message: msg,
  })
}
const onClickSaveCarrierValidation = async () => {
  if (!carrier.value) {
    showPromptNotification('Carrier is not selected.', 'error');
    return;
  } else if (!carrier_product.value) {
    showPromptNotification('Carrier product is not selected.', 'error');
    return;
  } else if ((lengthMin.value > lengthMax.value) || lengthMin.value < 0 || lengthMax.value < 0) {
    showPromptNotification('Invalid length min or max values', 'error');
    return;
  } else if (heightMin.value > heightMax.value || heightMin.value < 0 || heightMax.value < 0) {
    console.log("Height Min ", heightMin.value)
    console.log("Height Max ", heightMax.value)
    showPromptNotification('Invalid height min or max values', 'error');
    return;
  } else if ((widthMin.value > widthMax.value) || widthMin.value < 0 || widthMax.value < 0) {
    showPromptNotification('Invalid width min or max values', 'error');
    return;
  }

  const selectedCarrier = carrierList.value.find((item) => item.carrier_id_2 == carrier.value);
  const selectedProduct = carrierProducts.value.find((item) => item.product_id == carrier_product.value);

  const data = {
    module: 'CARRIER_VALIDATIONS',
    id: id.value,
    key: `${carrier.value}-${carrier_product.value}-carrier_validation`,
    value: JSON.stringify({
      length: {
        min: lengthMin.value,
        max: lengthMax.value,
      },
      height: {
        min: heightMin.value,
        max: heightMax.value,
      },
      width: {
        min: widthMin.value,
        max: widthMax.value,
      },
      carrier: {
        carrier_id_2: carrier.value,
        name: selectedCarrier?.name,
        /* id: selectedCarrier?.id,
         carrier_id: selectedCarrier?.carrier_id,*/
      },
      carrier_product: {
        product_id: carrier_product.value,
        name: selectedProduct?.name,
        /*  id: selectedProduct?.id,
          carrier_product_code: selectedProduct?.carrier_product_code*/
      }
    }),
  }
  fullScreenLoading.value = true
  try {
    if (isEdit.value) {
      await carrierValidationService.updateCarrierValidations(data, id.value);
    } else {
      await carrierValidationService.createCarrierValidations(data);
    }
    ElNotification.success({
      title: 'Success',
      message: `Dimension Validations saved successfully.`,
    })
    await getCarrierValidationsList();
    addCarrierValidationForm.value = false;
    resetDimensionValidationForm()
  } catch (error) {
    if (error.response.data) {
      ElNotification.error({
        title: 'Error',
        message: error.response.data.error[0],
      })
    }
  } finally {
    fullScreenLoading.value = false
  }
}
const resetDimensionValidationForm = () => {
  id.value = null;
  carrier.value = null;
  carrier_product.value = '';
  lengthMin.value = 0;
  lengthMax.value = 0
  heightMin.value = 0;
  heightMax.value = 0;
  widthMin.value = 0;
  widthMax.value = 0;
}
const getCarrierValidationsList = async () => {
  try {
    await carrierValidationService.getCarrierValidationsList().then(response => {
      carrierValidations.value = response.data.carrier_validations.map((item) => {
        return {
          ...item,
          value: JSON.parse(item.value)
        }
      });
      pagination.total = carrierValidations.value.length;
    });
  } catch (e) {
    ElNotification.error({
      title: "Error",
      message: e.response.data.error[0],
      position: "top-right"
    })
  }
}

/** set edit attributes **/
const onClickEdit = (row) => {
  isEdit.value = true;
  id.value = row.id;
  carrier.value = row.value.carrier.carrier_id_2;
  carrier_product.value = row.value.carrier_product.product_id;
  lengthMin.value = row.value.length.min;
  lengthMax.value = row.value.length.max;
  heightMin.value = row.value.height.min;
  heightMax.value = row.value.height.max;
  widthMin.value = row.value.width.min;
  widthMax.value = row.value.width.max;
  addCarrierValidationForm.value = true;
  onChangeCarrier(carrier.value, carrier_product.value);
}

const onClickDeleteCarrierValidation = (index, id) => {
  console.log("ID ", id)
  console.log("Index ", index)
  ElMessageBox.confirm(
      'Dimension Validation will be deleted permanently. Continue?',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
  )
      .then(async () => {
        if (id) {
          await carrierValidationService.deleteCarrierValidation(id).then(response => {
            carrierValidations.value.splice(index, 1);
            ElMessage({
              type: 'success',
              message: 'Dimension Validation deleted successfully!',
            })
          });
          await getCarrierValidationsList();
        } else {
          carrierValidations.value.splice(index, 1);
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: 'Delete canceled',
        })
      })

  //
}

const getCarrierList = async () => {
  // loadingService.startLoading('main-loader:login');
  const res = await carrierService.getCarrierList();
  carrierList.value = res.data.carriers.filter((item) => item.is_active);
  if (carrier.value && carrier_product.value) {
    await onChangeCarrier(carrier.value, carrier_product.value)
  }

}
const onChangeCarrier = async (carrier_id_2, product_id = null) => {
  carrier_product.value = '';
  const id = carrierList.value.find((item) => item.carrier_id_2 == carrier_id_2)?.id;
  if (id) await getCarrierProducts(id, product_id);
}
const getCarrierProducts = async (id, product_id = null) => {
  carrierProductsLoading.value = true
  const res = await carrierService.getProductsByCarrier(id);
  carrierProducts.value = res.data.carrier_products;
  carrierProductsLoading.value = false;
  if (product_id) {
    carrier_product.value = product_id;
  }
}

onBeforeMount(() => {
  getCarrierValidationsList()
  getCarrierList()
})
</script>
<style lang="scss">
.el-form-item__label:before {
  content: "" !important;
}
</style>