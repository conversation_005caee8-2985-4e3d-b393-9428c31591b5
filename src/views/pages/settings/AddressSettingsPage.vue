<template>
  <div class="scrollable only-y p-2">
    <div class="toolbar-box flex align-center">
      <div class="page-header">
        <h1 class="ml-2">Address Settings</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
          <el-breadcrumb-item>Main Menu</el-breadcrumb-item>
          <el-breadcrumb-item>Address Settings
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="card-base card-shadow--medium p-15">
      <div class="bb-bt-br">
        <div class="search-card scrollable only-y mt-2">
          <el-form autocomplete="off" ref="templateForm" label-width="120px">
            <h4 class="ml-12">Address Settings</h4>
            <div class="col-4 flex justify-flex-end">
              <el-button v-if="addresses && addresses.length==0" @click="onClickAddAddress()"
                         style="margin-top: 7px;"
                         type="primary"
                         size="medium">Add Address
              </el-button>
            </div>
            <template v-for="(address, index) in addresses" :key="index" style="margin-top: 15px;">
              <!-- <div style="font-weight: bold;margin-left: 15px;">{{address.key}}</div>-->
              <el-col

                  class="demo-form-inline flex mt-5" :span="24">
                <el-col
                    class="demo-form-inline" :span="4">
                  <el-form-item label="Carrier Name"
                                required
                                class="asterisk-right"
                                label-width="110px"
                  >
                    <!--<el-input
                            v-model="address.key"
                            placeholder=""></el-input>-->
                    <el-select
                        v-model="address.key"
                        filterable
                        placeholder=""
                    >
                      <el-option
                          v-for="(item,index) in carrierList"
                          :key="`${item.carrier_code}-${index}`"
                          :label="item.name"
                          :value="item.carrier_id_2"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                    class="demo-form-inline" :span="4">
                  <el-form-item label="Customer Name"
                                required
                                class="asterisk-right"
                                label-width="128px">
                    <el-input
                        v-model="address.value.customer_name"
                        placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col
                    class="demo-form-inline" :span="5">
                  <el-form-item label="Address"
                                required
                                class="asterisk-right"
                                label-width="85px"
                  >
                    <el-input
                        v-model="address.value.address"
                        placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col
                    class="demo-form-inline" :span="3">
                  <el-form-item label="Country"
                                required
                                class="asterisk-right"
                                label-width="75px"
                  >
                    <el-select
                        v-model="address.value.country"
                        filterable
                        autocomplete="off"
                        placeholder=""
                    >
                      <el-option
                          v-for="item in countries"
                          :key="item.country_code"
                          :label="item.name"
                          :value="item.iso"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                    class="demo-form-inline" :span="3">
                  <el-form-item label="City"
                                required
                                class="asterisk-right"
                                label-width="50px"
                  >
                    <el-input
                        v-model="address.value.city"
                        placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col
                    class="demo-form-inline" :span="3">
                  <el-form-item label="Zip Code"
                                required
                                class="asterisk-right"
                                label-width="85px"
                  >
                    <el-input
                        v-model="address.value.zip"
                        placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col
                    style="display:flex;justify-content: center;"
                    class="demo-form-inline ml-1" :span="2">
                  <i
                      title="Remove"
                      class="widget-icon mdi mdi-delete-circle-outline fs-23 cursor-pointer"
                      style="color: #b22234"
                      @click="onClickDeleteAddress(index,address.id)"
                  ></i>
                  <i
                      title="Add new"
                      v-if="index+1==addresses.length"
                      style="margin-left: 4px;"
                      class="widget-icon mdi mdi-plus-circle accent-text fs-23 cursor-pointer"
                      @click="onClickAddAddress"
                  ></i>
                  <div v-else style="width:29px"></div>
                </el-col>
              </el-col>
            </template>

            <el-form-item class="mt-30" label-width="100px">
              <el-button v-if="addresses && addresses.length" class="btn-blue-bg" @click="onClickSave">
                Save
              </el-button>
            </el-form-item>
          </el-form>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onBeforeMount} from 'vue';
import {addressService, carrierService, merchantService} from "../../../services/_singletons";
import {ElNotification} from "element-plus";
import {ElMessage, ElMessageBox} from 'element-plus'

const addresses = ref([]);
const carrierList = ref([]);
const countries = ref([]);
const getAddressesList = async () => {
  try {
    await addressService.getAddressesList().then(response => {
      addresses.value = response.data.addresses.map((item) => {
        return {
          ...item,
          value: JSON.parse(item.value)
        }
      });
    });
  } catch (e) {
    ElNotification.error({
      title: "Error",
      message: e.response.data.error[0],
      position: "top-right"
    })
  }
}
const isValidated = () => {
  let valid = true;
  addresses.value.forEach((item) => {
    if (item.key == null || item.key == "" || item.value.customer_name == null || item.value.customer_name == "" || item.value.address == null || item.value.address == "" || item.value.zip == null || item.value.zip == "" || item.value.city == null || item.value.city == "") {
      valid = false;
    }
  })
  const uniqueValues = new Set(addresses.value.map(v => v.key));
  if (uniqueValues.size < addresses.value.length) {
    console.log("duplicated...")
    ElNotification.error({
      title: 'Error',
      message: "Duplicates records entered against carriers.",
    })
    valid = false;
  } else if (!valid) {
    ElNotification.error({
      title: 'Error',
      message: "Some fields are not entered or still empty!",
    })
  }
  return valid;
}
const onClickSave = async () => {
  if (!isValidated()) {
    return;
  }
  const formData = new FormData();
  addresses.value.forEach((item, index) => {
    formData.append(`settings[${index}][module]`, 'ADDRESS');
    formData.append(`settings[${index}][key]`, item.key);
    formData.append(`settings[${index}][value]`, JSON.stringify(item.value));
  })
  await addressService.updateAddresses(formData).then(response => {
    ElNotification.success({
      title: 'Success',
      message: `Addresses saved successfully.`,
    })
  }).catch(error => {
    if (error.response.data) {
      ElNotification.error({
        title: 'Error',
        message: error.response.data.error[0],
      })
    }
  })
}
const onClickDeleteAddress = (index, id) => {
  ElMessageBox.confirm(
      'Address will be deleted permanently. Continue?',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
  )
      .then(async () => {
        if (id) {
          await addressService.deleteAddress(id).then(response => {
            addresses.value.splice(index, 1);
            ElMessage({
              type: 'success',
              message: 'Address deleted successfully!',
            })
          });
        } else {
          addresses.value.splice(index, 1);
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: 'Delete canceled',
        })
      })

  //
}
const onClickAddAddress = (index) => {
  addresses.value.push({id: null, module: 'ADDRESS', value: {customer_name: '', address: '', zip: ''}});
}
const getCarrierList = async () => {
  const res = await carrierService.getCarrierList();
  carrierList.value = res.data.carriers.filter((item) => item.is_active);
}
const getCountries = async () => {
  try {
    const res = await merchantService.getCountryList()
    countries.value = res.data.countries
  } catch (e) {
  }
}
onBeforeMount(async () => {
  await getCountries();
  await getAddressesList()
  await getCarrierList();
})
</script>
<style lang="scss">
.el-form-item__label:before {
  content: "" !important;
}
</style>