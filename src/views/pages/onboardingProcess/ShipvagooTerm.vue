<template>
    <div>
        <h1 style="text-align: center !important;">Terms and condition</h1>
        <el-row>
            <el-col class="demo-form-inline" :span="24">
                <iframe src="https://shipvagoo.com/vilkaar-og-betingelser/"
                        width="100%" height="400px"
                        frameBorder="0"></iframe>
            </el-col>
        </el-row>
        <el-row>
            <el-col style="text-align: center" class="demo-form-inline" :span="24">
                <el-checkbox v-model="termAndCondtion"
                             size="large">
                    I accept the <a target="_blank" href=http://shipvagoo.com/virksomhed/vilkaar-og-betingelser> terms
                    and
                    conditions</a>
                </el-checkbox>
            </el-col>
        </el-row>
        <el-row>
            <el-col style="text-align: center" class="demo-form-inline" :span="24">
                <el-button :disabled="!termAndCondtion" type="primary" style="margin-top: 12px;" @click="next">Next
                </el-button>
                <el-button type="info" style="margin-top: 12px;" @click="back">Decline</el-button>
            </el-col>
        </el-row>

    </div>
</template>

<script>
    export default {
        name: "ShipvagooTerm",
        props: ['active'],
        data() {
            return {
                termAndCondtion: false
            }
        },
        setup(props) {
            return {
                activeTab: props.active
            }
        },
        methods: {
            next() {
                const nextActive = this.activeTab + 1
                this.$emit('activeTab', {
                    nextActive: nextActive,
                    tab: 'terms',
                    data: {
                        termAndCondtion: this.termAndCondtion
                    }
                })
            },
            back() {
                const nextActive = this.activeTab - 1
                this.$emit('activeTab', {
                    nextActive: nextActive,
                    tab: 'terms',
                    data: {}
                })
            }
        }
    }
</script>
