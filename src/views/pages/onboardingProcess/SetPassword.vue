<template>
  <div>
    <h1 style="text-align: center !important;">Set Password</h1>
    <div class="bb-bt-br">
      <div class="search-card scrollable only-y mt-2">
        <el-card>
          <Form class="form-box" :validation-schema="schema">
            <el-row>
              <el-col class="demo-form-inline mr-10" :span="24">
                <el-form-item class="form-label" label="Password">
                  <el-input @input="passwordMatchHandle"
                            name="password"
                            type="password" placeholder="Password" v-model="password"/>
                  <small v-if="!validated" style="color:red">Password must be at least 8 characters</small>
                </el-form-item>

              </el-col>
            </el-row>
            <el-row>
              <el-col class="demo-form-inline" :span="24">
                <el-form-item class="form-label" label="Confirm Password">
                  <el-input @input="passwordMatchHandle"
                            name="passwordConfirmation"
                            type="password" placeholder="Confirm Password" v-model="confirmPassword"/>
                  <small v-if="!is_confirm_matched" style="color:red">Password and confirm password does not
                    match.</small>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col class="demo-form-inline" :span="24">
                <el-button :disabled="!isMatch" type="primary" style="margin-top: 12px;margin-left: 200px"
                           @click="next">Next
                </el-button>
              </el-col>
            </el-row>
            <!-- </el-form> -->
          </Form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import {useMainStore} from "@/stores/main";
import * as Yup from "yup";

const schema = Yup.object().shape({
  password: Yup.string()
      .min(8, 'Password must be at least 8 characters')
      .required('Password is required'),
  passwordConfirmation: Yup.string()
      .oneOf([Yup.ref('password'), null], 'Passwords must match')
});
export default {
  name: "SetPassword",
  props: ['active'],
  data() {
    return {
      password: '',
      confirmPassword: '',
      isMatch: false,
      schema,
      validated: false,
      is_confirm_matched: true,
    }
  },
  setup(props) {
    return {
      activeTab: props.active
    }
  },
  methods: {
    passwordMatchHandle() {
      if (this.password === this.confirmPassword && this.password != '' && this.password.length >= 8) {
        this.isMatch = true
      } else {
        this.isMatch = false
      }
      if (this.password.length < 8) {
        this.validated = false;
      } else {
        this.validated = true;
      }
      if (this.password.length <= this.confirmPassword.length) {
        if (this.password !== this.confirmPassword) {
          this.is_confirm_matched = false;
        } else {
          this.is_confirm_matched = true;
        }
      }
    },
    next() {
      useMainStore().setPassword(this.password);
      useMainStore().setConfirmPassword(this.confirmPassword);
      const nextActive = this.activeTab + 1
      this.$emit('activeTab', {
        nextActive: nextActive,
        tab: 'setPassword',
        data: {
          password: this.password,
          confirmPassword: this.confirmPassword
        }
      })
    }
  }
}
</script>

<style scoped>

</style>