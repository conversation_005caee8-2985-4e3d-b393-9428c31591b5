<template>
  <div>
    <h1 style="text-align: center !important;color:#313131">API Integration</h1>
    <div class="bb-bt-br">
      <div class="search-card scrollable only-y mt-2">
        <div class="full_width ml-5" style="height: 1px;background-color: #DDDDDD"></div>
        <Form class="form-box mb-30">
          <div class="ml-12 mt-20" style="color:#313131;font-size: 16px;font-weight:600;">API Integration
          </div>
          <el-row class="mt-25">
            <el-col :span="12">
              <el-form-item class="api-integration-form-item"
                            style="margin-left: 15px; width: 93%" label="Platform">
                <el-select
                    size="large"
                    v-model="platformId" style="width: 100%">
                  <el-option v-for="platform in platforms" :label="platform.name"
                             :value="platform.platform_code"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="display:flex;justify-content: end">
              <el-form-item
                  class="api-integration-form-item"
                  style="margin-left: 15px;width: 93%" label="Shop Url">
                <el-input
                    size="large"
                    placeholder="Enter Shop Url" type="text"
                    v-model="apiUrl"/>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row :span="24">
               <el-col :span="12">
                   <el-form-item class="form-label" label="API Key">
                       <el-input placeholder="Enter API Key" type="text" v-model="apiUserName"/>
                   </el-form-item>
               </el-col>
           </el-row>
           <el-row :span="24">
               <el-col :span="12">
                   <el-form-item class="form-label" label="Admin API Access Token">
                       <el-input placeholder="Enter Admin API Access Token" type="text" v-model="accessToken"/>
                   </el-form-item>
               </el-col>
               <el-col :span="12">
                   <el-form-item class="form-label" label="API Secret Key">
                       <el-input placeholder="Enter API Secret key" type="text" v-model="apiSecret"/>
                   </el-form-item>
               </el-col>
           </el-row>-->
          <el-row>
            <el-col :span="24">
              <el-button style="margin-top: 12px;margin-left: 90px;color:white;background-color: #243E90"
                         :disabled="install_disabled"
                         @click="onSubmitNext()">Install
              </el-button>
              <el-button style="margin-top: 12px;" @click="onSubmit('skip') ">Skip
              </el-button>
            </el-col>
          </el-row>
        </Form>
        <div class="full_width ml-5" style="height: 1px;background-color: #DDDDDD"></div>
        <div class="innerBox ml-5">
          <strong style="font-size: 15px;color: black;">Connect your webshop to automatically import orders
            and
            manage shipments on Shipvagoo.</strong>
          <div class="mt-5 ml-10" style="font-size: 15px;color: #232323;">
            <strong>
              Please download our guidelines on how to connect your Shopify store with the Shipvagoo app.<br/> <a
                style="color: black"
                target="_blank"
                href="https://shipvagoo.com/wp-content/uploads/Shipvagoo-Shopify-integration-App-onboading-process-DK.pdf">
              Dansk
            </a> /
              <a style="color: black" target="_blank"
                 href="https://shipvagoo.com/wp-content/uploads/Shipvagoo-Shopify-integration-App-onboading-process-EN.pdf">
                Engelsk
              </a>
            </strong>
            <!--            <strong style="color:#131313">Step 1:</strong><span class="instruction-span">Choose your e-commerce platform from the dropdown menu Step</span>
                        <br/><strong style="color:#131313">Step 2:</strong><span
                          class="instruction-span">Enter your e-commerce store URL</span>
                        <br/><strong style="color:#131313">Step 3:</strong><span
                          class="instruction-span">Click "Install" and Shipvagoo will establish a connection with your e-commerce store.</span>-->
          </div>

        </div>
        <div class="full_width ml-5 mt-30" style="height: 1px;background-color: #DDDDDD"></div>
        <!-- <el-collapse value="index" class="collapse-parent">
             <el-collapse-item title="Instructions" name="index" class="collapse-parent-item font-size-18">
                 <div class="innerBox">
                     <h4 style="color:#131313;padding:0;margin:0">Guideline:</h4>
                     <h4 style="color:#131313;padding:0;margin:0">Please click here to see the guideline
                         <a
                                 target="_blank"
                                 href="http://shipvagoo.aveo06.dk/virksomhed/vilkaar-og-betingelser"
                                 style="color:#131313;text-decoration: underline">Dansk</a> / <a
                                 target="_blank"
                                 href="http://shipvagoo.aveo06.dk/virksomhed/vilkaar-og-betingelser"
                                 style="color:#131313;text-decoration: underline">English</a>
                     </h4>
                     &lt;!&ndash; <h4>Guideline Video</h4>
                      <div class="videoBox">
                          <iframe width="560" height="315"
                                  src="https://www.youtube-nocookie.com/embed/WEniS1dYejA"
                                  title="YouTube video player" frameborder="0"
                                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                  allowfullscreen></iframe>
                      </div>&ndash;&gt;
                 </div>
             </el-collapse-item>
         </el-collapse>-->
      </div>
    </div>
  </div>
</template>

<script>
import {loadingService, merchantService} from "@/services/_singletons";
import * as Yup from "yup";
import {useMainStore} from "@/stores/main";

export default {
  name: "ApiIntegration",
  props: ['active'],
  created() {
    this.getPlatformList();
  },
  data() {
    return {
      platforms: [],
      platformId: '',
      apiUrl: '',
      apiUserName: '',
      accessToken: '',
      apiSecret: '',
      install_disabled: false
    }
  },
  setup(props) {
    return {
      activeTab: props.active
    }
  },

  methods: {
    next() {
      const nextActive = this.activeTab + 1
      this.$emit('activeTab', {
        nextActive: nextActive,
        tab: 'apiIntegration',
        data: {}
      })
    },
    back() {
      const nextActive = this.activeTab - 1
      this.$emit('activeTab', {
        nextActive: nextActive,
        tab: 'terms',
        data: {}
      })
    },
    async getPlatformList() {
      // display form values on success
      try {
        // loadingService.startLoading('main-loader:login');
        const res = await merchantService.getPlatformList()
        this.platforms = res.data
      } catch (e) {
        if (e.response?.status === 422) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        }
        this.$notify.error({
          title: "Error",
          message: e.response.data.error[0],
          position: "top-right"
        })
        // loggingService.error('Error during login', e);
      } finally {
        loadingService.stopLoading("main-loader:login")
      }
    },
    async onSubmit(type = 'skip') {
      console.log('Company Code ', this.$route.query.company_code)
      // display form values on success
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const password = useMainStore().getPassword;
      const c_password = useMainStore().getConfirmPassword;
      let data = {
        password:password,
        c_password: c_password,
        otp: this.$route.params.merchantId.toString()
      }

      /* if (type === 'next') {
           data = {
               ...data,
               platform_code: this.platformId,
               api_url: this.apiUrl,
               api_secret_key: this.apiSecret,
               access_token: this.accessToken,
               api_username: this.apiUserName
           }
       }*/
      try {
        const res = await merchantService.postUpdatePassword(data)
        this.$notify({
          title: "Success",
          message: res.message,
          type: "success"
        })
        if (type == 'next') {
          let parser = document.createElement('a');
          parser.href = this.apiUrl;
          let url = "https://" + parser.host + "/admin/oauth/authorize?client_id=" + import.meta.env.VITE_clientId + "&scope=write_customers,read_customers,write_discounts,read_discounts,read_gdpr_data_request,write_gift_cards,read_gift_cards,write_inventory,read_inventory,write_orders,read_orders,write_payment_terms,read_payment_terms,write_price_rules,read_price_rules,write_product_listings,read_product_listings,write_products,read_products,write_reports,read_reports,write_shipping,read_shipping,write_assigned_fulfillment_orders,read_assigned_fulfillment_orders,write_fulfillments,read_fulfillments,write_third_party_fulfillment_orders,read_third_party_fulfillment_orders&redirect_uri=" + import.meta.env.VITE_shopify_callback_url + "&state=" + this.$route.query.company_code;
          this.install_disabled=true
          window.location.href = url;
        } else {
          window.location.href = import.meta.env.VITE_MERCHANT_URL
        }
      } catch (e) {
        loading.close();
        if (e.response?.status === 422 || e.response?.status === 400) {
          this.$notify.error({
            title: "Error",
            message: e.response.data.error[0],
            position: "top-right"
          })
          return
        } else {
          this.$notify.error({
            title: "Error",
            message: "Error while saving api integrations.",
            position: "top-right"
          })
        }
        // loggingService.error('Error during login', e);
      } finally {
        loading.close();
        loadingService.stopLoading("main-loader:login")
      }
    },
    onSubmitNext() {
      if (this.platformId && this.apiUrl) {
        this.onSubmit('next')
        return true;
      }

      this.errors = [];
      if (!this.apiUrl) {
        this.errors.push('API Shop Url required.');
      }
      /* if (!this.platformId) {
          this.errors.push('Platform required.');
      }

       if (!this.apiUserName) {
            this.errors.push('API User Name required.');
        }
        if (!this.accessToken) {
            this.errors.push('Access Token required.');
        }
        if (!this.apiSecret) {
            this.errors.push('API Secret required.');
        }*/
      this.$notify.error({
        title: "Error",
        message: this.errors[0],
        position: "top-right"
      })
      if (this.errors.length > 0) {
        console.log("Error ...")
        return false;
      } else {
        this.onSubmit('next')
      }
    }
  }
}
</script>

<style scoped>
.innerBox {
  padding-top: 20px;
}

.videoBox iframe {
  display: block;
  width: 100%;
  height: 500px;
}

.instruction-span {
  color: #2f2f2f;
  margin-left: 7px;
  font-weight: 600;
}
</style>

<style>
.api-integration-form-item .el-form-item__label {
  margin-top: 4px !important;
  color: #313131
}
</style>