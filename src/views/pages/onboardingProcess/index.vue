<template>
  <div>
    <div class="formBox">
      <div class="container">
        <div class="center-text ">
          <el-steps :active="active" finish-status="success" align-center>
            <el-step title="Set Password"></el-step>
            <el-step title="Terms and condition"></el-step>
            <el-step title="API Integeration"></el-step>
          </el-steps>
          <div class="mt-15">
            <div v-if="active ===0" class="card-base card-shadow--medium p-15">
              <SetPassword :active="active" @activeTab="nextActiveHandle"/>
            </div>
            <div v-if="active ===1" class="card-base card-shadow--medium p-15">
              <ShipvagooTerm :active="active" @activeTab="nextActiveHandle"/>
            </div>
            <div v-if="active ===2" class="card-base card-shadow--medium p-15">
              <ApiIntegration :active="active" @activeTab="nextActiveHandle"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import users from "@/assets/data/USERS_MOCK_DATA.json"
import _ from "lodash"
import dayjs from "dayjs"
import {merchantService, agreementService, templateService, loadingService} from "../../../services/_singletons"
import {useMainStore} from "@/stores/main"
import {defineComponent} from "@vue/runtime-core"
import {Form, Field, ErrorMessage} from "vee-validate"
import * as Yup from "yup"
import SetPassword from "@/views/pages/onboardingProcess/SetPassword.vue";
import ShipvagooTerm from "@/views/pages/onboardingProcess/ShipvagooTerm.vue";
import ApiIntegration from "@/views/pages/onboardingProcess/ApiIntegration.vue";

const schema = Yup.object().shape({
  account_name: Yup.string().required("Account Name is required"),
  password: Yup.string().min(6, "Password must be at least 8 characters").required("Password is required"),
  client_id: Yup.number().required("ClientID should be greater then zero or minimum three digit").test('len', 'ClientID shold be greater then zero or minimum three digit', val => val.toString().length >= 3),
  username: Yup.string().required("Username is required").email()
})

export default defineComponent({
  name: "AddCustomAgreementPage",
  data() {
    return {
      schema,
      active: 0
    }
  },
  methods: {

    nextActiveHandle(nextTab) {
      if (this.active > 2) {
        this.active = 0;
      } else {
        this.active = nextTab.nextActive
      }
      this.$forceUpdate()
    }
  },
  created() {
  },
  mounted() {
    //ie fix
    if (!window.Number.parseInt) window.Number.parseInt = parseInt
  },
  components: {
    ApiIntegration,
    ShipvagooTerm,
    SetPassword,
    Form,
    Field,
    ErrorMessage
  }
})
</script>
<!--<style>
@import "vue-select/dist/vue-select.css";
</style>-->
<style lang="scss" scoped>

.page-table {
  &.overflow {
    overflow: auto;
  }

  .toolbar-box {
    &.hidden {
      visibility: hidden;
    }
  }

  .table-box {
    overflow: hidden;

    &.hidden {
      visibility: hidden;
    }
  }
}

.bt-br-gray {
  border-top: 1px solid #ebeef5;
}

.bb-br-gray {
  border-bottom: 1px solid #ebeef5;
}

.el-select {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  line-height: 32px;
  width: 100%;
}

.accent-text {
  color: #006efb;
}

.el-collapse-item .el-collapse-bg-gray {
  background: #151526 !important;
}

.el-collapse-item__header {
  background-color: #151526 !important;
  background: #151526 !important;
}

</style>

<style lang="scss">
@import "../../../assets/scss/_variables";

.page-table {
  padding: 20px;

  .toolbar-box {
    margin-bottom: 10px;
    margin-top: 0;
  }

  .clickable {
    cursor: pointer;
    text-decoration: underline;
    font-weight: bold;
  }

  .sel-string {
    .sel {
      background: transparentize($text-color-primary, 0.8);
      border-radius: 5px;
      //text-transform: uppercase;
    }
  }
}

@media (max-width: 768px) {
  .page-table {
    .toolbar-box {
      display: block;
      overflow: hidden;
      font-size: 80%;
      padding-bottom: 10px;

      & > * {
        display: inline-block;
        min-width: 120px;
        height: 22px;
        //background: rgba(0, 0, 0, 0.04);
        margin-bottom: 16px;
      }
    }
  }
}

.collapse-parent-item {
  .el-collapse-item__header {
    padding-left: 24px;
    padding-right: 17px;
    background-color: #151526;
    color: #fff;
  }

  .collapse-child-item {
    .el-collapse-item__header {
      background: #4a596a;
    }
  }
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  align-items: center;
  gap: 10px;

  .discount-percent-input {
    width: 70px;
  }
}

.cai-dialog-wrapper {
  &.el-dialog {
    margin-bottom: 120px !important;
  }
}

@media print {
  .cai-dialog-wrapper {
    &.el-dialog {
      width: 100% !important;
      margin-top: 0 !important;
    }
  }
  .download-pdf-button-wrapper {
    display: none;
  }
}

.form-label label {
  width: 200px;
  text-align: left;
}
.layout-container{
  background: black;
}
.formBox{
  position: relative;
  overflow: hidden;
  margin: 0 -30px;
  overflow-y: scroll;
  height: 100vh;
}


.formBox .el-step__icon{
  border: 2px solid #006efb;
  color: #006efb;
}
.formBox  .el-step__line{
  background: rgba(0,0,0,0.1);
}
.formBox .el-step__head.is-success .el-step__line i,
.formBox .el-step__head.is-success .el-step__line{
  background: #006efb;
  color: #006efb;
  border-color: #006efb;
}
.formBox .el-step__head.is-success .el-step__icon{
  background: #006efb;
  border-color: #006efb;
  color: #fff;
}
.formBox .el-step__title.is-success{
  color: #006efb;
}

.formBox .container{
  max-width: 1200px !important;
  margin: 0 auto;
  padding: 50px 15px;
}
</style>
