<template>
    <div>
        <div class="select-dashboard">
            <div class="flex m-2">
                <div class="col-6">
                    <el-select v-model="value" style="width: 400px;" class="management-selection">
                        <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div class="col-6 flex justify-flex-end">
                    <el-form class="mt-8">
                        <el-form-item label="From">
                            <el-col :span="11">
                                <el-form-item prop="startDate">
                                    <el-date-picker
                                            v-model="startDate"
                                            :clearable="false"
                                            type="date"
                                            @change="handleChangeStart"
                                            placeholder="From"
                                            format="DD-MM-YYYY"
                                            style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col class="text-center" :span="2">
                                <span class="text-gray-500">To</span>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item prop="endDate">
                                    <el-date-picker
                                            :clearable="false"
                                            v-model="endDate"
                                            type="date"
                                            @change="handleChange"
                                            placeholder="TO"
                                            format="DD-MM-YYYY"
                                            style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

        </div>

        <div class="dashboard-cards">
            <div class="flex mb-10">
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/stack.png" alt=""/>
                        </div>
                        <div class="text_box">
                            <span>Shipping Labels</span>
                            <h2>{{ danishNumberFormat(stats.shipment_labels) }}</h2>
                        </div>
                    </el-card>
                </div>
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/people-line.png" alt=""/>
                        </div>
                        <div class="text_box">
                            <span>No of Customers</span>
                            <h2>{{ danishNumberFormat(stats.customers_count) }}</h2>
                        </div>
                    </el-card>
                </div>
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/person.png" alt=""/>
                        </div>
                        <div class="text_box">
                            <span>No of New Customers (Last 7 days)</span>
                            <h2>{{ danishNumberFormat(stats.new_customers_count) }}</h2>
                        </div>
                    </el-card>
                </div>
            </div>
            <div class="flex">
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/bill.png" alt=""/>
                        </div>
                        <div class="text_box">
                            <span>Shipvagoo Earning</span>
                            <h2>DKK {{ danishNumberFormat(stats.shipvagoo_earning) }}</h2>
                        </div>
                    </el-card>
                </div>
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/money.png" alt=""/>
                        </div>
                        <div class="text_box">
                            <span>Shipvagoo Revenue</span>
                            <h2>DKK {{ danishNumberFormat(stats.shipvagoo_revenue) }}</h2>
                        </div>
                    </el-card>
                </div>
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/coin.png" alt=""/>
                        </div>
                        <div class="text_box">
                            <span>Total UBsend Cost</span>
                            <h2>DKK {{ danishNumberFormat(stats.total_ubsend_cost) }}</h2>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>
    </div>

    <!--    <div class="table-sec">
            <div class="flex">
                <div class="col-12">
                    <div class="flex flex-box">
                        <div class="label mr-8">Year</div>
                        <div class="table-select">
                            <el-select v-model="value" placeholder="2023">
                                <el-option
                                    v-for="item in options2"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <button class="btn">Export</button>
                    </div>
                </div>
            </div>
            <el-table :data="tableData" border style="width: 100%; margin-top: 20px">
                <el-table-column prop="title" width="300"> </el-table-column>
                <el-table-column prop="jan" label="Jan"> </el-table-column>
                <el-table-column prop="feb" label="Fab"> </el-table-column>
                <el-table-column prop="mar" label="Mar"> </el-table-column>
                <el-table-column prop="apr" label="Apr"> </el-table-column>
                <el-table-column prop="may" label="May"> </el-table-column>
                <el-table-column prop="jun" label="Jun"> </el-table-column>
                <el-table-column prop="jul" label="Jul"> </el-table-column>
                <el-table-column prop="aug" label="Aug"> </el-table-column>
                <el-table-column prop="sep" label="Sep"> </el-table-column>
                <el-table-column prop="oct" label="Oct"> </el-table-column>
                <el-table-column prop="nov" label="Nov"> </el-table-column>
                <el-table-column prop="dec" label="Dec"> </el-table-column>
            </el-table>
        </div>

        <div class="dashboard-cards">
            <div class="flex mb-12">
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/stack.png" alt="" />
                        </div>
                        <div class="text_box">
                            <span>Shipping Labels</span>
                            <h2>20,000</h2>
                        </div>
                    </el-card>
                </div>
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/people-line.png" alt="" />
                        </div>
                        <div class="text_box">
                            <span>No of Customers</span>
                            <h2>5,500</h2>
                        </div>
                    </el-card>
                </div>
                <div class="col-4">
                    <el-card class="card d-flex" :body-style="{ padding: '0px' }">
                        <div class="icon">
                            <img src="@/assets/images/person.png" alt="" />
                        </div>
                        <div class="text_box">
                            <span>No of New Customers</span>
                            <h2>1,000</h2>
                        </div>
                    </el-card>
                </div>
            </div>
            <div class="flex check-box">
                <el-checkbox v-model="checked">Not Started</el-checkbox>
                <el-checkbox>Draft</el-checkbox>
                <el-checkbox>Agreement Sent</el-checkbox>
                <el-checkbox>Completed</el-checkbox>
            </div>
        </div>-->
</template>


<script>
    //card js.....

    import {dashboardService, loadingService} from "@/services/_singletons";
    //import {useMainStore} from "@/stores/main";
    import {danishNumberFormat} from "@/helpers";
    import {defineComponent} from "@vue/runtime-core";
    import moment from "moment";

    const date = new Date();
    export default defineComponent({
        name: "DashboardList",
        data() {
            return {
                startDate: moment(new Date(date.getFullYear(), date.getMonth(), 1)).format('YYYY-MM-DD'),
                endDate: moment(new Date()).format('yyyy-MM-DD'),
                isMobile: false,
                options: [
                    {
                        value: 0,
                        label: "Management Dashboard"
                    },
                    {
                        value: 1,
                        label: "Financials Dashboard"
                    },
                    {
                        value: 2,
                        label: "Customer Service Dashboard"
                    },
                    {
                        value: 3,
                        label: "Onboarding Dashboard"
                    }
                ],
                value: 0,
                stats: {
                    shipment_labels: 0,
                    customers_count: 0,
                    new_customers_count: 0,
                    shipvagoo_earning: 0,
                    shipvagoo_revenue: 0,
                    total_ubsend_cost: 0
                }
            }
        },
        methods: {
            danishNumberFormat,
            handleChange(e) {
                this.getDashboardData();
            },
            handleChangeStart(e) {
                this.getDashboardData();
            },
            async getDashboardData() {
                // display form values on success
                this.startDate = moment(this.startDate).format('yyyy-MM-DD')
                this.endDate = moment(this.endDate).format('yyyy-MM-DD')
                const payload = [
                    {
                        key: 'start_date',
                        value: this.startDate
                    },
                    {
                        key: 'end_date',
                        value: this.endDate
                    }
                ]
                try {
                    loadingService.startLoading('main-loader:login');
                    const res = await dashboardService.getDashboardContent(payload)
                    this.stats = res.data
                } catch (e) {
                    if (e.response?.status === 422) {
                        this.$notify.error({
                            title: "Error",
                            message: e.response.data.error[0],
                            position: "top-right"
                        })
                        return
                    }
                    this.$notify.error({
                        title: "Error",
                        message: 'Something went wrong',
                        position: "top-right"
                    })
                    // loggingService.error('Error during login', e);
                } finally {
                    loadingService.stopLoading("main-loader:login")
                }
            },
        },
        mounted() {
            this.getDashboardData()
        },
    })
</script>
<style>
    .mb-12 {
        margin-bottom: 40px;
    }

    .dashboard-cards .el-checkbox__input.is-checked .el-checkbox__inner {
        background: rgba(0, 0, 0, 0.6);
        border-color: rgba(0, 0, 0, 0.6);
    }

    .dashboard-cards .el-checkbox .el-checkbox__label {
        color: rgba(0, 0, 0, 0.6) !important;
    }

    .main {
        flex-direction: column;
    }


    .dashboard-cards .check-box {
        justify-content: flex-end;
    }

    .select-dashboard:after {
        position: absolute;
        content: "";
        left: -100%;
        right: -100%;
        top: 0;
        bottom: 0;
        background: #f5f5f5;
        z-index: -1;
    }

    .select-dashboard .el-select:hover {
        border-color: transparent !important;
    }

    .management-selection .el-input__wrapper {
        box-shadow: none;
        outline: none;
        background: transparent;
        font-family: "Nunito Sans";
        font-style: normal;
        font-weight: 600;
        font-size: 24px;
        line-height: 33px;
        color: #313131;
    }

    .dashboard-cards .flex {
        gap: 10px;
    }

    .dashboard-cards .card {
        padding: 16px 24px;
        font-family: "Nunito Sans";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        color: #4a596a;
        box-shadow: 0px 4px 20px rgba(170, 169, 184, 0.25);
        border-radius: 5px;
    }

    .dashboard-cards .card .el-card__body {
        display: flex;
        gap: 16px;
    }

    .dashboard-cards .card .el-card__body .icon {
    }

    .dashboard-cards .card .el-card__body .icon img {
        display: block;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 5px;
        width: 16px;
        height: 16px;
        padding: 10px;
    }

    .dashboard-cards .card h2 {
        font-weight: 700;
        font-size: 24px;
        line-height: 33px;
        color: #313131;
        margin: 0;
    }

    .table-sec {
        overflow: auto;
    }

    .table-sec .el-table__row:nth-child(3n + 1) {
        font-weight: 700;
    }

    .table-sec .flex-box {
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    .table-sec .btn {
        background: #006efb;
        color: #fff;
        padding: 10px 20px;
        border: none;
        outline: none;
        border-radius: 5px;
        cursor: pointer;
    }

    .select-dashboard .el-select:hover:not(.el-select--disabled) .el-input__wrapper, .el-select .el-input.is-focus .el-input__wrapper {
        box-shadow: none !important;
    }

    .select-dashboard .el-input__inner {
        padding: 20px 0;
    }

    .select-dashboard {
        position: relative;
        height: 50px;
        align-items: center;
        z-index: 1;
        width: 100%;
        margin-bottom: 30px;
    }
</style>