<template>
    <div class="page-echarts scrollable ph-20">
        <div class="page-header">
            <h1>Echarts</h1>
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                <el-breadcrumb-item>Components</el-breadcrumb-item>
                <el-breadcrumb-item>Charts</el-breadcrumb-item>
                <el-breadcrumb-item>Echarts</el-breadcrumb-item>
            </el-breadcrumb>
        </div>

        <div class="card-base card-shadow--medium bg-white black-text pv-30 ph-20">
            <div id="chart" :style="{ height: '500px', width: '100%' }"></div>
        </div>

        <h4>
            <a href="https://github.com/apache/incubator-echarts" target="_blank">
                <i class="mdi mdi-link-variant"></i>reference
            </a>
        </h4>
    </div>
</template>

<script>
import * as echarts from "echarts"

import { defineComponent } from "@vue/runtime-core"

export default defineComponent({
    name: "EchartsPage",
    data() {
        return {
            chart: null
        }
    },
    mounted() {
        this.initChart()
        window.addEventListener("resize", this.__resizeHanlder)
    },
    beforeUnmount() {
        if (!this.chart) {
            return
        }
        window.removeEventListener("resize", this.__resizeHanlder)
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        __resizeHanlder() {
            if (this.chart) {
                this.chart.resize()
            }
        },
        initChart() {
            this.chart = echarts.init(document.getElementById("chart"))
            this.chart.setOption({
                //backgroundColor: '#394056',
                title: {
                    top: 20,
                    text: "Chart title",
                    fontWeight: "normal",
                    fontSize: 16 /*color: '#F1F1F3'*/,
                    left: "1%"
                },
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        lineStyle: {
                            /*color: '#57617B'*/
                        }
                    }
                },
                legend: {
                    top: 20,
                    icon: "rect",
                    itemWidth: 14,
                    itemHeight: 5,
                    itemGap: 13,
                    data: ["One", "Two", "Three"],
                    right: "4%",
                    textStyle: { fontSize: 12 /*color: '#F1F1F3'*/ }
                },
                grid: {
                    top: 100,
                    left: "3%",
                    right: "4%",
                    bottom: "2%",
                    containLabel: true
                },
                xAxis: [
                    {
                        type: "category",
                        boundaryGap: false,
                        axisLine: {
                            lineStyle: {
                                /*color: '#57617B'*/
                            }
                        },
                        data: [
                            "13:00",
                            "13:05",
                            "13:10",
                            "13:15",
                            "13:20",
                            "13:25",
                            "13:30",
                            "13:35",
                            "13:40",
                            "13:45",
                            "13:50",
                            "13:55"
                        ]
                    }
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "(%)",
                        axisTick: { show: false },
                        axisLine: {
                            lineStyle: {
                                /*color: '#57617B'*/
                            }
                        },
                        axisLabel: {
                            margin: 10,
                            fontSize: 14
                        },
                        splitLine: { lineStyle: { color: "#eee" /*color: '#57617B'*/ } }
                    }
                ],
                series: [
                    {
                        name: "One",
                        type: "line",
                        smooth: true,
                        symbol: "circle",
                        symbolSize: 5,
                        showSymbol: false,
                        lineStyle: { width: 1 },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(137, 189, 27, 0.3)"
                                    },
                                    {
                                        offset: 0.8,
                                        color: "rgba(137, 189, 27, 0)"
                                    }
                                ],
                                false
                            ),
                            shadowColor: "rgba(0, 0, 0, 0.1)",
                            shadowBlur: 10
                        },
                        itemStyle: {
                            color: "rgb(137,189,27)",
                            borderColor: "rgba(137,189,2,0.27)",
                            borderWidth: 12
                        },
                        data: [220, 182, 191, 134, 150, 120, 110, 125, 145, 122, 165, 122]
                    },
                    {
                        name: "Two",
                        type: "line",
                        smooth: true,
                        symbol: "circle",
                        symbolSize: 5,
                        showSymbol: false,
                        lineStyle: { width: 1 },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(0, 136, 212, 0.3)"
                                    },
                                    {
                                        offset: 0.8,
                                        color: "rgba(0, 136, 212, 0)"
                                    }
                                ],
                                false
                            ),
                            shadowColor: "rgba(0, 0, 0, 0.1)",
                            shadowBlur: 10
                        },
                        itemStyle: {
                            color: "rgb(0,136,212)",
                            borderColor: "rgba(0,136,212,0.2)",
                            borderWidth: 12
                        },
                        data: [120, 110, 125, 145, 122, 165, 122, 220, 182, 191, 134, 150]
                    },
                    {
                        name: "Three",
                        type: "line",
                        smooth: true,
                        symbol: "circle",
                        symbolSize: 5,
                        showSymbol: false,
                        lineStyle: { width: 1 },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(219, 50, 51, 0.3)"
                                    },
                                    {
                                        offset: 0.8,
                                        color: "rgba(219, 50, 51, 0)"
                                    }
                                ],
                                false
                            ),
                            shadowColor: "rgba(0, 0, 0, 0.1)",
                            shadowBlur: 10
                        },
                        itemStyle: {
                            color: "rgb(219,50,51)",
                            borderColor: "rgba(219,50,51,0.2)",
                            borderWidth: 12
                        },
                        data: [220, 182, 125, 145, 122, 191, 134, 150, 120, 110, 165, 122]
                    }
                ]
            })
        }
    }
})
</script>

<style lang="scss"></style>
