<template>
    <div class="page-table scrollable only-y" id="affix-container">
        <div class="page-header">
            <h1>Table</h1>
            <h4>simple table</h4>
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }"><i class="mdi mdi-home-outline"></i></el-breadcrumb-item>
                <el-breadcrumb-item>Components</el-breadcrumb-item>
                <el-breadcrumb-item>Tables</el-breadcrumb-item>
                <el-breadcrumb-item>Table</el-breadcrumb-item>
            </el-breadcrumb>
        </div>

        <div class="table-box card-base card-shadow--medium scrollable only-x">
            <table class="styled striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>First name</th>
                        <th>Last name</th>
                        <th>Email</th>
                        <th>Gender</th>
                        <th>IP address</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in list" :key="item.id">
                        <td>{{ item.id }}</td>
                        <td>{{ item.first_name }}</td>
                        <td>{{ item.last_name }}</td>
                        <td>{{ item.email }}</td>
                        <td>{{ item.gender }}</td>
                        <td>{{ item.ip_address }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
import Affix from "@/components/Affix.vue"

import { defineComponent } from "@vue/runtime-core"

export default defineComponent({
    name: "Table",
    data() {
        return {
            affixEnabled: true,
            list: [
                {
                    id: 1,
                    first_name: "Fidela",
                    last_name: "MacLaverty",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "*************"
                },
                {
                    id: 2,
                    first_name: "Garrard",
                    last_name: "Inge",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "*************"
                },
                {
                    id: 3,
                    first_name: "Clayborn",
                    last_name: "Blencoe",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "***************"
                },
                {
                    id: 4,
                    first_name: "Reinaldos",
                    last_name: "Briiginshaw",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "*************"
                },
                {
                    id: 5,
                    first_name: "Abigael",
                    last_name: "Richmond",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "**************"
                },
                {
                    id: 6,
                    first_name: "Elna",
                    last_name: "Deboick",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "**************"
                },
                {
                    id: 7,
                    first_name: "Lanna",
                    last_name: "Prentice",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "*************"
                },
                {
                    id: 8,
                    first_name: "Sheffie",
                    last_name: "Fellgett",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "**************"
                },
                {
                    id: 9,
                    first_name: "Mamie",
                    last_name: "Calkin",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "***********"
                },
                {
                    id: 10,
                    first_name: "Saudra",
                    last_name: "Dunniom",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "***************"
                },
                {
                    id: 11,
                    first_name: "Sile",
                    last_name: "Rudeforth",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "*************"
                },
                {
                    id: 12,
                    first_name: "Nichole",
                    last_name: "Verrell",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "*************"
                },
                {
                    id: 13,
                    first_name: "Portie",
                    last_name: "Sporle",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "************"
                },
                {
                    id: 14,
                    first_name: "Cosme",
                    last_name: "Pauling",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "**************"
                },
                {
                    id: 15,
                    first_name: "Elisha",
                    last_name: "Tipperton",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "*************"
                },
                {
                    id: 16,
                    first_name: "Skyler",
                    last_name: "Schwandermann",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "***************"
                },
                {
                    id: 17,
                    first_name: "Anallise",
                    last_name: "Tuftin",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "************"
                },
                {
                    id: 18,
                    first_name: "Janka",
                    last_name: "Hissie",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "**************"
                },
                {
                    id: 19,
                    first_name: "Tonie",
                    last_name: "Swede",
                    email: "<EMAIL>",
                    gender: "Female",
                    ip_address: "***************"
                },
                {
                    id: 20,
                    first_name: "Marve",
                    last_name: "Egle",
                    email: "<EMAIL>",
                    gender: "Male",
                    ip_address: "***************"
                }
            ]
        }
    },
    components: {
        Affix
    }
})
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/_variables";

.page-table {
    padding-left: 20px;
    padding-right: 15px;
    padding-bottom: 20px;
}
.table-box {
    overflow: auto;
}
</style>
