<template>
    <el-menu
        v-model:default-active="activeLink"
        :mode="mode"
        @select="goto"
        :collapse="isCollapse"
        :unique-opened="true"
        background-color="transparent"
        class="main-navigation-menu"
        :class="{ 'nav-collapsed': isCollapse }"
    >
        <div v-if="is_assigned('dashboard-view')" class="el-menu-item-group__title" style="padding-top: 4px">
            <span>My Work</span>
        </div>
        <el-menu-item index="/dashboard/list" v-if="is_assigned('dashboard-view')">
            <i class="mdi mdi-gauge"></i><span slot="title">Dashboard</span>
        </el-menu-item>

        <!--    <el-menu-item index="">
          <i class="mdi mdi-clipboard-check-outline"></i><span slot="title">Activities</span>
        </el-menu-item>-->
        <div v-if="is_assigned('customer-list')" class="el-menu-item-group__title" style="padding-top: 4px">
            <span>Account</span>
        </div>
        <el-menu-item v-if="is_assigned('customer-list')" index="/customer/customer-list">
            <i class="mdi mdi-apps"></i><span slot="title">Customer</span>
        </el-menu-item>
        <div class="el-menu-item-group__title" style="padding-top: 4px"><span>Credit</span></div>
        <el-menu-item index="/credit-records">
            <i class="mdi mdi-credit-card"></i><span slot="title">Credit Record</span>
        </el-menu-item>
        <div class="el-menu-item-group__title" style="padding-top: 4px">
            <span>Report</span>
        </div>
        <el-menu-item index="/error/logs">
            <i class="mdi mdi-math-log"></i><span slot="title">Error Logs</span>
        </el-menu-item>
        <!--    <el-menu-item index="">
          <i class="mdi mdi-apps"></i><span slot="title">Contacts</span>
        </el-menu-item>
        <el-menu-item index="">
          <i class="mdi mdi-apps"></i><span slot="title">Agreement</span>
        </el-menu-item>
        <div class="el-menu-item-group__title" style="padding-top: 4px"><span>Sales</span></div>
        <el-menu-item index="">
          <i class="mdi mdi-apps"></i><span slot="title">Leads</span>
        </el-menu-item>
        <el-menu-item index="">
          <i class="mdi mdi-apps"></i><span slot="title">Opportunity</span>
        </el-menu-item>
        <el-menu-item index="">
          <i class="mdi mdi-apps"></i><span slot="title">Competitor</span>
        </el-menu-item>-->
        <div
            v-if="
                is_assigned([
                    'ubsend-list',
                    'pricegroup-list',
                    'carrier-list',
                    'carrierproduct-list',
                    'weightclass-list',
                    'video-list',
                    'onboardingtemplate-view',
                    'agreementtemplate-view',
                    'fundtemplate-view',
                    'createordertemplate-view',
                    'footertemplate-view'
                ])
            "
            class="el-menu-item-group__title"
            style="padding-top: 4px"
        >
            <span>Settings</span>
        </div>
        <el-menu-item index="/ubsend/list" v-if="is_assigned('ubsend-list')">
            <i class="mdi mdi-office-building"></i><span slot="title">UBsend</span>
        </el-menu-item>
        <el-menu-item index="/price-group/list" v-if="is_assigned('pricegroup-list')">
            <i class="mdi mdi-bookmark-multiple"></i><span slot="title">Price Group</span>
        </el-menu-item>
        <el-sub-menu
            index="/carrier"
            popper-class="main-navigation-submenu"
            v-if="is_assigned(['carrier-list', 'carrierproduct-list'])"
        >
            <template #title><i class="mdi mdi-package-variant-closed"></i><span>Carrier</span></template>
            <el-menu-item index="/carrier/carrier-list" v-if="is_assigned('carrier-list')">
                <span slot="carrier">Carrier List</span>
            </el-menu-item>
            <el-menu-item index="/carrier/carrier-products" v-if="is_assigned('carrierproduct-list')">
                <span slot="carrier">Carrier Products</span>
            </el-menu-item>
        </el-sub-menu>
        <el-menu-item v-if="is_assigned('shipping-countries')" index="/shipping-countries">
            <i class="mdi mdi-flag"></i><span slot="video">Shipping Countries</span>
        </el-menu-item>
        <el-menu-item index="/weight-classes" v-if="is_assigned('weightclass-list')">
            <i class="mdi mdi-weight"></i><span slot="weight">Weight Class</span>
        </el-menu-item>
        <el-menu-item index="/video/video-list" v-if="is_assigned('video-list')">
            <i class="mdi mdi-video"></i><span slot="video">Video</span>
        </el-menu-item>
        <el-sub-menu
            index="/template"
            popper-class="main-navigation-submenu"
            v-if="
                is_assigned([
                    'onboardingtemplate-view',
                    'agreementtemplate-view',
                    'fundtemplate-view',
                    'createordertemplate-view',
                    'footertemplate-view'
                ])
            "
        >
            <template #title><i class="mdi mdi-file-document-outline"></i><span>Template</span></template>
            <el-menu-item v-if="is_assigned('onboardingtemplate-view')" index="/template/create-email-template">
                <span slot="template">Onboarding Email</span>
            </el-menu-item>
            <el-menu-item v-if="is_assigned('agreementtemplate-view')" index="/template/create-template">
                <span slot="template">Agreement</span>
            </el-menu-item>
            <el-menu-item v-if="is_assigned('fundtemplate-view')" index="/template/create-add-funds">
                <span slot="template">Add Funds</span>
            </el-menu-item>
            <el-menu-item v-if="is_assigned('createordertemplate-view')" index="/template/create-order-template">
                <span slot="template">Create Order</span>
            </el-menu-item>
            <el-menu-item v-if="is_assigned('footertemplate-view')" index="/template/create-footer-template">
                <span slot="template">Footer</span>
            </el-menu-item>
            <el-menu-item index="/template/create-shipping-email-template">
                <span slot="template">Tracking Messages</span>
            </el-menu-item>
        </el-sub-menu>
        <!--<el-sub-menu index="/reports" popper-class="main-navigation-submenu">
        <template #title><i class="mdi mdi-file-chart-check"></i><span>Reports</span></template>
        <el-menu-item index="/reports/ub-comparison">
            <span slot="reports">Ub Comparison</span>
        </el-menu-item>
        <el-menu-item index="/reports/revenue">
            <span slot="reports">Revenue</span>
        </el-menu-item>
        <el-menu-item index="/reports/balance">
            <span slot="reports">Balance</span>
        </el-menu-item>
    </el-sub-menu>-->
        <el-sub-menu
            v-if="is_assigned(['dimensions', 'dimension-validations', 'addresses'])"
            index="/settings"
            popper-class="main-navigation-submenu"
        >
            <template #title><i class="mdi mdi-refresh"></i><span>Settings</span></template>
            <el-menu-item v-if="is_assigned('dimension-validations')" index="/settings/carrier-validations-settings">
                <span slot="settings">Dimension Validations</span>
            </el-menu-item>
            <el-menu-item v-if="is_assigned('dimensions')" index="/settings/dimension-settings">
                <span slot="settings">Dimensions</span>
            </el-menu-item>
            <el-menu-item v-if="is_assigned('addresses')" index="/settings/address-settings">
                <span slot="settings">Addresses</span>
            </el-menu-item>
        </el-sub-menu>
        <div
            v-if="is_assigned(['systemuser-list', 'role-list', 'permission-list'])"
            class="el-menu-item-group__title"
            style="padding-top: 4px"
        >
            <span>System User</span>
        </div>
        <!-- <el-sub-menu index="/agreement" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-file-document-edit-outline"></i><span>Agreement</span> </template>
        <el-menu-item index="/agreement/add-agreement">
            <span slot="agreement">Add Agreement</span>
        </el-menu-item>
        <el-menu-item index="/agreement/create-agreement-template">
            <span slot="agreement">Add Agreement Template</span>
        </el-menu-item>
        <el-menu-item index="/agreement/agreement-list">
            <span slot="agreement">Agreement List</span>
        </el-menu-item>
    </el-sub-menu>  -->

        <!--
            <el-sub-menu index="/onboarding" popper-class="main-navigation-submenu">
                <template #title> <i class="mdi mdi-ferry"></i><span>Onboarding Process</span> </template>
                <el-menu-item index="/onboarding/process-terms">
                    <span slot="onboarding">Onboarding</span>
                </el-menu-item>
            </el-sub-menu>-->
        <el-menu-item v-if="is_assigned('systemuser-list')" index="/system-user/list">
            <i class="mdi mdi-account"></i><span slot="role">User</span>
        </el-menu-item>
        <el-menu-item v-if="is_assigned('role-list')" index="/role/role-list">
            <i class="mdi mdi-account-cog"></i><span slot="role">Role List</span>
        </el-menu-item>
        <el-menu-item v-if="is_assigned('permission-list')" index="/permission/permission-list">
            <i class="mdi mdi-account-edit"></i><span slot="permission">Permission</span>
        </el-menu-item>

        <!-- <el-menu-item index="/">
        <i class="mdi mdi-account-group"></i><span slot="title">Onboarding</span>
    </el-menu-item> -->

        <!-- <el-sub-menu index="/settings" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-cog-outline"></i><span>Setting</span> </template>
        <el-menu-item index="/agreement/add-agreement">
            <span slot="title">Carrier</span>
        </el-menu-item>
    </el-sub-menu> -->

        <!-- <el-sub-menu index="/systems" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-cog-sync"></i><span>System</span> </template>
        <el-menu-item index="/agreement/add-agreement">
            <span slot="title">Users</span>
        </el-menu-item>
        <el-menu-item index="/agreement/agreement-list">
            <span slot="title">Role And Permissions</span>
        </el-menu-item>
    </el-sub-menu> -->

        <!-- <el-sub-menu index="/dashboards" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-gauge"></i><span>Dashboard</span> </template>
        <el-menu-item index="/dashboard">
            <span slot="title">Analytical</span>
        </el-menu-item>
        <el-menu-item index="/ecommerce-dashboard">
            <span slot="title">eCommerce</span>
        </el-menu-item>
        <el-menu-item index="/crypto-dashboard">
            <span slot="title">Crypto</span>
        </el-menu-item>
    </el-sub-menu> -->

        <!-- <el-menu-item index="/calendar">
        <i class="mdi mdi-calendar"></i><span slot="title">Calendar</span>
    </el-menu-item>
    <el-menu-item index="/contacts">
        <i class="mdi mdi-account-multiple-outline"></i><span slot="title">Contacts</span>
    </el-menu-item>
    <el-menu-item index="/gallery">
        <i class="mdi mdi-image-multiple-outline"></i><span slot="title">Gallery</span>
    </el-menu-item>
    <el-menu-item index="/cards">
        <i class="mdi mdi-view-dashboard-outline"></i><span slot="title">Cards</span>
    </el-menu-item>
    <div class="el-menu-item-group__title"><span>User interface</span></div>
    <el-sub-menu index="layout" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-view-compact-outline"></i><span>Layout</span> </template>
        <el-menu-item index="/layout/flexbox">
            <span slot="title">Flexbox</span>
        </el-menu-item>
        <el-menu-item index="/layout/blank">
            <span slot="title">Blank page</span>
        </el-menu-item>
        <el-menu-item index="/layout/page-headers">
            <span slot="title">Page headers</span>
        </el-menu-item>
        <el-menu-item index="/layout/sidebar-right">
            <span slot="title">Sidebar right</span>
        </el-menu-item>
        <el-menu-item index="/layout/sidebar-left">
            <span slot="title">Sidebar left</span>
        </el-menu-item>
        <el-menu-item index="/layout/tabbed">
            <span slot="title">Tabbed page</span>
        </el-menu-item>
    </el-sub-menu>
    <el-menu-item index="/themes">
        <i class="mdi mdi-format-color-fill"></i><span slot="title">Themes</span>
    </el-menu-item> -->
        <!-- <el-sub-menu index="1" popper-class="main-navigation-submenu" popper-append-to-body>
        <template #title> <i class="mdi mdi-menu"></i><span>Multi level menu</span> </template>
        <el-menu-item index="1-1">item one (1)</el-menu-item>
        <el-menu-item index="1-2">item two (2)</el-menu-item>
        <el-menu-item index="1-3">item three (3)</el-menu-item>
        <el-sub-menu index="1-4" popper-class="main-navigation-submenu" popper-append-to-body>
            <template #title>item four (4)</template>
            <el-sub-menu index="1-4-1" popper-class="main-navigation-submenu" popper-append-to-body>
                <template #title>item one (4.1)</template>
                <el-menu-item index="1-4-1-1">item one (4.1.1)</el-menu-item>
                <el-menu-item index="1-4-1-2">item two (4.1.2)</el-menu-item>
                <el-menu-item index="1-4-1-3">item three (4.1.3)</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="1-4-2" popper-class="main-navigation-submenu" popper-append-to-body>
                <template #title>item two (4.2)</template>
                <el-menu-item index="1-4-2-1">item one (4.2.1)</el-menu-item>
                <el-menu-item index="1-4-2-2">item two (4.2.2)</el-menu-item>
                <el-menu-item index="1-4-2-3">item three (4.2.3)</el-menu-item>
            </el-sub-menu>
            <el-menu-item index="1-4-3">item three (4-3)</el-menu-item>
            <el-menu-item index="1-4-4">item four (4-4)</el-menu-item>
            <el-menu-item index="1-4-5">item five (4-5)</el-menu-item>
            <el-menu-item index="1-4-6">item six (4-6)</el-menu-item>
            <el-menu-item index="1-4-7">item seven (4-7)</el-menu-item>
        </el-sub-menu>
    </el-sub-menu> -->
        <!-- <el-sub-menu index="icons" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-folder-star-outline"></i><span>Icons</span> </template>
        <el-menu-item index="/icons/md-icons">
            <span slot="title">MD Icons</span>
        </el-menu-item>
        <el-menu-item index="/icons/flag-icons">
            <span slot="title">Flag Icons</span>
        </el-menu-item>
    </el-sub-menu> -->
        <!-- <el-menu-item index="/multi-language">
        <i class="mdi mdi-translate"></i><span slot="title">Multi language</span>
    </el-menu-item> -->
        <!-- <el-menu-item index="/typography">
        <i class="mdi mdi-format-font"></i><span slot="title">Typography</span>
    </el-menu-item>
    <el-menu-item index="/helper-classes">
        <i class="mdi mdi-help-circle-outline"></i><span slot="title">Helper Classes</span>
    </el-menu-item> -->
        <!-- <el-sub-menu index="element" popper-class="main-navigation-submenu limit-height">
        <template #title> <i class="mdi mdi-shape-outline"></i><span>Element</span> </template>
        <el-menu-item index="/element/button">
            <span slot="title">Button</span>
        </el-menu-item>
        <el-menu-item index="/element/radio">
            <span slot="title">Radio</span>
        </el-menu-item>
        <el-menu-item index="/element/checkbox">
            <span slot="title">Checkbox</span>
        </el-menu-item>
        <el-menu-item index="/element/input">
            <span slot="title">Input</span>
        </el-menu-item>
        <el-menu-item index="/element/input-number">
            <span slot="title">Input Number</span>
        </el-menu-item>
        <el-menu-item index="/element/select">
            <span slot="title">Select</span>
        </el-menu-item>
        <el-menu-item index="/element/cascader">
            <span slot="title">Cascader</span>
        </el-menu-item>
        <el-menu-item index="/element/switch">
            <span slot="title">Switch</span>
        </el-menu-item>
        <el-menu-item index="/element/slider">
            <span slot="title">Slider</span>
        </el-menu-item>
        <el-menu-item index="/element/time-picker">
            <span slot="title">Time Picker</span>
        </el-menu-item>
        <el-menu-item index="/element/date-picker">
            <span slot="title">Date Picker</span>
        </el-menu-item>
        <el-menu-item index="/element/datetime-picker">
            <span slot="title">Date Time Picker</span>
        </el-menu-item>
        <el-menu-item index="/element/upload">
            <span slot="title">Upload</span>
        </el-menu-item>
        <el-menu-item index="/element/rate">
            <span slot="title">Rate</span>
        </el-menu-item>
        <el-menu-item index="/element/color-picker">
            <span slot="title">Color Picker</span>
        </el-menu-item>
        <el-menu-item index="/element/transfer">
            <span slot="title">Transfer</span>
        </el-menu-item>
        <el-menu-item index="/element/form">
            <span slot="title">Form</span>
        </el-menu-item>
        <el-menu-item index="/element/tag">
            <span slot="title">Tag</span>
        </el-menu-item>
        <el-menu-item index="/element/progress">
            <span slot="title">Progress</span>
        </el-menu-item>
        <el-menu-item index="/element/tree">
            <span slot="title">Tree</span>
        </el-menu-item>
        <el-menu-item index="/element/pagination">
            <span slot="title">Pagination</span>
        </el-menu-item>
        <el-menu-item index="/element/badge">
            <span slot="title">Badge</span>
        </el-menu-item>
        <el-menu-item index="/element/alert">
            <span slot="title">Alert</span>
        </el-menu-item>
        <el-menu-item index="/element/loading">
            <span slot="title">Loading</span>
        </el-menu-item>
        <el-menu-item index="/element/message">
            <span slot="title">Message</span>
        </el-menu-item>
        <el-menu-item index="/element/message-box">
            <span slot="title">Message Box</span>
        </el-menu-item>
        <el-menu-item index="/element/notification">
            <span slot="title">Notification</span>
        </el-menu-item>
        <el-menu-item index="/element/menu">
            <span slot="title">NavMenu</span>
        </el-menu-item>
        <el-menu-item index="/element/tabs">
            <span slot="title">Tabs</span>
        </el-menu-item>
        <el-menu-item index="/element/breadcrumb">
            <span slot="title">Breadcrumb</span>
        </el-menu-item>
        <el-menu-item index="/element/dropdown">
            <span slot="title">Dropdown</span>
        </el-menu-item>
        <el-menu-item index="/element/steps">
            <span slot="title">Steps</span>
        </el-menu-item>
        <el-menu-item index="/element/dialog">
            <span slot="title">Dialog</span>
        </el-menu-item>
        <el-menu-item index="/element/tooltip">
            <span slot="title">Tooltip</span>
        </el-menu-item>
        <el-menu-item index="/element/popover">
            <span slot="title">Popover</span>
        </el-menu-item>
        <el-menu-item index="/element/card">
            <span slot="title">Card</span>
        </el-menu-item>
        <el-menu-item index="/element/carousel">
            <span slot="title">Carousel</span>
        </el-menu-item>
        <el-menu-item index="/element/collapse">
            <span slot="title">Collapse</span>
        </el-menu-item>
        <el-menu-item index="/element/timeline">
            <span slot="title">Timeline</span>
        </el-menu-item>
    </el-sub-menu> -->

        <!-- <div class="el-menu-item-group__title"><span>Components</span></div>
    <el-sub-menu index="tables" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-table"></i><span>Tables</span> </template>
        <el-menu-item index="/tables/simple-table">
            <span slot="title">Basic Table</span>
        </el-menu-item>
        <el-menu-item index="/tables/styled-table">
            <span slot="title">Element UI</span>
        </el-menu-item>
    </el-sub-menu> -->
        <!-- <el-sub-menu index="maps" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-map-outline"></i><span>Maps</span> </template>
        <el-menu-item index="/maps/leaflet">
            <span slot="title">Leaflet</span>
        </el-menu-item>
        <el-menu-item index="/maps/mapbox">
            <span slot="title">Mapbox</span>
        </el-menu-item>
    </el-sub-menu> -->
        <!-- <el-sub-menu index="editors" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-pencil-box-outline"></i><span>Editors</span> </template>
        <el-menu-item index="/editors/quill">
            <span slot="title">Quill</span>
        </el-menu-item>
        <el-menu-item index="/editors/pell">
            <span slot="title">Pell</span>
        </el-menu-item>
        <el-menu-item index="/editors/markdown">
            <span slot="title">Markdown</span>
        </el-menu-item>
    </el-sub-menu> -->
        <!-- <el-sub-menu index="charts" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-chart-line"></i><span>Charts</span> </template>
        <el-menu-item index="/charts/vuechartkick">
            <span slot="title">Vue Chartkick</span>
        </el-menu-item>
        <el-menu-item index="/charts/peity">
            <span slot="title">Peity</span>
        </el-menu-item>
        <el-menu-item index="/charts/echarts">
            <span slot="title">Echarts</span>
        </el-menu-item>
    </el-sub-menu> -->

        <!-- <div class="el-menu-item-group__title"><span>Pages</span></div> -->
        <!-- <el-menu-item index="/profile">
        <i class="mdi mdi-card-account-details-outline"></i><span slot="title">Profile</span>
    </el-menu-item> -->
        <!-- <el-sub-menu index="authentication" popper-class="main-navigation-submenu">
        <template #title> <i class="mdi mdi-lock-outline"></i><span>Authentication</span> </template>
        <el-menu-item index="/logout">
            <span slot="title">Login</span>
        </el-menu-item>
        <el-menu-item index="/login2">
            <span slot="title">Login 2</span>
        </el-menu-item>
        <el-menu-item index="/new-password">
            <span slot="title">New Password</span>
        </el-menu-item>
        <el-menu-item index="/forgot-password">
            <span slot="title">Forgot Password</span>
        </el-menu-item>
    </el-sub-menu> -->
        <!-- <el-menu-item index="/invoice">
        <i class="mdi mdi-file-document-edit-outline"></i><span slot="title">Invoice</span>
    </el-menu-item> -->
        <!-- <el-menu-item index="/404">
        <i class="mdi mdi-alert-octagon-outline"></i><span slot="title">404</span>
    </el-menu-item> -->

        <div class="collapse-menu-btn" :class="{ collapsed: isCollapse }">
            <button @click="$emit('collapse-nav-toggle')" class="flex align-center">
                <span v-if="!isCollapse"> <i class="mdi mdi-unfold-less-vertical"></i> Collapse </span>
                <span v-if="isCollapse"> <i class="mdi mdi-unfold-more-vertical"></i></span>
            </button>
        </div>
    </el-menu>
</template>

<script lang="ts">
import { defineComponent } from "@vue/runtime-core"
import { detect } from "detect-browser"
import { useRoute } from "vue-router"
import { authService } from "@/services/_singletons"

const browser = detect()

export default defineComponent({
    name: "Nav",
    props: ["mode", "isCollapse"],
    data() {
        return {
            isIe: true,
            isEdge: true,
            activeLink: null as unknown as string,
            authService
        }
    },
    methods: {
        is_assigned(name) {
            return authService.isAssigned(name)
        },
        goto(index: string) {
            if (index.charAt(0) === "/") {
                this.$router.push(index)
                this.$emit("push-page", { page: index })
            }
        },
        setLink(path: string) {
            this.activeLink = path
        }
    },
    created() {
        if (browser && browser.name !== "ie") this.isIe = false
        if (browser && browser.name !== "edge") this.isEdge = false

        const route = useRoute()
        this.setLink(route.path)
        this.$router.afterEach((to, from) => {
            this.setLink(route.path)
        })
    }
})
</script>

<style lang="scss" scoped>
@import "../assets/scss/_variables";

.el-menu {
    border: none;
}

.el-menu::before,
.el-menu::after {
    display: none;
}

.el-sub-menu,
.el-menu-item {
    .mdi {
        vertical-align: middle;
        margin-right: 5px;
        display: inline-block;
        width: 24px;
        text-align: center;
        font-size: 18px;
    }
}

.collapse-menu-btn {
    button {
        border-radius: 4px;
        border: none;
        color: $text-color-accent;
        background: lighten($text-color-accent, 30);
        font-weight: 600;
        padding: 4px 8px;
        margin-top: 20px;
        font-size: 12px;
        margin-left: 20px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-align: center;

        i {
            font-size: 14px;
            margin-right: 5px;
        }
    }

    &.collapsed {
        button {
            margin-left: 17px;

            i {
                margin-right: -2px;
            }
        }
    }
}
</style>

<style lang="scss">
@import "../assets/scss/_variables";

.main-navigation-menu {
    transition: width 0.5s;

    &:not(.el-menu--collapse) {
        .el-sub-menu__title,
        .el-menu-item {
            height: 40px;
            line-height: 40px;
            background-color: transparent !important;
        }

        &:not(.el-menu--horizontal) {
            .el-menu-item,
            .el-sub-menu {
                position: relative;

                &::before {
                    content: "";
                    display: block;
                    width: 0px;
                    height: 1px;
                    position: absolute;
                    bottom: 10px;
                    left: 30px;
                    // background: $text-color-primary;
                    background: $text-color-white;
                    z-index: 1;
                    opacity: 0;
                    transition: all 0.7s cubic-bezier(0.55, 0, 0.1, 1);
                }

                &:hover {
                    &::before {
                        width: 100px;
                        opacity: 1;
                        //left: 50px;
                        transform: translate(20px, 0);
                    }
                }

                &.is-active {
                    &::before {
                        background: $text-color-accent;
                    }
                }
            }
        }

        .el-sub-menu.is-opened {
            //background: #edf1f6 !important;
            //background: rgba(223, 228, 234, 0.38) !important;
            position: relative;

            &::after {
                content: "";
                display: block;
                width: 2px;
                position: absolute;
                top: 40px;
                bottom: 10px;
                left: 31px;
                background: $text-color-white;
                z-index: 1;
            }

            &::before {
                display: none;
            }

            .el-menu-item,
            .el-sub-menu {
                &::before,
                &::after {
                    display: none;
                }
            }
        }

        .el-menu-item-group__title {
            padding: 15px 0 0px 20px;
            text-decoration: underline;
            font-weight: bold;
            font-size: 13px;
            color: transparentize($text-color-white, 0.65);
        }
    }

    .el-sub-menu__title,
    .el-menu-item:not(.is-active) {
        color: $text-color-white;

        i {
            color: $text-color-white;
        }
    }

    &.el-menu--collapse {
        .el-menu-item-group__title {
            padding: 15px 0 0px 0px;
            width: 100%;
            text-align: center;
        }

        .el-sub-menu__title:hover,
        .el-menu-item:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }
    }

    &.el-menu--horizontal {
        white-space: nowrap;
        /*width: fit-content;
width: max-content;*/
        overflow: hidden;
        display: table;

        & > * {
            float: inherit !important;
            display: inline-block;
        }
    }

    &.nav-collapsed {
        .el-menu-item,
        .el-sub-menu__title {
            & > span {
                display: none;
            }
        }
    }
}

.main-navigation-submenu {
    .el-menu {
        background: #fff !important;
        //   background-color: #cccccc !important;

        .el-menu-item:not(.is-active) {
            color: black;
        }

        .el-menu-item:hover {
            background-color: transparentize($background-color, 0.3) !important;
        }
    }
}
</style>
