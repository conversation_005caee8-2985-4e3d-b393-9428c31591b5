export interface IMerchant {
    company_name: string
    address: string
    country_id: string
    zip: string
    vat: string
    platform: string
    company_url: string
    platform_url: string
    summary: string
    agreement_summary: string
    name: string
    email: string
    phone: string
    city: string
    merchant_current_agreement: string[]
    id: string
    economic_id: string
    credit_limit: string | number
    credit_enabled: boolean | string
    billing_cycle: string
    billing_cycle_start: string
}

export interface IMerchantResponse {
    company_name: string
    address: string
    country_code: string
    zip: string
    vat: string
    platform_id: string
    company_url: string
    platform_url: string
    summary: string
    agreement_summary: string
    name: string
    email: string
    phone: string
    city: string
    merchant_current_agreement: string[]
    id: string
}

export interface IAddMerchantRequest {
    company_code: string
    company_name: string
    address: string
    country_code: string
    zip: string
    vat: string
    platform_id: string
    company_url: string
    platform_url: string
    summary: string
    agreement_summary: string
    name: string
    email: string
    phone: string
    city: string
    merchant_current_agreement: string[]
    id: string
}

export interface IMerchantResetPasswordRequest {
    user_code: string
}

export interface IMerchantResetPasswordResponse {
    code: string
    message: string
    data: null
}

export interface IMerchantRequestDetail {
    user_code: string
}

export interface IMerchantDetailResponse {
    user_code: string
    name: string
    email: string
    email_verified_at: string
    company_code: string
    created_at: string
    updated_at: string
    role: string
    otp: string
    permitted_modules: string[]
    deleted_at: string
    company: string[]
    merchant_current_agreement: string[]
    id: string
}

export interface IDeleteMerchantRequest {
    user_code: string
}

export interface IMerchantDelteResponse {
    user_code: string
}

export interface IMerchantUpdateResponse {
    data: object
}

export interface IMerchantUpdatePasswordRequest {
    password: string
    c_password: string
    api_key?: string
    platform_code?: string
    api_url?: string
    access_token?: string
    api_secret_key?: string
    name?: string
    otp: string
    api_username?: string
}

export interface IMerchantUpdatePasswordResponse {
    code: string
    message: string
    data: {
        user: IMerchantResponse
    }
}

export interface IMerchantUpdateStatusRequest {
    user_code: string | number
    is_active: boolean | number
}

export interface UpdateCreditStatusRequest {
    company_code: string | number
    credit_enabled: boolean | number
    credit_limit: string | number | null
    billing_cycle: string | number | null
    billing_cycle_start: string | number | null
}

export interface ICreateSettlementRequest {
    invoiceId: number | string
    invoiceNo: string
    amountPayable: number
    companyCode: number | string
}

export interface ICreateSettlementResponse {
    code: number
    message: string
    data: {
        settlement_id: number | string
        invoice_id: number | string
        amount: number
        status: string
        created_at: string
    } | null
}
