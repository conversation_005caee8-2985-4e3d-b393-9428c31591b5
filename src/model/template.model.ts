export interface ITemplate {
  id: string,
  agreement_footer: string,
  summary: string,
  type: string,
}

export interface ITemplateResponse {
  id: string,
  agreement_footer: string,
  summary: string,
  type: string,
}
export interface IPriceListResponse {
  price_group_id:string;
}
export interface IPriceGroupListResponse {
  carrier:string;
}
export interface IGetTemplateRequest {
  type: string,
}
export interface ISaveTemplateRequest {
  summary: string,
  type: string,
}
export interface IDeleteTemplateRequest {
  price_group_id: string;
}

export interface ITemplateDelteResponse {
  price_group_id: string;
}
export interface ITemplateResponse {
  data: object;
}

export interface ITemplateRequest {
}