import {fileService} from './../services/_singletons';

export interface ICarrier {
    id: string,
    name: string,
    carrier_code: string,
    carrier_id: string,
    tracking_url: string,
}

export interface IStats {
    shipment_labels: string,
    customers_count: string,
    new_customers_count: string,
    shipvagoo_earning: string,
    shipvagoo_revenue: string,
    total_ubsend_cost: string,
}

export interface IDashboardContentResponse {
    code: string;
    message: string;
    data: IStats;
}

export interface ICarrierProduct {
    id: string,
    carrier_product_code: number,
    carrier_code: number,
    name: string,
    product_id: string,
    note: string,
    pick_type: string,
    deleted_at?: string | null,
    created_at?: string,
    updated_at?: string
}

export interface ICarrierProductRequest {
    carrier_code: number,
    name: string,
    product_id: string,
    note: string,
    // pick_type: string
}

export interface ICarrierProductCreateResponse {
    code: number,
    message: string,
    data: {
        product: ICarrierProduct
    }
}

export interface ICarrierResponse {
    id: string,
    name: string,
    carrier_code: string,
    carrier_id: string,
    tracking_url: string,
    icon: Object,
    file: File,
}

export interface IAddCarrierRequest {
    id: string,
    name: string,
    carrier_code: string,
    carrier_id: string,
    tracking_url: string,
    icon: Object,
    file: File,

}

export interface IDeleteCarrierRequest {
    id: string;
}

export interface IDeleteCarrierProductRequest {
    carrier_product_code: string;
}

export interface ICarrierDelteResponse {
    id: string;
}

export interface IFileUploadResponse {
    data: object;
}

export interface ICarrierStatusUpdateRequest {
    id: string | number,
    status: boolean | number
}