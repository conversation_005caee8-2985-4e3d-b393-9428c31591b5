import {ICompany} from './company.model';
import {IUser} from './user.model';

export interface ILoginRequest {
    email: string;
    password: string;
    time_zone: string
}

export interface IOptRequest {
    opt: string;
    email: string;
}

export interface ILoginResponse extends IUser {
    data: {
        data: {
            token: string,
            admin: object
        };
    },
    token?:string

}

export interface IOptResponse {
    opt: string;
    email: string;
}

export interface ISignUpRequest {
    company_name: string;
    contact_person: string;
    address: string;
    country_code: number;
    city: string;
    language_code: number;
    zipcode: string;
    vat_no: string;
    phone_no: string;
    email: string;
    password: string;
    password_confirmation: string;
}

export interface ISignUpResponse extends ICompany {
    user: ILoginResponse;
}

export interface ILogoutRequest {
    token: string;
}

export interface IForgotPasswordRequest {
    email: string;
}

export type IForgotPasswordResponse = [false];

export interface IResetPasswordRequest {
    password: string;
    otp: string;
}

export interface IVerifyOtp {
    otp: string;
}

