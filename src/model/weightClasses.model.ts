import { fileService } from './../services/_singletons';

export interface IWeightClass{
    id: string,
    weight_class_code: number,
    min_weight: string,
    max_weight: string,
    unit: string,
}

export interface IWeightClassRequest {
    min_weight: string,
    max_weight: string,
    unit: string,
}

export interface IWeightClassCreateResponse {
    code: number,
    message: string,
    data: {
      product: IWeightClass
    }
}

export interface IDeleteWeightClassRequest {
    weight_class_code: string;
  }
  export interface IWeightClassDelteResponse {
    id: string;
  }
