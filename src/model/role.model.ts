import { fileService } from '../services/_singletons';

export interface IRole {
  id: string,
  name: string,
  carrier_code: string,
  carrier_id: string,
  tracking_url: string,
}

export interface IRoleProduct {
  id: string,
  carrier_product_code: number,
  carrier_code: number,
  name: string,
  product_id: string,
  note: string,
  pick_type: string,
  deleted_at?: string| null,
  created_at?: string,
  updated_at?: string
}
export interface IRoleProductRequest {
  carrier_code: number,
  name: string,
  product_id: string,
  note: string,
  pick_type: string
}
export interface IRoleProductCreateResponse {
  code: number,
  message: string,
  data: {
    product: IRoleProduct
  }
}

export interface IRoleResponse {
  id: string,
  name: string,
  permissions: [],
}
export interface IAddRoleRequest {
  id: string,
  name: string,
  permissions: [],

}
export interface IDeleteRoleRequest {
  id: string;
}
export interface IRoleDeleteResponse {
  id: string;
}
export interface IDeleteRoleProductRequest {
  Role_product_code: string;
}
export interface IRoleDelteResponse {
  id: string;
}
export interface IFileUploadResponse  {
  data: object;
}
