export interface IPriceGroup {
  company_name: string,
  address: string,
  country_id: string,
  zip: string,
  vat: string,
  platform: string,
  summary: string,
  agreement_summary: string,
  name: string,
  email: string,
  phone: string,
  city: string,
  PriceGroup_current_agreement: string[],
  id: string;
}

export interface IPriceEditRequestParam {
  price_group_id:string;
}
export interface IPriceGroupResponse {
  ubsend_account_id: string,
}
export interface IPriceListResponse {
  ubsend_account_id:string;
  carrier:string;
}
export interface IAddPriceGroupRequest {
  company_code: string,
  company_name: string,
  address: string,
  country_id: string,
  zip: string,
  vat: string,
  platform: string,
  summary: string,
  agreement_summary: string,
  name: string,
  email: string,
  phone: string,
  city: string,
  merchant_current_agreement: string[],
  id: string;
}
export interface IDeletePriceGroupRequest {
  price_group_id: string;
}

export interface IPriceGroupDelteResponse {
  price_group_id: string;
}
export interface IPriceGroupUpdateResponse {
  data: object;
}