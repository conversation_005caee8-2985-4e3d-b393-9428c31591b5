import { fileService } from '../services/_singletons';

export interface ISystemUserResponse {
  id: string,
  name: string,
  password: string,
  email: string,
  active: string,
  role_id: string,

}
export interface IAddSystemUserRequest {
  id: string,
  name: string,
  password: string,
  email: string,
  active: string,
  role_id: string,
}
export interface IDeleteSystemUserRequest {
  id: string;
}
export interface ISystemUserDeleteResponse {
  id: string;
}
export interface IDeleteSystemUserProductRequest {
  SystemUser_product_code: string;
}
export interface ISystemUserDelteResponse {
  id: string;
}
export interface IFileUploadResponse  {
  data: object;
}
