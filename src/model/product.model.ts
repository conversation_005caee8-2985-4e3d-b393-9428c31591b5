import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './common.model';
import { ICompany } from './company.model';
import { ICountry } from './country.model';

export interface IProductLabelOptions {
  sku: string;
  name: string;
  bin?: string;
  barcode: string;
}

export interface IProductShelfOptions {
  sectionFrom: TAlphabet;
  sectionTo: TAlphabet;
  numberFrom: number;
  numberTo: number;
  levels: number;
  placements: number;
}

export interface IProductVariant {
  id: number;
  sku: '';
  grams: 0;
  price: string;
  title: string;
  weight: number;
  barcode: string;
  option1: string;
  option2: string | null;
  option3: string | null;
  taxable: boolean;
  image_id: string | null;
  position: number;
  created_at: string;
  product_id: number;
  updated_at: string;
  weight_unit: string;
  compare_at_price: string | null;
  inventory_policy: string;
  inventory_item_id: number;
  requires_shipping: boolean;
  inventory_quantity: number;
  fulfillment_service: string;
  admin_graphql_api_id: string;
  inventory_management: string;
  old_inventory_quantity: number;
}

export interface IProductOption {
  id: number;
  name: string;
  values: string[];
  position: number;
  product_id: number;
}

export interface IProductImage {
  id: number;
  alt: string | null;
  src: string;
  width: number;
  height: number;
  position: number;
  created_at: string;
  product_id: number;
  updated_at: string;
  variant_ids: number[];
  admin_graphql_api_id: string;
}

export interface IProduct {
  product_code: number;
  integration_code: number;
  company_code: number;
  product_id: number;
  title: string;
  sku: string | null;
  body_html: string;
  bin: string | null;
  barcode: string | null;
  is_virtual: 0 | 1;
  variant_code: string | null;
  selling_price: string;
  currency_code: null;
  currency: null;
  country_code: number | null;
  content: string | null;
  commodity_code: string | null;
  weight: string;
  product_type: null;
  handle: string;
  tags: string;
  image_url: string | null;
  company: ICompany | null;
  country: ICountry | null;
  created_at: string;
  updated_at: string;
}

export interface IProductUpdate {
  title: string;
  bin: string | null;
  barcode: string | null;
  is_virtual: 0 | 1;
  image: string | null;
  country_code: number | null;
  content: string | null;
  commodity_code: string | null;
  weight: number;
  variant_code: string | null;
  selling_price: number;
}
