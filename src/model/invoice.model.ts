export interface IInvoiceItem {
  type: string;
  items: [
    {
      name: string;
      count: 1;
      unit_price: string;
      weight_class: string;
    },
  ];
  country_pair: string;
}

export interface IInvoice {
  invoice_code: number;
  company_code: number;
  shipment_code: number;
  invoice_items: IInvoiceItem[];
  status: string;
  tax_percent: string;
  amount: number | null;
  shipment: null;
  company: null;
  created_at: string;
  updated_at: string;
}
