
export interface IVideo {
  company_code: number;
  company_name: string;
  contact_person: string;
  zipcode: string;
  country_code: number;
  attn: string | null;
  website: string | null;
  address: string;
  language_code: number;
  language: null;
  vat_no: string;
  phone_no: string;
  city: string;
  balance: number | null;
}

export interface IVideoResponse {
  name:string,
  url: string,
  is_active: boolean,
  id: string;
}
export interface IAddVideoRequest {
  name:string,
  url: string,
  is_active: boolean,
  id: string
}

export interface IDeleteVideoRequest {
  id: string;
}
export interface IVideoDelteResponse {
  id: string;
}
export interface IFileUploadResponse  {
  data: object;
}
