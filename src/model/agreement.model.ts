export interface IAgreement {
  company_name: string,
  address: string,
  country_id: string,
  zip: string,
  vat: string,
  platform: string,
  summary: string,
  agreement_summary: string,
  name: string,
  email: string,
  phone: string,
  city: string,
  Agreement_current_agreement: string[],
  id: string;
}

export interface IAgreementResponse {
  // price_group_id: string,
  merchant_id: string,
  address: string,
  summary: string,
  footer: string,
  date: string,
  name: string,
  pricing: string[],
  id: string;
}
export interface IPriceListResponse {
  price_group_id:string;
}
export interface IAgreementObjResponse {
  agreement_id:string;
}
export interface IPriceGroupListResponse {
  carrier:string;
}
export interface IAddAgreementRequest {
  merchant_id: string,
  address: string,
  summary: string,
  footer: string,
  date: string,
  name: string,
  pricing: string[],
  id: string;
}
export interface IUpdateAgreementRequest {
  pricing: string[],
  agreement_id: string;
}
export interface IDeleteAgreementRequest {
  price_group_id: string;
}

export interface IAgreementDelteResponse {
  price_group_id: string;
}
export interface IAgreementUpdateResponse {
  data: object;
}