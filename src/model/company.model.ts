import { ICountry } from './country.model';

export interface ICompanyBase {
  company_code: number;
  company_name: string;
  contact_person: string;
  zipcode: string;
  country_code: number;
  city: string;
  language_code: number;
  vat_no: string;
  phone_no: string;
  address: string;
  balance: number;
}

export interface ICompany {
  company_code: number;
  company_name: string;
  contact_person: string;
  zipcode: string;
  country_code: number;
  attn: string | null;
  website: string | null;
  address: string;
  language_code: number;
  country: ICountry | null;
  language: null;
  vat_no: string;
  phone_no: string;
  city: string;
  balance: number | null;
}

export interface IBalance {
  balance: number;
}

export type ICompanyUpdateRequest = Omit<ICompanyBase, 'balance'>;

export interface IStaffInvitation {
  invite_code: number;
  name: string;
  email: string;
  token: string;
  company_code: number;
  permitted_modules: number[];
  company: ICompany | null;
}

export interface IStaffInvitationUpdatePayload {
  name: string;
  email: string;
  permitted_modules: number[];
}

export interface ICompanyUser {
  user_code: number;
  company_code: number;
  name: string;
  email: string;
  role: 'SUPER_ADMIN' | 'ADMIN_USER' | 'COMPANY_ADMIN' | 'COMPANY_USER';
  permitted_modules: number[];
}

export type ICompanyUserUpdate = Omit<ICompanyUser, 'user_code' | 'company_code' | 'role'>;

export interface ICompanyState extends ICompany {
  balance: number;
}
