
export interface ICompanyBase {
  company_code: number;
  company_name: string;
  contact_person: string;
  zipcode: string;
  country_code: number;
  city: string;
  language_code: number;
  vat_no: string;
  phone_no: string;
  address: string;
  balance: number;
}

export interface IUbsend {
  company_code: number;
  company_name: string;
  contact_person: string;
  zipcode: string;
  country_code: number;
  attn: string | null;
  website: string | null;
  address: string;
  language_code: number;
  language: null;
  vat_no: string;
  phone_no: string;
  city: string;
  balance: number | null;
}

export interface IUbsendResponse {
  username: string;
  account_name: string;
  password: string;
  client_id: string;
  id: string;
}
export interface IAddUbsendRequest {
  account_name: string;
  username: string;
  password: string;
  client_id: string;
  id: string;
}

export interface IDeleteUbsendRequest {
  id: string;
}

export interface IUploadUbsendRequest {
  file: File;
  ubsend_account_id: string;
}

export interface IUbsendPricingRequest {
  ubsend_account_id: string;
}

export interface IUbsendDelteResponse {
  id: string;
}
export interface IFileUploadResponse  {
  data: object;
}
