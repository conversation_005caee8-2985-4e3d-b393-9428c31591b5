import { fileService } from '../services/_singletons';

export interface IPermission {
  id: string,
  name: string,
  carrier_code: string,
  carrier_id: string,
  tracking_url: string,
}

export interface IPermissionProduct {
  id: string,
  carrier_product_code: number,
  carrier_code: number,
  name: string,
  product_id: string,
  note: string,
  pick_type: string,
  deleted_at?: string| null,
  created_at?: string,
  updated_at?: string
}
export interface IPermissionProductRequest {
  carrier_code: number,
  name: string,
  product_id: string,
  note: string,
  pick_type: string
}
export interface IPermissionProductCreateResponse {
  code: number,
  message: string,
  data: {
    product: IPermissionProduct
  }
}

export interface IPermissionResponse {
  id: string,
  name: string,
  carrier_code: string,
  carrier_id: string,
  tracking_url: string,
  icon: Object,
  file: File,
}
export interface IAddPermissionRequest {
  id: string,
  name: string,
  carrier_code: string,
  carrier_id: string,
  tracking_url: string,
  icon: Object,
  file: File,

}
export interface IDeletePermissionRequest {
  id: string;
}
export interface IPermissionDeleteResponse {
  id: string;
}
export interface IDeletePermissionProductRequest {
  Permission_product_code: string;
}
export interface IPermissionDelteResponse {
  id: string;
}
export interface IFileUploadResponse  {
  data: object;
}
