/// <reference types="vite/client" />
/// <reference types="vue/macros-global" />
declare module "*.vue" {
    import type { DefineComponent } from "vue"
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
    const component: DefineComponent<{}, {}, any>
    export default component
}

interface ImportMetaEnv {
    readonly VITE_baseUrl: string;
    readonly VITE_APP_TITLE: string;
    readonly VITE_MERCHANT_URL: string;
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}
