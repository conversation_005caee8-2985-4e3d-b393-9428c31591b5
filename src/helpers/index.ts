import moment from "moment/moment";

// export const getDateFormat = ($date: any, $format = "DD-MM-YYYY hh:mm") => {
//     return moment(String($date)).format('DD-MM-YYYY hh:mm:ss')
// }
export const getDateFormat = ($date: any, $format = "DD-MM-YYYY") => {
    return $date;
}

export const mwsEmailValidation = (email) => {
    if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email)) {
        return true
    }
    return false
}
export const mwsUrlValidation = (url) => {
    return /((ftp|http|https):\/\/)?[A-Za-z0-9_-]+\.+[A-Za-z0-9.\/%&=\?_:;-]+$/.test(url) ///^(ftp|https?):\/\/+(www\.)?[a-z0-9\-\.]{3,}\.[a-z]{3}$/.test(url);
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
}

export function mwsReplaceAll(str, find, replace) {
    return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
}

export function danishNumberFormat(str) {
    const chars = {
        ',': '.',
        '.': ','
    };

    let s = thousands_separators(str);
    /*if (!s.includes('.')) {
        s += ',00';
    } else {
        s = s.replace(/[.,]/g, m => chars[m]);
    }*/
    const numParts = s.split('.');
    if (numParts.length === 1) {
        s += '.00';
    } else if (numParts[1].length === 1) {
        s += '0';
    }
    s = s.replace(/[.,]/g, m => chars[m]);
    return s;
}

export function thousands_separators(num) {
    var num_parts = num.toString().split(".");
    num_parts[0] = num_parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return num_parts.join(".");
}
