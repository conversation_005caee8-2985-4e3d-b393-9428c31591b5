<template>
    <el-dialog width="30%" title="Create Settlement" v-model="dialogVisible" class="p-0" @close="closeModal">
        <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">
            <el-form ref="formRef" :model="form" :rules="rules" class="mb-50 bb-br-gray" label-width="160px">
                <el-col class="demo-form-inline flex mt-5" :span="24">
                    <el-col class="demo-form-inline" :span="24">
                        <el-form-item
                            label="Invoice Number"
                            prop="invoiceNo"
                            label-width="160px"
                        >
                            <el-input v-model="form.invoiceNo" :disabled="true" placeholder="Invoice Number" />
                        </el-form-item>
                    </el-col>
                </el-col>

                <el-col class="demo-form-inline flex" :span="24">
                    <el-col class="demo-form-inline" :span="24">
                        <el-form-item
                            label="Amount Payable"
                            prop="amountPayable"
                            label-width="160px"
                        >
                            <el-input
                                v-model="form.amountPayable"
                                type="number"
                                :min="0.01"
                                :max="invoice.total_amount"
                                step="0.01"
                                placeholder="Enter amount payable"
                                @input="validateAmount"
                            />
                            <div class="el-form-item__help" style="margin-top: 5px; font-size: 12px; color: #909399">
                                Maximum amount: DKK {{ danishNumberFormat(invoice.total_amount) }}
                            </div>
                        </el-form-item>
                    </el-col>
                </el-col>

                <div class="mt-5 dialog-footer">
                    <el-button
                        class="btn-blue-bg"
                        @click="onSubmit"
                        :disabled="!isFormValid"
                        v-loading.fullscreen.lock="fullscreenLoading"
                    >
                        Save
                    </el-button>
                    <el-button class="btn" @click="closeModal">Cancel</el-button>
                </div>
            </el-form>
        </div>
    </el-dialog>
</template>

<script>
import { danishNumberFormat } from "@/helpers"
import HelperService from "@/services/helper.service"

export default {
    name: "SettlementDialog",
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        invoice: {
            type: Object,
            required: true,
            default: () => ({})
        }
    },
    emits: ["update:visible", "save", "close"],
    data() {
        return {
            fullscreenLoading: false,
            form: {
                invoiceNo: "",
                amountPayable: null
            },
            rules: {
                invoiceNo: [{ required: true, message: "Invoice number is required", trigger: "blur" }],
                amountPayable: [
                    { required: true, message: "Amount payable is required", trigger: "blur" },
                    {
                        validator: this.validateAmountRange,
                        trigger: ["blur", "change"]
                    }
                ]
            }
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible
            },
            set(value) {
                this.$emit("update:visible", value)
            }
        },
        
        maxPayable() {
            const amt = Number(this.invoice?.total_amount)
            return Number.isFinite(amt) && amt > 0 ? amt : 0
        },
        isFormValid() {
            const max = this.maxPayable
            const amt = parseFloat(this.form.amountPayable)
            if (!this.form.invoiceNo) return false
            if (isNaN(amt)) return false
            return amt > 0 && amt <= max
        }
    },
    watch: {
        visible(newVal) {
            if (newVal && this.invoice) {
                this.initializeForm()
            }
        },
        invoice: {
            handler(newVal) {
                if (newVal && this.visible) {
                    this.initializeForm()
                }
            },
            immediate: true
        }
    },
    methods: {
        danishNumberFormat,
        initializeForm() {
            this.form.invoiceNo = HelperService.formatId(this.invoice.id)
            this.form.amountPayable = this.invoice.total_amount
        },
        validateAmount() {
            this.$refs.formRef?.validateField("amountPayable")
        },
        validateAmountRange(rule, value, callback) {
            if (!value) {
                callback(new Error("Amount payable is required"))
                return
            }

            const numValue = parseFloat(value)

            if (isNaN(numValue)) {
                callback(new Error("Please enter a valid number"))
                return
            }

            if (numValue <= 0) {
                callback(new Error("Amount must be greater than 0"))
                return
            }

            if (numValue > this.invoice.total_amount) {
                callback(new Error(`Amount cannot exceed DKK ${this.danishNumberFormat(this.invoice.total_amount)}`))
                return
            }

            callback()
        },
        async onSubmit() {
            try {
                const isValid = await this.$refs.formRef?.validate()
                if (!isValid) return

                this.fullscreenLoading = true
                const settlementData = {
                    economic_invoice_code: this.invoice.economic_invoice_code,
                    invoiceNo: this.form.invoiceNo,
                    amount: parseFloat(this.form.amountPayable),
                    company_code: this.invoice.company.company_code
                }

                this.$emit("save", settlementData)
            } catch (error) {
                console.error("Form validation failed:", error)
            } finally {
                this.fullscreenLoading = false
            }
        },
        closeModal() {
            this.$refs.formRef?.resetFields()
            this.$emit("close")
            this.$emit("update:visible", false)
        }
    }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: right;
}

.btn-blue-bg {
    background-color: #409eff;
    border-color: #409eff;
    color: white;
}

.btn-blue-bg:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
}

.btn-blue-bg:disabled {
    background-color: #c0c4cc;
    border-color: #c0c4cc;
    cursor: not-allowed;
}

.btn {
    margin-left: 10px;
}

.bt-br {
    border-top: 1px solid #eee;
    margin: 20px 0;
}

.el-form-item__help {
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
}
</style>
