<template>
  <h4
      style="color: #BBBBBB; font-size: 14px; font-weight: 700;margin-left: 20px; margin-top: 13px;">
    Last Activity <span style="color: black" id="lastActivity">{{ latestTimeLine(activityLogs) ?? "" }}</span></h4>
  <div class="scrollable only-y">
    <el-timeline>
      <el-timeline-item
          v-for="(activity, key) in getTimeLine(activityLogs)"
          :key="key"
          :type="activity.type"
          :color="activity.color"
          :size="activity.size"
          :hollow="activity.hollow"
      >
        <div v-if="activity.isParent">
          <span>{{ timeAgo(activity.timeLineTitle) }}</span>
        </div>
        <div v-else>{{ activity.timeLineTitle }}</div>
      </el-timeline-item>

    </el-timeline>
  </div>
</template>

<script>
import TimeAgo from 'vue2-timeago'
import {defineComponent} from "@vue/runtime-core"
import moment from 'moment'
import {MoreFilled} from '@element-plus/icons-vue'
import {ref, watch} from 'vue';

export default defineComponent({
  props: {
    logs: {
      type: Object,
      required: true
    }
  },
  components: {
    TimeAgo
  },
  setup(props) {
    const dateObj = new Date();
    var month = dateObj.getUTCMonth() + 1; //months from 1-12
    var day = dateObj.getUTCDate();
    var year = dateObj.getUTCFullYear();
    if (month < 10) {
      month = "0" + month;
    }
    if (day < 10) {
      day = "0" + day;
    }
    let newdate = year + "-" + month + "-" + day;
    const activityLogs = ref(props.logs);
    watch(() => props.logs, (currentVal, oldVal) => {
      activityLogs.value = currentVal;
    })
    return {
      newdate,
      activityLogs
    }
  },
  methods: {

    latestTimeLine(activityLogs) {
      var activity = this.getTimeLine(activityLogs)[0];
      if (activity) {
        if (activity.isParent) {
          return this.timeAgo(activity.timeLineTitle);
        } else {
          return activity.timeLineTitle
        }
      }
    },
    getTimeLine(activityLogs) {
      let newTimeLine = [];
      Object.keys(activityLogs).map((dateAt, index) => {
        //console.log(JSON.stringify(activityLogs[dateAt][0].created_at))
        newTimeLine.push({
          timeLineTitle: `${activityLogs[dateAt][0].created_at}`,
          isParent: true,
          size: 'large',
          type: 'primary',
          icon: MoreFilled,
          hollow: false,
        })
        activityLogs[dateAt].map((timeLineItem, timeLineKey) => {
          newTimeLine.push({
            timeLineTitle: `${timeLineItem.action} by ${timeLineItem.admin_name} at ${this.getDateFormat(timeLineItem.created_at)}`,
            isParent: false,
            size: 'large',
            icon: MoreFilled,
            type: 'primary',
            hollow: true,
          })
        })
      })
      return newTimeLine;
    },
    getDateFormat($date, $format = "MM/DD/YYYY hh:mm") {
      return moment(String($date)).format('MMM DD, YYYY hh:mm:ss')
    },
    timeAgo(date) {
      var seconds = Math.floor((new Date() - new Date(date)) / 1000);
      var interval = seconds / 31536000;
      if (interval > 1) {
        return Math.floor(interval) + " years ago";
      }
      interval = seconds / 2592000;
      if (interval > 1) {
        return Math.floor(interval) + " months ago";
      }
      interval = seconds / 86400;
      if (interval > 1) {
        return Math.floor(interval) + " days ago";
      }
      interval = seconds / 3600;
      if (interval > 1) {
        return Math.floor(interval) + " hours ago";
      }
      interval = seconds / 60;
      if (interval > 1) {
        return Math.floor(interval) + " minutes ago";
      } else {
        return Math.floor(seconds) + " seconds ago";
      }
      return 'N/A';
    }
  }
});
</script>
<style scoped>
@import 'vue2-timeago/dist/vue2-timeago.css';

</style>