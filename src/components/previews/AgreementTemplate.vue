<template>
    <el-dialog title="" v-model="mailTemplate" class="p-0">
        <div class="customer-agreement-dialog position-relative">
            <section id="customer-invoice" ref="DownloadComp">
                <div class="logo-wrapper logo" style="text-align: right">
                    <img src="@/assets/images/logo-black.svg" class="logo" alt="logo"/>
                </div>
                <div class="user-info">
                    <h3 class="customer-name m-0 black-text" style="margin: 5px 0">XYZ Company</h3>
                    <p class="m-0 black-text" style="margin: 5px 0">Lorem ipsum, lorem ipsum</p>
                    <!-- <p class="m-0 black-text" style="margin: 5px 0">DK-2200 Kobenhavn N</p> -->
                    <p class="text-right date m-0 black-text" style="text-align: right">
                        <strong>Date:</strong> 06-02-2023
                    </p>
                </div>
                <div class="application">
                    <p class="black-text">
                        Dear, <strong>XYZ Company</strong>
                    </p>
                    <div class="application-description m-0 black-text" style="margin-left: 8px;">
                        <div v-html="summary" style="line-height: 10px;"></div>
                    </div>
                    <p></p>
                    <p></p>
                    <p></p>
                </div>
                <div class="table mt-10">
                    <table style="width: 100%; border-collapse: collapse">
                        <thead style="border-bottom: 2px solid #8b8b8b">
                        <tr>
                            <td class="black-text" style="padding-bottom: 5px">
                                <strong style="padding-left: 10px"> Description </strong>
                            </td>
                            <td class="black-text" style="text-align: right; width: 160px; padding-bottom: 5px">
                                <strong style="padding-right: 10px"> Price ex. VAT (DKK) </strong>
                            </td>
                        </tr>
                        </thead>
                        <tbody style="border-bottom: 2px solid #8b8b8b">
                        <tr v-for="index in 10">
                            <td>
                                <p
                                        style="
                                        margin: 0;
                                        margin-top: 3px;
                                        color: #bbbbbb;
                                        margin-bottom: 10px;
                                        padding-left: 10px;
                                    "
                                        :style="`${index === 0 ? 'margin-top: 10px' : ''}`"
                                >
                                    DK >DK - GLS Denmark - DropOff to Parcel Shop 0.00 - 0.49 kg
                                </p>
                            </td>
                            <td style="text-align: right; color: #bbbbbb">
                                <p
                                        style="margin: 0;  margin-top: 3px; padding-right: 10px;margin-bottom: 10px"
                                        :style="`${index === 0 ? 'margin-top: 10px' : ''}`"
                                >
                                    36,75
                                </p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="invoice-footer">
                    <div class="footer-info">
                        <div style="text-align: center">
                            <div style="line-height: 8px;" v-html="footer">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </el-dialog>
</template>
<script>
    import {templateService} from "@/services/_singletons";

    export default {
        name: "AgreementTemplate",
        props: {
            summary: {
                required: false,
                default: ''
            }
        },
        watch: {
            summary(newVal, oldVal) {

            },
            mailTemplate(newVal, oldVal) {
                this.$emit('close', newVal)
            }
        },
        data() {
            return {
                mailTemplate: true,
                preview: {
                    video_url: '',
                },
                footer: ''
            }
        },
        methods: {
            async getFooter() {
                const res = await templateService.getTemplateSummary({'type': 'FOOTER'})
                if (res.data.template) {
                    this.footer = res.data.template.summary;
                }
            }
        },
        created() {
            this.getFooter()
        }
    }
</script>
<style lang="scss" scoped>
    :deep(.el-table) {
        thead {
            .cell {
                color: #000;
            }
        }
    }

    .download-pdf-button-wrapper {
        position: absolute;
        bottom: -70px;
        left: 50%;
        transform: translate(-50%);
    }
</style>
