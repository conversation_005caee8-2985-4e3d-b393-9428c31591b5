<template>
  <el-dialog title="" v-model="mailTemplate" class="p-0">
    <div class="card-base card-shadow--medium search-card scrollable only-y mt-2 p-10">
      <el-form ref="form" class="mb-50 bb-br-gray" label-width="120px">
        <div id="template">
          <div style="padding: 0 20px" class="stack-column">
            <table cellspacing="0" cellpadding="0" border="0" width="100%">
              <tr>
                <td style="padding: 20px 0px">
                  <table
                      cellspacing="0"
                      cellpadding="0"
                      border="0"
                      width="100%"
                      style="font-size: 14px; text-align: left"
                  >
                    <tr>
                      <td>
                        <div>
                          <img
                              style="width: 130px"
                              src="@/assets/images/Logo2.png"
                              alt="img"
                          />
                        </div>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </div>

          <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              width="100%"
              class="data-tbl"
              style="
                                background: url('/src/assets/images/banner-bg.png');
                                padding: 20px 0;
                                border-radius: 20px;
                            "
          >
            <tr>
              <td>
                <div style="padding: 32px; font-size: 16px">
<!--                  <h1 style="text-align: center; font-size: 28px; line-height: 26px">
                    Welcome To <span style="color: #1476cc">Shipvagoo</span>
                  </h1>-->
                  <span>Velkommen
                                            <h3 style="display: inline; font-size: 22px">XYZ</h3></span
                  >
                  <p style="margin-bottom: 10px">
                    <span v-html="summary"></span>
                  </p>
                  <!--                  <p style="margin-bottom: 0">Regards,</p>
                                    <p style="margin: 0">Shipvagoo Team</p>-->
                </div>
              </td>
            </tr>
          </table>

          <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              width="100%"
              class="data-tbl"
              style="padding: 20px 0"
          >
            <tr>
              <td>
                <ul
                    style="
                                            list-style: none;
                                            margin: 0;
                                            margin: 40px;
                                            padding: 0;
                                            border: 1px solid #ccc;
                                            border-radius: 10px;
                                        "
                >
                  <li
                      style="
                                                border-bottom: 1px solid #ccc;
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;
                                                padding: 30px;
                                            "
                  >
                    <div>
                      <h3
                          style="
                                                        color: #1476cc;
                                                        margin: 0;
                                                        font-size: 22px;
                                                        line-height: 26px;
                                                        display: flex;
                                                        align-items: center;
                                                    "
                      >
                        Din fragtaftale
                        <img
                            src="@/assets/images/parcel.png"
                            style="width: 30px; margin-left: 20px"
                            alt="icon"
                        />
                      </h3>
                      <p>
                        <a v-loading.fullscreen.lock="fullscreenLoading"
                           href="#" @click="onClickPreview()" style="color: #006efb; margin-right: 3px">Klik her</a>
                        for at se din fragtaftale
                      </p>
                    </div>
                    <a href="#" style="display: block"
                    ><img style="width: 55px" src="@/assets/images/pdf.png" alt=""/>
                    </a>
                  </li>
                  <li
                      style="
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;
                                                padding: 30px;
                                            "
                  >
                    <div>
                      <h3
                          style="
                                                        color: #1476cc;
                                                        margin: 0;
                                                        font-size: 22px;
                                                        line-height: 26px;
                                                        display: flex;
                                                        align-items: center;
                                                    "
                      >
                        Kom i gang
                        <img
                            src="@/assets/images/rocket.png"
                            style="width: 30px; margin-left: 20px"
                            alt="icon"
                        />
                      </h3>
                      <p>
                        <a
                            href="#"
                            style="color: #006efb; margin-right: 3px"
                        > Klik her</a
                        >
                        for at oprette din
                      </p>
                    </div>
                    <a style="display: block" href="#"
                    ><img style="width: 55px" src="@/assets/images/onborad.png" alt=""
                    /></a>
                  </li>
                </ul>
              </td>
            </tr>
          </table>

          <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              width="100%"
              class="data-tbl"
              style="padding: 20px 0"
          >
            <tr>
              <td>
                <div class="bg-sec">
                  <img :src="preview.video_thumbnail" alt="img"/>
                  <a
                      style="
                                                display: flex;
                                                align-items: center;
                                                max-width: 160px;
                                                padding: 12px 12px;
                                                background-color: #006efb;
                                                color: #eff6ff;
                                                border-radius: 30px;
                                                position: absolute;
                                                left: 50%;
                                                top: 50%;
                                                transform: translate(-50%, -50%);
                                                width: 100%;
                                                cursor: pointer;
                                            "
                      :href="preview.video_url" target="_blank"><img
                      src="@/assets/images/play.png"
                      style="width: 35px; margin-right: 10px"
                      alt=""
                  />
                    Watch Now</a
                  >
                </div>
              </td>
            </tr>
          </table>

          <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              width="100%"
              class="data-tbl"
              style="padding: 20px 0px"
          >
            <tr>
              <td>
                <div class="text-center mt-20" v-html="footer">

                </div>
              </td>
            </tr>
          </table>
        </div>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>

import {templateService} from "@/services/_singletons";
import {useHelper} from "@/composeable/Helper";

const {downloadTemplateAgreementPdf} = useHelper();
export default {
  name: "OnboardingTemplate",
  props: {
    summary: {
      required: false,
      default: ''
    },
    preview: {
      required: false,
      default: {}
    },
  },
  watch: {
    summary(newVal, oldVal) {

    },
    mailTemplate(newVal, oldVal) {
      this.$emit('close', newVal)
    }
  },
  data() {
    return {
      fullscreenLoading: false,
      mailTemplate: true,
      footer: 'This is the footer text and this is for dummy purposes. This is the footer text and this is for dummy purposes.\n' +
          '\n' +
          'This is footer text and this is for dummy purposes'
    }
  },
  methods: {
    async onClickPreview() {
      try {
        this.fullscreenLoading = true
        await downloadTemplateAgreementPdf()
      } catch (e) {
      } finally {
        this.fullscreenLoading = false;
      }
    },
    async getFooter() {
      const res = await templateService.getTemplateSummary({'type': 'FOOTER'})
      if (res.data.template) {
        this.footer = res.data.template.summary;
      }
    }
  },
  created() {
    this.getFooter()
  }
}
</script>

<style scoped>
html,
body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  width: 100% !important;
  overflow-x: hidden;
  font-family: "Montserrat", sans-serif;
  color: #313131;
}

#template {
  max-width: 1140px;
}

p {
  font-family: "Nunito Sans", sans-serif;
}

/* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
table {
  border-spacing: 0 !important;
  border-collapse: collapse !important;
  table-layout: fixed !important;
  margin: 0 auto !important;
}

table table table {
  table-layout: auto;
}

.footer-list {
  margin: 40px;
  padding: 0;
  text-align: center;
  font-family: "Nunito Sans", sans-serif;
  list-style: decimal;
}

.footer-list li {
  display: inline-block;
  margin: 0 10px;
  position: relative;
}

.footer-list li::after {
  position: absolute;
  content: "";
  width: 6px;
  height: 6px;
  background-color: #313131;
  top: 8px;
  right: -15px;
  border-radius: 50%;
}

.footer-list li:last-of-type::after {
  display: none;
}

.footer-list li a {
  text-decoration: none;
  color: #313131;
}

.bg-sec {
  margin: 0 40px;
  position: relative;
}

.bg-sec img {
  display: block;
  height: auto;
  width: 100%;
}

#template {
  margin: auto;
}

@media screen and (max-width: 1200px) {
  #template {
    max-width: 100%;
  }
}

@media screen and (max-width: 580px) {
  h1 {
    font-size: 18px !important;
  }

  h3 {
    font-size: 16px !important;
  }

  p {
    font-size: 14px !important;
  }
}


.form-box .el-form-item__label {
  width: 100px !important;
}

</style>