<template>
    <div style="height:0;overflow: hidden">
        <div
                id="transactions-pdf-content"
                aria-hidden
                style="position: relative; width: 2480px; min-height: 3507px"
        >
            <div style="display: flex; justify-content: end; width: 100%">
                <h1 style="font-size: 80px; color: #243E90;font-weight: 400; letter-spacing: 4px;">Payment
                    Confirmation</h1>
            </div>
            <div style="width: 100%; height: 3px; background-color:#CCCCCC;margin-top:60px;"></div>
            <div
                    style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding-top: 100px;
        "
            >
                <div style="font-size: 45px; color: #222234">
                    <div style="color:#243E90">
                        {{transactionToPrint.company_name}}
                    </div>
                    <div>{{transactionToPrint.address}}</div>
                    <div>
                        <div>{{transactionToPrint.iso}}</div>
                        -
                        <div>{{transactionToPrint.zipCode}}</div>
                        <div>{{transactionToPrint.city}}</div>
                    </div>
                    <div>
                        VAT No: {{transactionToPrint.vat_no}}
                    </div>
                </div>
                <div
                        style="
            display: flex;
            min-width: 620px;
            flex-direction: row;justify-content: space-between;
           "
                >
                    <div>
                        <div style="font-size: 40px; color: #222234;font-weight: 700;">Date:</div>
                        <div style="font-size: 40px; color: #222234;font-weight: 700;">Reference No:</div>
                        <div style="font-size: 40px; color: #222234;font-weight: 700;">Customer No:</div>
                    </div>
                    <div>
                        <div style="font-size: 40px; ">{{ transactionToPrint.created_at.split(" ")[0] }}
                        </div>
                        <div style="font-size: 40px; "></div>
                        <div style="font-size: 40px; ">3434339943433</div>
                    </div>
                </div>
            </div>
            <table style="width: 100%; margin-top: 135px; border-collapse: collapse">
                <thead>
                <tr style="font-size: 35px;height: 120px;color:#757575;">
                    <th
                            style="
              border-radius: 15px 0  0 15px;
              font-weight: 500;
               background-color: #F5F5F5;
                width: 34%;
                padding-left: 40px;
                text-align: left;
              "
                    >
                        Description
                    </th>
                    <th
                            style="
              font-weight: 500;
                width: 33%;
                padding-bottom: 16px;
                text-align: center;
                background-color: #F5F5F5;
              "
                    >
                        Transaction No
                    </th>
                    <th
                            style="
               border-radius: 0 15px 15px 0 ;
              font-weight: 500;
                width: 33%;
                padding-right: 40px;
                padding-bottom: 16px;
                text-align: right;
                 background-color: #F5F5F5;
              "
                    >
                        Total (DKK)
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr style="font-size: 38px">
                    <td style="padding-left: 40px">
                        {{transactionToPrint.description}}
                    </td>
                    <td style="text-align: center; padding-right: 40px;">
                        {{transactionToPrint.transaction_code}}
                    </td>
                    <td style="text-align: right; padding-right: 40px;">
                        DKK {{danishNumberFormat(transactionToPrint.amount)}}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!--<div class="customer-agreement-dialog position-relative">
        <section id="customer-invoice" ref="DownloadComp">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%;"><h2 class="text-black">Receipt for Payment</h2>
                    </td>
                    <td style="width: 50%;">
                        <div class="logo-wrapper logo" style="text-align: right;">
                            <img src="@/assets/images/logo-black.svg" class="logo" alt="logo"/>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="user-info">
                <h4 class="m-0 black-text" style="margin: 5px 0">{{ data.company_name }}</h4>
                <p class="m-0 black-text" style="margin: 5px 0">{{ data.address }}</p>
            </div>
            <div style="margin-top:50px;">
                &lt;!&ndash;        <strong class="m-0 black-text">No. 000{{ data.id }}</strong>&ndash;&gt;
                <p class="m-0 black-text">Date: {{ date }}</p>
            </div>

            <div class="table mt-50">
                <table>
                    <tr>
                        <td style="width:120px;">{{ type == 'invoice' ? 'Invoice No.' : 'Transaction ID'}}</td>
                        <td style="text-align: right">{{ data.transaction_code }}</td>
                    </tr>
                    <tr>
                        <td style="width:120px;">Description</td>
                        <td style="text-align: right">{{ data.description }}</td>
                    </tr>
                    &lt;!&ndash;          <tr>
                                <td style="width:120px;">Transaction No.</td>
                                <td style="text-align: right">000{{ data.id }}</td>
                              </tr>&ndash;&gt;
                    <tr>
                        <td style="width:120px;"><strong>Total</strong></td>
                        <td style="text-align: right"><strong>DKK {{ danishNumberFormat(data.amount) }}</strong></td>
                    </tr>
                </table>
            </div>
            <div class="invoice-footer">
                <div class="footer-info">
                    <div style="text-align: center">
                        <div v-html="data.footer"></div>

                    </div>
                </div>
            </div>
        </section>
        &lt;!&ndash;    <div class="download-pdf-button-wrapper">
              <el-button class="btn-blue-bg" @click="printDownload">Download PDF</el-button>
            </div>&ndash;&gt;
    </div>-->
</template>
<script>
    import {danishNumberFormat} from "@/helpers";
    import JsPDF from "jspdf";
    import {nextTick} from 'vue';
    import Html2Canvas from "html2canvas";
    import dayjs from 'dayjs';

    export default {
        name: "PaymentReceipt",
        props: {
            transactionToPrint: {
                type: Array,
                default: {}
            },
            type: {
                required: false,
                default: 'transaction'
            }
        },
        watch: {
            transactionToPrint(newVal, oldVal) {
                console.log("Newval ", newVal)
                this.printPdf();
            }
        },
        data() {
            return {}
        },
        computed: {
            date() {
                return this.data.created_at//moment(new Date(this.data.created_at)).format("DD-MM-yyyy")//.split(" ")[0]
            }
        },
        methods: {
            dayjs,
            danishNumberFormat,
            async printPdf() {
                await nextTick();

                const el = document.getElementById('transactions-pdf-content');
                const logo = document.querySelector('.pdf-logo');

                if (!this.transactionToPrint || !el || !logo || !(logo instanceof HTMLImageElement)) {
                    console.log('abc..')
                    return;
                }

                const PAGE_WIDTH_MM = 195//210
                const DESIGN_WIDTH = 2480
                const SCALE_MULTIPLIER = PAGE_WIDTH_MM / DESIGN_WIDTH;

                const logoCanvas = await Html2Canvas(logo, {
                    useCORS: true,
                });

                const jsPDFInstance = new JsPDF('p', 'mm', 'a4');

                await jsPDFInstance.html(el, {
                    width: 195,
                    html2canvas: {
                        scale: SCALE_MULTIPLIER,
                        useCORS: true,
                    },
                    //margin: [5, 0, 20, 0],
                });

                jsPDFInstance.addImage(
                    logoCanvas.toDataURL('image/png'),
                    'PNG',
                    9,
                    //210 - 662.88 * SCALE_MULTIPLIER - 150 * SCALE_MULTIPLIER,
                    150 * SCALE_MULTIPLIER,
                    662.88 * SCALE_MULTIPLIER,
                    106 * SCALE_MULTIPLIER,
                );

                /* const pages = jsPDFInstance.internal.getNumberOfPages();
                 const pageWidth = jsPDFInstance.internal.pageSize.width;  //Optional
                 const pageHeight = jsPDFInstance.internal.pageSize.height;  //Optional
                 jsPDFInstance.setFontSize(10);  //Optional
                 for (let j = 1; j < pages + 1; j++) {
                     let horizontalPos = pageWidth / 2;  //Can be fixed number
                     let verticalPos = pageHeight - 10;  //Can be fixed number
                     jsPDFInstance.setPage(j);
                     jsPDFInstance.text(`${j} of ${pages}`, horizontalPos, verticalPos, {
                         align: 'center'
                     })
                 }*/
                jsPDFInstance.save(`123.pdf`);
            },
        },
        mounted() {
            console.log(this.transactionToPrint)
            this.printPdf();
        }
    }
</script>
<style lang="scss" scoped>
    .invoice-footer {
        bottom: 0;
        position: absolute;
        display: flex;
        justify-content: center;
        width: 100%;
    }

    #transactions-pdf-content {
        padding: 100px;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
        Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        color: black !important;
        letter-spacing: 0.01px !important;
        background-color: white;

        h1 {
            margin: 0;
        }
    }
</style>
