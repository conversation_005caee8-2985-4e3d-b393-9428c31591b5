<template>
    <div style="height:0;overflow: hidden">
        <div
                id="invoices-pdf-content"
                aria-hidden
                style="position: relative; width: 2480px; min-height: 3507px"
        >
            <div style="display: flex; justify-content: end; width: 100%">
                <h1 style="font-size: 90px; color: #243E90;font-weight: 400;">Invoice</h1>
            </div>
            <div style="width: 100%; height: 3px; background-color:#CCCCCC;margin-top:60px;"></div>
            <div
                    style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding-top: 100px;
        "
            >
                <div style="font-size: 50px; color: #222234">
                    <div style="color:#243E90">
                        -Company name
                    </div>
                    <div>-Address</div>
                    <div>
                        -ISO - ZipCode
                        -City
                    </div>
                    <div>
                        VAT No: -VAT NO
                    </div>
                </div>
                <div
                        style="
            display: flex;
            min-width: 620px;
            flex-direction: row;justify-content: space-between;
           "
                >
                    <div>
                        <div style="font-size: 45px; color: #222234;font-weight: 700;">Date:</div>
                        <div style="font-size: 45px; color: #222234;font-weight: 700;">Invoice No:</div>
                        <div style="font-size: 45px; color: #222234;font-weight: 700;">Customer No:</div>
                        <div style="font-size: 45px; color: #222234;font-weight: 700;">Shipment ID:</div>
                        <div style="font-size: 45px; color: #222234;font-weight: 700;">Status:</div>
                    </div>
                    <div>
                        <div style="font-size: 45px; ">-23-03-2023</div>
                        <div style="font-size: 45px; ">33434343333</div>
                        <div style="font-size: 45px; ">33434343333</div>
                        <div style="font-size: 45px; ">33434343333</div>
                        <div style="font-size: 45px; ">PAID</div>
                    </div>
                </div>
            </div>


            <table style="width: 100%; margin-top: 145px; border-collapse: collapse">
                <thead>
                <tr style="font-size: 45px;height: 120px;color:#757575;">
                    <th
                            style="
              border-radius: 15px 0  0 15px;
              font-weight: 500;
               background-color: #F5F5F5;
                width: 40%;
                padding-left: 45px;
                text-align: left;
              "
                    >
                        Description
                    </th>
                    <th
                            style="
              font-weight: 500;
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                 background-color: #F5F5F5;
              "
                    >
                        Quantity
                    </th>
                    <th
                            style="
              font-weight: 500;
                width: 20%;
                padding-bottom: 16px;
                text-align: right;
                background-color: #F5F5F5;
              "
                    >
                        Unit Price
                    </th>
                    <th
                            style="
               border-radius: 0 15px 15px 0 ;
              font-weight: 500;
                width: 20%;
                padding-right: 40px;
                padding-bottom: 16px;
                text-align: right;
                 background-color: #F5F5F5;
              "
                    >
                        Amount
                    </th>
                </tr>
                </thead>
                <tbody v-if="invoiceToPrint.invoice_items && invoiceToPrint.invoice_items.length > 0">
                <tr>
                    <td style="font-size: 40px; font-weight: 700;padding:25px 0 25px 40px;">
                        Shipments
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="4" style="height: 3px;background-color: #CCCCCC;">
                    </td>
                </tr>
                <!-- <template
                         v-for="[key, invoiceItems] in Object.entries(
               _.groupBy(
                 invoiceToPrint.invoice_items.filter((i) => i.type === 'Shipments'),
                 'country_pair',
               ),
             )"
                         :key="key"
                 >
                     <tr>
                         <td style="padding-left: 45px; font-size: 45px;padding-top: 25px">({{ key }})</td>
                         <td></td>
                         <td></td>
                         <td></td>
                     </tr>
                     <template v-for="invoiceItem in invoiceItems">
                         <tr
                                 v-for="item in invoiceItem.items"
                                 :key="`${item.weight_class}-${item.count}-${item.unit_price}`"
                                 style="font-size: 45px"
                         >
                             <td style="padding-left: 70px">{{ item.weight_class }}</td>
                             <td style="text-align: right">{{ item.count }}</td>
                             <td style="text-align: right">
                                 {{
                                 new Intl.NumberFormat('dk-DK', {
                                 minimumFractionDigits: 2,
                                 maximumFractionDigits: 2,
                                 }).format(Number(item.unit_price))
                                 }}
                             </td>
                             <td style="padding-right: 20px; text-align: right">
                                 {{ DecimalService.toDecimal(Number(item.unit_price) * Number(item.count)) }}
                             </td>
                         </tr>
                     </template>
                 </template>
                 <template
                         v-for="additionalServices in invoiceToPrint.invoice_items.filter(
               (i) => i.type === 'Additional Services',
             )"
                         :key="additionalServices.items"
                 >
                     <tr v-if="additionalServices.items.some((i) => parseFloat(i.unit_price) > 0)">
                         <td style="font-size: 45px; font-weight: 700;padding:25px 0 20px 45px;">
                             Shipvagoo Products
                         </td>
                         <td></td>
                         <td></td>
                         <td></td>
                     </tr>
                     <tr>
                         <td colspan="4" style="height: 3px;background-color: #CCCCCC;">
                         </td>
                     </tr>
                     <tr
                             v-for="item in additionalServices.items.filter((i) => parseFloat(i.unit_price) > 0)"
                             :key="`${item.name}-${item.count}-${item.unit_price}`"
                             style="font-size: 45px"
                     >
                         <td style="padding-left: 45px; font-size: 45px;padding-top: 15px">{{ item.name }}</td>
                         <td style="text-align: right">{{ item.count }}</td>
                         <td style="text-align: right">
                             {{ DecimalService.toDecimal(Number(item.unit_price)) }}
                         </td>
                         <td style="padding-right: 20px; text-align: right">
                             {{ DecimalService.toDecimal(Number(item.unit_price) * Number(item.count)) }}
                         </td>
                     </tr>
                 </template>-->
                </tbody>
            </table>

            <table style="width: 100%; margin-top: 40px; border-collapse: collapse">
                <thead>
                <tr style="font-size: 40px;height: 120px;color:#757575;">
                    <th
                            style="
              border-radius: 15px 0  0 15px;
              font-weight: 500;
               background-color: #F5F5F5;
                width: 30%;
                padding-left: 40px;
                text-align: left;
              "
                    >
                        Amount
                    </th>
                    <th
                            style="
               font-weight: 500;
               background-color: #F5F5F5;
                width: 23%;
                text-align: center;
              "
                    >
                        VAT
                    </th>
                    <th
                            style="
               font-weight: 500;
               background-color: #F5F5F5;
                width: 23%;
                text-align: center;
              "
                    >
                        VAT Amount
                    </th>
                    <th
                            style="
               font-weight: 500;
               background-color: #F5F5F5;
                width: 24%;
                padding-right: 40px;
                text-align: right;
                  border-radius: 0 15px 15px 0 ;
              "
                    >
                        Total Inc. VAT(DKK)
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr style="font-size: 40px;">
                    <td style="text-align: left;padding-left: 40px;">
                        333,33.00
                    </td>
                    <td style="text-align: center">
                        25%
                    </td>
                    <td style="text-align: center">
                        333,34.00
                    </td>
                    <td style="padding-right: 40px;text-align: right">
                        3333.333,0
                    </td>
                </tr>
                </tbody>
            </table>
            <pdf-footer/>
        </div>
    </div>
</template>

<script>
    import {nextTick} from 'vue';
    import dayjs from 'dayjs';
    import Html2Canvas from "html2canvas";
    import JsPDF from "jspdf";

    export default {
        props: {
            invoiceToPrint: {
                required: true,
            }
        },
        data() {
            return {}
        },
        watch: {
            invoiceToPrint(newVal, oldVal) {
                this.printInvoice()
            }
        },
        methods: {
            async printInvoice() {
                await nextTick();

                const el = document.getElementById('invoices-pdf-content');
                const logo = document.querySelector('.pdf-logo');

                if (
                    !this.invoiceToPrint || !el || !logo || !(logo instanceof HTMLImageElement)
                ) {
                    return;
                }

                const PAGE_WIDTH_MM = 185;//210
                const DESIGN_WIDTH = 2480;
                const SCALE_MULTIPLIER = PAGE_WIDTH_MM / DESIGN_WIDTH;

                const logoCanvas = await Html2Canvas(logo, {
                    useCORS: true,
                });

                const jsPDFInstance = new JsPDF('p', 'mm', 'a4');

                await jsPDFInstance.html(el, {
                    width: 185,
                    html2canvas: {
                        scale: SCALE_MULTIPLIER,
                        useCORS: true,
                    },
                });

                jsPDFInstance.addImage(
                    logoCanvas.toDataURL('image/png'),
                    'PNG',
                    11,
                    //210 - 662.88 * SCALE_MULTIPLIER - 150 * SCALE_MULTIPLIER,
                    180 * SCALE_MULTIPLIER,
                    662.88 * SCALE_MULTIPLIER,
                    106 * SCALE_MULTIPLIER,
                );
                jsPDFInstance.save(`invoice-123.pdf`);
            }
        },
        mounted() {
            if (this.invoiceToPrint) {
                this.printInvoice();
            }
        }
    }


</script>

<style scoped lang="scss">
    #invoices-pdf-content {
        padding: 145px;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
        Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        color: black !important;
        letter-spacing: 0.01px !important;
        background-color: white;

        h1 {
            margin: 0;
        }
    }


</style>