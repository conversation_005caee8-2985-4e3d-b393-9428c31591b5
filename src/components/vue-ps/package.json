{"name": "vue-ps", "version": "0.1.0", "description": "", "main": "dist/index.js", "scripts": {"build": "cross-env NODE_ENV=production webpack --progress --hide-modules --config build-webpack.conf.js", "example": "cross-env NODE_ENV=development webpack-dev-server --open --inline --hot --config example-webpack.conf.js", "example:vuerouter": "cross-env NODE_ENV=development webpack-dev-server --open --inline --hot --config example-vuerouter-webpack.conf.js"}, "repository": {"type": "git", "url": "https://github.com/Linko91/vue-ps"}, "author": "", "license": "MIT", "dependencies": {"perfect-scrollbar": "^1.3.0"}, "files": ["dist", "index.js", "index.vue"], "keywords": ["scroll", "scrollbar", "perfect-scroll", "vue-ps", "vue-scroll", "vue-scrollbar", "vue-plugin", "frontend"], "devDependencies": {"babel-core": "^6.0.0", "babel-loader": "^6.0.0", "babel-preset-es2015": "^6.0.0", "cross-env": "^3.0.0", "css-loader": "^0.25.0", "file-loader": "^0.9.0", "node-sass": "^4.5.3", "sass-loader": "^4.1.1", "scss-loader": "0.0.1", "style-loader": "^0.13.1", "vue": "^2.4.2", "vue-loader": "^10.3.0", "vue-router": "^2.7.0", "vue-style-loader": "^1.0.0", "vue-template-compiler": "^2.3.3", "webpack": "^2.5.1", "webpack-dev-server": "^2.4.5"}}