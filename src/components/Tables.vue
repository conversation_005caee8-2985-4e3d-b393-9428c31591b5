<template>
    <div class="page-table scrollable only-y" id="affix-container">
        <div class="table-box card-base card-shadow--medium scrollable only-x">
            <table class="css-serial styled striped">
                <thead>
                <tr class="Font-Size1">
                    <th style="width:15%">ID</th>
                    <th style="width:30%">Country From</th>
                    <th style="width:30%">Country To</th>
                    <th style="width:25%">Parcel per year</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="agreement in agreements" :key="agreement.id">
                    <td></td>
                    <td>{{ agreement.country_from }}</td>
                    <td>{{ agreement.country_to }}</td>
                    <td>{{ danishNumberFormat(agreement.parcels) }}</td>
                </tr>
                </tbody>
            </table>
            <div class="flex">
                <div class="col-9">
                    <div class="parcel-per-year "><b>Total Parcel Per year: </b></div>
                </div>
                <div class="col-3">
                    <div class="parcel-per-year"><b class="ml-7">{{ danishNumberFormat(totalParcelCount(agreements))
                        }}</b></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Affix from "@/components/Affix.vue"
    import {defineComponent} from "@vue/runtime-core"
    import {ref, watch} from 'vue';
    import {danishNumberFormat} from "@/helpers";

    export default defineComponent({
        name: "Table",
        props: {
            agreements: {
                type: Array,
                required: true
            }
        },
        setup(props) {
            const agreements = ref(props.agreements);
            watch(() => props.agreements, (currentVal, oldVal) => {
                agreements.value = currentVal;
            })
            return {
                affixEnabled: true,
                agreements
            }
        },
        components: {
            Affix
        },
        methods: {
            danishNumberFormat,
            totalParcelCount(agreements) {
                let parcelsCount = 0;
                agreements?.map((agreement, i) => {
                    parcelsCount += agreement.parcels;
                })
                return parcelsCount
            }
        }
    })
</script>

<style lang="scss" scoped>
    @import "@/assets/scss/_variables";

    .page-table {
        padding-left: 20px;
        padding-right: 15px;
        padding-bottom: 20px;
    }

    .table-box {
        overflow: auto;
    }

    /* Automatic Serial Number Row */
    .css-serial {
        counter-reset: serial-number; /* Set the serial number counter to 0 */
    }

    .css-serial td:first-child:before {
        counter-increment: serial-number; /* Increment the serial number counter */
        content: counter(serial-number); /* Display the counter */
    }

    .parcel-per-year {
        margin-top: 15px;
        padding: 15px;
    }

    .Font-Size1 {
        font-size: 14px !important;
    }
</style>
