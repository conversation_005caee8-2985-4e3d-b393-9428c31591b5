<template>
  <el-input v-model="query" class="w-50" :placeholder="placeholder" @input="search">
    <template #prefix>
      <el-icon class="el-input__icon">
        <i class="mdi mdi-magnify mdi-icon-size"></i>
      </el-icon>
    </template>
  </el-input>
</template>

<script>
export default {
  name: "SearchBox",
  props: {
    placeholder: {
      required: false,
      default: 'Search...'
    }
  },
  data() {
    return {
      query: '',
      timer: 0
    }
  },
  methods: {
    search() {
      clearInterval(this.timer);
      this.timer = setTimeout(() => {
        this.$emit('search', this.query)
      }, 1500)
    }
  }
}
</script>

<style scoped>

</style>