{"name": "pragmatic", "version": "5.0.1", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "test:unit": "vitest --environment jsdom", "test:e2e": "start-server-and-test preview http://localhost:4173/ 'cypress open --e2e'", "test:e2e:ci": "start-server-and-test preview http://localhost:4173/ 'cypress run --e2e'", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^36.0.0", "@ckeditor/ckeditor5-editor-classic": "^36.0.0", "@ckeditor/ckeditor5-vue": "^4.0.1", "@element-plus/icons-vue": "^2.0.9", "@fullcalendar/core": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/timegrid": "^5.11.3", "@fullcalendar/vue3": "^5.11.2", "@mdi/font": "^7.0.96", "@types/webpack-env": "^1.18.0", "@vue-leaflet/vue-leaflet": "^0.6.1", "animate.css": "^4.1.1", "axios": "^1.2.0", "balloon-css": "^1.2.0", "chance": "^1.1.8", "cryptocoins-icons": "^2.9.0", "dayjs": "^1.11.7", "detect-browser": "^5.3.0", "drift-zoom": "^1.5.0", "echarts": "^5.3.3", "element-plus": "^2.2.16", "file-saver": "^2.0.5", "flag-icon-css": "^4.1.7", "flex.box": "^3.4.4", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "leaflet": "^1.8.0", "lodash": "^4.17.21", "mapbox-gl": "^2.10.0", "marquee-infinite": "^0.0.4", "mavon-editor": "^3.0.0-beta", "moment": "^2.29.4", "papaparse": "^5.3.2", "parse-full-name": "^1.2.6", "pdf-lib": "^1.17.1", "pell": "^1.0.6", "perfect-scrollbar": "^1.5.5", "pinia": "^2.0.22", "pinia-plugin-persistedstate": "^2.2.0", "quill": "^1.3.7", "tui-grid": "^4.21.4", "v-click-outside": "^3.2.0", "v-viewer": "3.0.10", "vee-validate": "^4.7.3", "vue": "^3.2.39", "vue-chartkick": "^1.1.0", "vue-fullscreen": "^2.6.1", "vue-html2pdf": "^1.8.0", "vue-i18n": "^9.2.2", "vue-papa-parse": "^3.1.0", "vue-router": "^4.1.5", "vue-virtual-collection": "^1.5.0", "vue2-timeago": "^2.0.9", "vue3-highlightjs": "^1.0.5", "yup": "^0.32.11"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@types/jsdom": "^20.0.0", "@types/node": "^18.7.16", "@vitejs/plugin-vue": "^3.1.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.1", "@vue/test-utils": "^2.0.2", "@vue/tsconfig": "^0.1.3", "eslint": "^8.23.0", "eslint-plugin-vue": "^9.4.0", "jsdom": "^20.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "sass": "^1.54.9", "start-server-and-test": "^1.14.0", "typescript": "~4.8.3", "url": "^0.11.0", "vite": "^3.1.0", "vitest": "^0.23.2", "vue-tsc": "^0.40.13", "stylelint-recommended-conf": "^1.5.0"}}