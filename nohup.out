
> pragmatic@5.0.0 dev
> vite


  VITE v3.2.4  ready in 396 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
DEPRECATION WARNING on line 166, column 30 of node_modules/flex.box/src/flexbox.scss: 
This operation is parsed as:

    & + "."

but you may have intended it to mean:

    & (+".")

Add a space after + to clarify that it's meant to be a binary operation, or wrap
it in parentheses to make it a unary operation. This will be an error in future
versions of Sass.

More info and automated migrator: https://sass-lang.com/d/strict-unary
    ╷
166 │     $grids: append($grids, #{& +"."+ $flexbox-grid-selector + $grid}, "comma");
    │                              ^^^^^^
    ╵

DEPRECATION WARNING on line 167, column 28 of node_modules/flex.box/src/flexbox.scss: 
This operation is parsed as:

    & + " > ."

but you may have intended it to mean:

    & (+" > .")

Add a space after + to clarify that it's meant to be a binary operation, or wrap
it in parentheses to make it a unary operation. This will be an error in future
versions of Sass.

More info and automated migrator: https://sass-lang.com/d/strict-unary
    ╷
167 │     $cols: append($cols, #{& +" > ."+ $flexbox-col-selector + $grid}, "comma");
    │                            ^^^^^^^^^
    ╵

7:36:35 AM [vite] hmr update /src/views/ui/Layout/LayoutTabbed.vue
7:36:35 AM [vite] hmr update /src/views/ui/Themes.vue
7:36:35 AM [vite] hmr update /src/views/ui/Layout/LayoutTabbed.vue
7:36:35 AM [vite] hmr update /src/views/pages/Profile.vue
hmr update /src/views/pages/Invoice.vue
hmr update /src/components/Profile/ProfileTimeline.vue
7:36:35 AM [vite] hmr update /src/views/pages/Profile.vue
7:36:35 AM [vite] hmr update /src/views/ui/Layout/LayoutSidebarRight.vue
hmr update /src/views/ui/Layout/LayoutSidebarLeft.vue
hmr update /src/views/ui/Layout/LayoutTabbed.vue
7:36:35 AM [vite] hmr update /src/views/ui/Themes.vue
7:36:35 AM [vite] hmr update /src/views/ui/Themes.vue
7:36:35 AM [vite] hmr update /src/views/ui/Themes.vue
7:36:35 AM [vite] changed tsconfig file detected: /var/www/admin-vue/cypress/e2e/tsconfig.json - Clearing cache and forcing full-reload to ensure TypeScript is compiled with updated config values.
7:36:50 AM [vite] .env changed, restarting server...
7:36:50 AM [vite] server restarted.

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
7:37:13 AM [vite] .env changed, restarting server...
7:37:13 AM [vite] server restarted.

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
DEPRECATION WARNING on line 166, column 30 of node_modules/flex.box/src/flexbox.scss: 
This operation is parsed as:

    & + "."

but you may have intended it to mean:

    & (+".")

Add a space after + to clarify that it's meant to be a binary operation, or wrap
it in parentheses to make it a unary operation. This will be an error in future
versions of Sass.

More info and automated migrator: https://sass-lang.com/d/strict-unary
    ╷
166 │     $grids: append($grids, #{& +"."+ $flexbox-grid-selector + $grid}, "comma");
    │                              ^^^^^^
    ╵

DEPRECATION WARNING on line 167, column 28 of node_modules/flex.box/src/flexbox.scss: 
This operation is parsed as:

    & + " > ."

but you may have intended it to mean:

    & (+" > .")

Add a space after + to clarify that it's meant to be a binary operation, or wrap
it in parentheses to make it a unary operation. This will be an error in future
versions of Sass.

More info and automated migrator: https://sass-lang.com/d/strict-unary
    ╷
167 │     $cols: append($cols, #{& +" > ."+ $flexbox-col-selector + $grid}, "comma");
    │                            ^^^^^^^^^
    ╵

7:40:56 AM [vite] .env changed, restarting server...
7:40:56 AM [vite] server restarted.

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
DEPRECATION WARNING on line 166, column 30 of node_modules/flex.box/src/flexbox.scss: 
This operation is parsed as:

    & + "."

but you may have intended it to mean:

    & (+".")

Add a space after + to clarify that it's meant to be a binary operation, or wrap
it in parentheses to make it a unary operation. This will be an error in future
versions of Sass.

More info and automated migrator: https://sass-lang.com/d/strict-unary
    ╷
166 │     $grids: append($grids, #{& +"."+ $flexbox-grid-selector + $grid}, "comma");
    │                              ^^^^^^
    ╵

DEPRECATION WARNING on line 167, column 28 of node_modules/flex.box/src/flexbox.scss: 
This operation is parsed as:

    & + " > ."

but you may have intended it to mean:

    & (+" > .")

Add a space after + to clarify that it's meant to be a binary operation, or wrap
it in parentheses to make it a unary operation. This will be an error in future
versions of Sass.

More info and automated migrator: https://sass-lang.com/d/strict-unary
    ╷
167 │     $cols: append($cols, #{& +" > ."+ $flexbox-col-selector + $grid}, "comma");
    │                            ^^^^^^^^^
    ╵

Could not open input file: artisan
