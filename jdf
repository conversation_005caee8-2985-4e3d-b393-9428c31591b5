[33mcommit 61426bca85cc69fa46cb06687c0faeeb4e2df597[m[33m ([m[1;36mHEAD -> [m[1;32mdev-main[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/development-usman[m[33m, [m[1;31morigin/dev-main[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32mmain[m[33m)[m
Merge: 20376c8 50621c2
Author: usman <<EMAIL>>
Date:   Thu Sep 7 18:40:32 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into main

[33mcommit 20376c843323c48d98a281522ffca57979d3e661[m
Author: usman <<EMAIL>>
Date:   Thu Sep 7 18:39:49 2023 +0500

    revert changes 809

[33mcommit 361ae4946108899274dd2e130c1c1ce14e53d490[m
Author: usman <<EMAIL>>
Date:   Thu Sep 7 17:22:05 2023 +0500

    task 809 fixes

[33mcommit 50621c28c4413514890f475d203ac1baf0d9cb54[m
Merge: b75584b 166e5d0
Author: root <<EMAIL>>
Date:   Thu Sep 7 10:48:23 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit b75584b0ff705bf9904bd069a91de529881e8a3f[m[33m ([m[1;32mdevelopment-usman[m[33m)[m
Author: usman <<EMAIL>>
Date:   Thu Sep 7 15:04:35 2023 +0500

    changes in agreement discount

[33mcommit 8a2749421bc451cae2d25b2e91d3897354f37214[m
Author: usman <<EMAIL>>
Date:   Thu Sep 7 12:32:33 2023 +0500

    country dropdown added in address settings

[33mcommit 166e5d0484e9aef14c5596da3024d60e4873d448[m
Author: Ali Khiljee <<EMAIL>>
Date:   Tue Sep 5 16:06:21 2023 +0500

    Update adminvueCI.yml

[33mcommit 32200febe13fda1c8de2da0aab8a1ba61fa57e8a[m
Author: usman <<EMAIL>>
Date:   Tue Sep 5 11:13:58 2023 +0500

    carrier_id_2 in addresses

[33mcommit adbe8c7576001d6a86c6de2d24bec7e7f00c47ea[m
Author: usman <<EMAIL>>
Date:   Mon Sep 4 16:58:06 2023 +0500

    fixes

[33mcommit a8d5646bfa0f7055d672ca46d3514ec99c92e8e2[m
Author: usman <<EMAIL>>
Date:   Fri Sep 1 15:17:41 2023 +0500

    address valid message and autofill in settings issue fixed

[33mcommit 76021fc041a0502940088e18dc5f1bc4806e8be5[m
Author: usman <<EMAIL>>
Date:   Wed Aug 30 14:46:47 2023 +0500

    t/c link updated

[33mcommit a3c229ac6dc63485e1fa9aa05c1b6a6bf50cbba1[m
Author: usman <<EMAIL>>
Date:   Tue Aug 29 18:42:19 2023 +0500

    terms and conditions link updated

[33mcommit 23c5b1334d8a155b68e35ae33adf4d5c3b97ad58[m
Author: usman <<EMAIL>>
Date:   Mon Aug 21 16:19:47 2023 +0500

    api integration 3rd step for shopify installation process

[33mcommit 1d0f7407d1c6b54eb7ca7534c38556a03377e939[m
Author: usman <<EMAIL>>
Date:   Thu Aug 17 14:40:02 2023 +0500

    2 digits fraction

[33mcommit 8875049c408884ff3580251eb7dd8fb0856d9de4[m
Author: usman <<EMAIL>>
Date:   Thu Aug 17 14:14:48 2023 +0500

    nav permission changes'

[33mcommit 3a8e69c534bab9efb167fa7e101fb05bfec43f88[m
Author: usman <<EMAIL>>
Date:   Thu Aug 17 11:17:22 2023 +0500

    amount fields alignment

[33mcommit e50a1106c49a297fc574eeb88f551ea077edb9e9[m
Author: usman <<EMAIL>>
Date:   Wed Aug 16 19:04:19 2023 +0500

    danish number format methods updated

[33mcommit efcdc3a29764cee206aa9dd11d4be2e53d52f186[m
Author: usman <<EMAIL>>
Date:   Wed Aug 16 15:58:47 2023 +0500

    code optimization

[33mcommit e3e4af7a3535c81fafc69ca054ea30b6f75d3a62[m
Author: usman <<EMAIL>>
Date:   Wed Aug 16 12:27:31 2023 +0500

    some onboarding response message and fixes

[33mcommit fdc40b90f369bb37357244754a93846ec9784986[m
Author: usman <<EMAIL>>
Date:   Fri Aug 11 15:49:52 2023 +0500

    some changes in template summary

[33mcommit 589fd5ca0faa1bc17b20c78ff7f72b0f11a234aa[m
Author: usman <<EMAIL>>
Date:   Wed Aug 9 17:29:14 2023 +0500

    guideline link added of public site for now

[33mcommit d178c6aba781fe2026259fdffaeb6df7ea5280ec[m
Author: usman <<EMAIL>>
Date:   Mon Aug 7 15:50:37 2023 +0500

    text align center for class quill-center

[33mcommit 39d71e6443256f4a207e6b343cc50a5b712f2dcc[m
Author: usman <<EMAIL>>
Date:   Fri Aug 4 17:36:31 2023 +0500

    admin vue preview on button click in template

[33mcommit 5d4b9e42d59c28b9529debb6cc1a5ed3a7552188[m
Author: usman <<EMAIL>>
Date:   Fri Aug 4 12:15:30 2023 +0500

    agreement pdf integrated

[33mcommit 925f4b9aa2e8cc8537d42d285c86052d525a7239[m
Author: usman <<EMAIL>>
Date:   Wed Aug 2 15:45:16 2023 +0500

    show balance in case of add funds

[33mcommit 388aab74cafcf6ddf5d3bab296d71a02a680de95[m
Author: usman <<EMAIL>>
Date:   Thu Jul 27 16:26:02 2023 +0500

    invoices tab added

[33mcommit b54f4543330051ee3ab1249a7c377ca9c5eb95e8[m
Author: usman <<EMAIL>>
Date:   Thu Jul 27 16:21:08 2023 +0500

    invoices tab added

[33mcommit baa29ad49fa2964bf81dc3360938d2b4363332a5[m
Author: usman <<EMAIL>>
Date:   Tue Jul 18 15:41:43 2023 +0500

    bin 4 digits substr

[33mcommit 6e8b22f009eb0f22607f0118f1bc20d4acb2b607[m
Author: usman <<EMAIL>>
Date:   Tue Jul 18 14:15:06 2023 +0500

    payment method column added in transactions and funds list

[33mcommit fe83e4d1a4e1744e375993c788cc905e80530be0[m
Author: usman <<EMAIL>>
Date:   Wed Jul 12 12:09:54 2023 +0500

    label changed shipvagoo terms

[33mcommit 29fc3225e2754f3eb53af6cb070fbfb27d1061cd[m
Author: usman <<EMAIL>>
Date:   Tue Jul 11 15:17:46 2023 +0500

    labels changed in api integraion tab, onborading-flow

[33mcommit 9806ad7cc535355f3ecb668c691711eede3992eb[m
Author: usman <<EMAIL>>
Date:   Tue Jul 11 15:13:38 2023 +0500

    labels changed in api integraion tab, onborading-flow

[33mcommit 974b00529ff48a8c7e87641beac96a1b0a2cb01c[m
Author: usman <<EMAIL>>
Date:   Mon Jul 10 13:12:31 2023 +0500

    task 768 done

[33mcommit b3984c5e97bf9cd8ea04ec5fdfac9ae452426559[m
Author: usman <<EMAIL>>
Date:   Fri Jul 7 10:39:30 2023 +0500

    iframe meta removed(which added previouslY)

[33mcommit 7aacf9633aa4ba044af7c7abc3ea8d682bc47bed[m
Author: usman <<EMAIL>>
Date:   Fri Jul 7 10:29:12 2023 +0500

    iframe meta removed(which added previouslY)

[33mcommit 58a2a980b69cd821a03b62903d1d3e436f8164cb[m
Author: usman <<EMAIL>>
Date:   Fri Jul 7 10:10:43 2023 +0500

    iframe meta removed(which added previouslY)

[33mcommit 4a89cdda8bde867b0376c6a852450315741f38b4[m
Author: usman <<EMAIL>>
Date:   Thu Jul 6 18:51:01 2023 +0500

    ifram issue fixed

[33mcommit b3882f46ebb6898f0507617ec1feb871b32ff8c9[m
Author: usman <<EMAIL>>
Date:   Thu Jul 6 18:29:52 2023 +0500

    changes for deployemtn

[33mcommit d72a2a8468b2eafdbc7d001d6ae72a937bdc1c6d[m
Author: usman <<EMAIL>>
Date:   Thu Jul 6 15:17:07 2023 +0500

    iframe done

[33mcommit 5c733be111254cbc4a8b8467dd35de4920d61820[m
Merge: 41705ae f6f7df1
Author: root <<EMAIL>>
Date:   Thu Jul 6 09:30:21 2023 +0200

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue

[33mcommit f6f7df1fb74c795ec8efc157a1738c447ecd02fc[m
Author: usman <<EMAIL>>
Date:   Thu Jul 6 11:50:45 2023 +0500

    el-dropdown hover functionality

[33mcommit 41705ae03af876d0ca21a53b7df04a5ff56b5236[m
Merge: c8b05b5 f0730a6
Author: root <<EMAIL>>
Date:   Mon Jul 3 12:23:55 2023 +0200

    Merge branch 'dev-main'

[33mcommit f0730a63cc445de118711f1297ba4b78ce07934f[m
Author: usman <<EMAIL>>
Date:   Mon Jul 3 14:42:30 2023 +0500

    dropdown in addresses

[33mcommit c8b05b5c2f3f6c36ec1c373747c92b0d8f3e7506[m
Merge: d713dd0 803908a
Author: root <<EMAIL>>
Date:   Tue Jun 27 15:00:04 2023 +0200

    Merge branch 'dev-main'

[33mcommit 803908a8e2db185fddcad8a2ca54c7ab3138dafd[m
Author: root <<EMAIL>>
Date:   Tue Jun 27 14:59:52 2023 +0200

    serrver

[33mcommit d713dd062828946b3cdc13276c9e8db63c24269d[m
Merge: 75f9a5d fb19eb5
Author: root <<EMAIL>>
Date:   Tue Jun 27 14:56:51 2023 +0200

    Merge branch 'dev-main'

[33mcommit fb19eb5cf77d1dd810f4cfae834bb947a5185e25[m
Author: root <<EMAIL>>
Date:   Tue Jun 27 14:55:52 2023 +0200

    server

[33mcommit 75f9a5dbf39c6ab177a3adaec716bbfd94389754[m
Merge: f707c22 e56d2b8
Author: root <<EMAIL>>
Date:   Tue Jun 27 14:52:18 2023 +0200

    Merge branch 'dev-main'

[33mcommit e56d2b82e14df6237c70459274ce3349dca1db26[m
Author: usman <<EMAIL>>
Date:   Tue Jun 27 11:46:51 2023 +0500

    carrier validations page changes

[33mcommit d06cc484742d8e832c32b3beec97368d01801dc2[m
Author: usman <<EMAIL>>
Date:   Tue Jun 27 10:35:53 2023 +0500

    dimensions list

[33mcommit 9bbd9413eecf589c75e0efcb4ca9541c1d17feca[m
Author: usman <<EMAIL>>
Date:   Mon Jun 26 19:05:09 2023 +0500

    carrier validations crud

[33mcommit 5cc3b10e4f513c17cf2c9b74eadfd4349773f0f9[m
Author: usman <<EMAIL>>
Date:   Thu Jun 22 18:19:00 2023 +0500

    city added in addresses settings

[33mcommit f707c22cdee5f61695f3778801a165e07926dc19[m
Merge: 666847e a34bd7c
Author: root <<EMAIL>>
Date:   Thu Jun 22 14:13:41 2023 +0200

    Merge branch 'dev-main'

[33mcommit a34bd7c43f3155ab30fc360643512b7bd360ff4e[m
Author: usman <<EMAIL>>
Date:   Thu Jun 22 16:56:16 2023 +0500

    reports hide from menu

[33mcommit 1005cee2e0fb436645cf157e7a5df53a38dfecd5[m
Author: usman <<EMAIL>>
Date:   Tue Jun 20 19:02:15 2023 +0500

    balance report partial

[33mcommit da36b06e5366ea1755154a4b1df83df23839b790[m
Author: usman <<EMAIL>>
Date:   Tue Jun 20 18:47:11 2023 +0500

    reports nav and addresses crud

[33mcommit db1212f72e311afe8b31caabba8dbe00e226742a[m
Author: usman <<EMAIL>>
Date:   Tue Jun 20 12:04:08 2023 +0500

    address and dimensions crud

[33mcommit a3c197549d642fde6d93dc7eaae9914695298833[m
Merge: 43203db ac388a1
Author: usman <<EMAIL>>
Date:   Mon Jun 19 16:12:00 2023 +0500

    Merge branch 'dev-main' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 666847ee6c6ba23a001e9c7999f73cc712fcec3d[m
Merge: 01b0cab ac388a1
Author: root <<EMAIL>>
Date:   Thu Jun 15 17:45:27 2023 +0200

    Merge branch 'dev-main'

[33mcommit ac388a14b17066c95a59eef7863780cfccfd6103[m
Author: usman <<EMAIL>>
Date:   Thu Jun 15 19:41:39 2023 +0500

    nav position updated

[33mcommit e990bf4ae1dc326ced9906d64cc50faf4440429f[m
Author: usman <<EMAIL>>
Date:   Thu Jun 15 19:04:47 2023 +0500

    pagination issue fixes

[33mcommit 9b4b9bedba8aa4e441533433006309fd92e3083b[m
Author: usman <<EMAIL>>
Date:   Thu Jun 15 17:08:30 2023 +0500

    modification in shipping country

[33mcommit be2c4016dbd050bdc8ac9c6ae908b55acfe9373c[m
Author: usman <<EMAIL>>
Date:   Thu Jun 15 16:27:04 2023 +0500

    changes in shipping country

[33mcommit fc32ae8c04f9e355eed863dd6a0fd5f7b3e13d80[m
Author: usman <<EMAIL>>
Date:   Thu Jun 15 15:39:10 2023 +0500

    shipping countries changes eg CRUD

[33mcommit 01b0cabea741d3e2cf062d659ea5b5ef2a50348c[m
Merge: 4358bda e2508b3
Author: root <<EMAIL>>
Date:   Wed Jun 14 09:22:10 2023 +0200

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 4358bdad72f1c7886cc83071982dd3ca98b8527a[m
Author: usman <<EMAIL>>
Date:   Wed Jun 14 11:28:13 2023 +0500

    multi parcel shops funcitonality added

[33mcommit e2508b351faa00669fbb01b744f654464406cfaa[m
Author: Mohid <<EMAIL>>
Date:   Tue Jun 13 14:20:42 2023 +0500

    changes in DockerFile

[33mcommit 58915309899dea0cdbe5708828ee867b2e1998e3[m
Author: usman <<EMAIL>>
Date:   Tue Jun 13 12:53:52 2023 +0500

    change for merging, build testing

[33mcommit 43203db451be6475c8fbcd467d22d212f0615620[m
Author: usman <<EMAIL>>
Date:   Tue Jun 13 12:52:51 2023 +0500

    dimensions settings layout

[33mcommit 2bd701441e66d8276d85bfcb93fdec9fa25d2fbc[m
Author: usman <<EMAIL>>
Date:   Tue Jun 13 11:42:27 2023 +0500

    list columsn width updated

[33mcommit 3f7813b951f9318dbc5b11d318c0d738192aed65[m
Author: usman <<EMAIL>>
Date:   Tue Jun 13 11:32:16 2023 +0500

    carrier list update

[33mcommit 1bb2a6294e8fdc0106cec2f94769df4fe4ad07d0[m
Author: usman <<EMAIL>>
Date:   Mon Jun 12 16:25:19 2023 +0500

    logo and carrier id 2 fixes

[33mcommit a7d9bfb91f675de27fdc682fb6f3438c1f4b1ed3[m
Author: usman <<EMAIL>>
Date:   Tue Jun 6 15:10:02 2023 +0500

    zip code issue fixed

[33mcommit 681a3081c280c987d7513b3813f7e41e214e52a7[m
Author: usman <<EMAIL>>
Date:   Fri Jun 2 11:42:39 2023 +0500

    agreement logo changes and others

[33mcommit 27d3f95d52367d78606f29b91b1cdc82a60d1ea9[m
Author: usman <<EMAIL>>
Date:   Tue May 30 11:39:41 2023 +0500

    api integration changes

[33mcommit 38a8214d28936184fc675304a3c3b4e0a82e8abc[m
Author: usman <<EMAIL>>
Date:   Mon May 29 12:20:41 2023 +0500

    cancel button removed

[33mcommit 489b343708567435d8443ab4200f66565acfb954[m
Author: usman <<EMAIL>>
Date:   Tue May 23 16:23:29 2023 +0500

    submmenu bg change

[33mcommit 0abb7f105d0be8d59909cca6e9705b111847db98[m
Author: usman <<EMAIL>>
Date:   Tue May 23 15:26:38 2023 +0500

    merchant service get merchants issue fixed

[33mcommit 04178d5ea3b10e1bfbd8aad6a311cd2d3348dca2[m
Author: usman <<EMAIL>>
Date:   Tue May 23 13:25:43 2023 +0500

    customer listing modified with server side pagination

[33mcommit fb382d55a59891abf2fdcf2521875eab1204fee1[m
Author: usman <<EMAIL>>
Date:   Mon May 22 19:24:43 2023 +0500

    carrier image issue fixed

[33mcommit 78d00fed3f908e05e7812525be4893d4b35b3d7c[m
Author: usman <<EMAIL>>
Date:   Fri May 19 15:57:51 2023 +0500

    admin api transactons pagination changes

[33mcommit 1f6d01bf8234a7cf337a09d8b5c06b2204e158f0[m
Author: usman <<EMAIL>>
Date:   Thu May 18 18:00:27 2023 +0500

    some fixes in agreement, summary and tracking message

[33mcommit 004edfc9660b4d2151ca38def277966f1d5a68cd[m
Author: usman <<EMAIL>>
Date:   Thu May 18 12:33:11 2023 +0500

    aestrick was showing on both side in create customer,fixed

[33mcommit 396cb72b93204b1d5ce6e06381c42539a794ca7b[m
Author: usman <<EMAIL>>
Date:   Wed May 17 20:52:30 2023 +0500

    company code added

[33mcommit 1195c4ce165842dad7e0f2961294f7808180754d[m
Author: usman <<EMAIL>>
Date:   Mon May 15 15:55:16 2023 +0500

    personalized message text updated

[33mcommit 21d4c2f5adc4f1d280e1e6061b768f36c1139634[m
Author: usman <<EMAIL>>
Date:   Fri May 5 17:15:19 2023 +0500

    type fixes issue

[33mcommit 8fbaf2324044276c5bc76e00270c9c53cca8cf40[m
Author: usman <<EMAIL>>
Date:   Fri May 5 16:52:19 2023 +0500

    pdfdocument and monthly invoices and specifications merged

[33mcommit a76eb8a6a40911d96917f90ba8411b6a7c497bdd[m
Merge: b15fc78 40b3372
Author: usman <<EMAIL>>
Date:   Thu May 4 14:53:28 2023 +0500

    type fixes in merchant

[33mcommit b15fc78ae5afaa69b46f47a4a1af14ffcc7aa222[m
Author: usman <<EMAIL>>
Date:   Thu May 4 14:49:35 2023 +0500

    local changes build fixes

[33mcommit 40b3372e42e2df6c3e9b36a17e4bf4627f6d8e93[m
Author: usman <<EMAIL>>
Date:   Thu May 4 14:17:00 2023 +0500

    inovices monthly listing

[33mcommit 68381c7160fcddff55eba5a73f8bd4db5f7ea76e[m
Author: usman <<EMAIL>>
Date:   Wed May 3 12:22:22 2023 +0500

    type fixes

[33mcommit e7fa4ab9076804bb6a54ae8c27dcce4f7ffc21a0[m
Author: usman <<EMAIL>>
Date:   Tue May 2 14:34:57 2023 +0500

    error 403 page

[33mcommit d993588799a54ec49a8acc1cb80babc7aa9f6033[m
Author: usman <<EMAIL>>
Date:   Tue Apr 18 14:06:37 2023 +0500

    product redirect route issue and terms&conditions like updated

[33mcommit 25c802c33ee5a331efb913e665d1da50d4ef9750[m
Author: usman <<EMAIL>>
Date:   Fri Apr 14 15:25:01 2023 +0500

    invoice, add funds pdf

[33mcommit 7da6436c62fabafd64597a7bb392b717ead49338[m
Author: usman <<EMAIL>>
Date:   Thu Apr 13 14:07:54 2023 +0500

    customer status label

[33mcommit 42d409524392812a1c173180d81b89aa27a07bf2[m
Author: usman <<EMAIL>>
Date:   Thu Apr 6 15:31:30 2023 +0500

    some bug fixes and payments design

[33mcommit d95384f46fb4a6754c445efdd70f5db9d9eae247[m
Author: usman <<EMAIL>>
Date:   Tue Apr 4 14:17:38 2023 +0500

    booked page filter

[33mcommit 55398d0c4942dffc58cca17ac03d26479e0d4bfa[m
Author: usman <<EMAIL>>
Date:   Tue Apr 4 11:30:58 2023 +0500

    bug fixes

[33mcommit 3c8f86230e1dee12bf0f100c3741198ae9dd4b30[m
Merge: fc6e9b4 7a75d03
Author: usman <<EMAIL>>
Date:   Mon Apr 3 10:22:00 2023 +0500

    Merge branch 'dev-main' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit fc6e9b413bbab61a80c8a81679cd7a58f0d84064[m
Author: usman <<EMAIL>>
Date:   Fri Mar 31 16:14:46 2023 +0500

    summary tab changes

[33mcommit 7a75d03f18861c7c17d45f97afac28cbac08b9ce[m
Author: akshipvagoo <<EMAIL>>
Date:   Wed Mar 29 16:20:37 2023 +0500

    Update adminvueCI.yml

[33mcommit 68512a10240a86c265f59b7605e743d7ab2cd66b[m
Author: akshipvagoo <<EMAIL>>
Date:   Wed Mar 29 16:08:12 2023 +0500

    Update Dockerfile

[33mcommit 9d7ae602ed9962bc585a5da0d2ca0b27a3d5c856[m
Author: akshipvagoo <<EMAIL>>
Date:   Wed Mar 29 14:59:07 2023 +0500

    Update adminvueCI.yml

[33mcommit cb6af7a15f83b7864980e43cdcdaf0c5daba375c[m
Author: akshipvagoo <<EMAIL>>
Date:   Wed Mar 29 13:39:57 2023 +0500

    Update adminvueCI.yml

[33mcommit 8cf8cd8c2f69367bfad6f0829aaaef5dbceb5cb7[m
Author: akshipvagoo <<EMAIL>>
Date:   Wed Mar 29 13:13:47 2023 +0500

    Update adminvue.conf

[33mcommit e5dbacd846d5853df7fa03b2babfd1516d79cbcb[m
Author: akshipvagoo <<EMAIL>>
Date:   Wed Mar 29 13:13:07 2023 +0500

    Update Dockerfile

[33mcommit 7bb190ededcf10b8351275ad909d779f8710cf0f[m
Merge: ad2f396 ed6a255
Author: root <<EMAIL>>
Date:   Tue Mar 28 08:53:18 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit ad2f396c1704ee678c027fc466f87767043927b8[m
Author: usman <<EMAIL>>
Date:   Tue Mar 28 11:24:24 2023 +0500

    sap new icon

[33mcommit ed6a255c5ad96d359ec5907404f36473085b4c15[m
Author: akshipvagoo <<EMAIL>>
Date:   Mon Mar 27 11:58:43 2023 +0500

    Update adminvueCI.yml

[33mcommit 21cd52a7a4e3b216a0828036ba25da631d487b4d[m
Author: akshipvagoo <<EMAIL>>
Date:   Mon Mar 27 11:55:17 2023 +0500

    Update adminvueCI.yml

[33mcommit 6806484e757e61e2f7ce659a891d4d0b3ab55e82[m
Author: akshipvagoo <<EMAIL>>
Date:   Mon Mar 27 11:23:16 2023 +0500

    Update adminvueCI.yml

[33mcommit 7380f6b168a8d2c3ac6c9f581e763640e98c9280[m
Author: akshipvagoo <<EMAIL>>
Date:   Mon Mar 27 11:04:20 2023 +0500

    Create adminvueCI.yml

[33mcommit 6b0a8e8f8c4199ccf4510388d2169a604031fa91[m
Author: apple <<EMAIL>>
Date:   Mon Mar 27 10:50:00 2023 +0500

    Dockerfile update

[33mcommit a5e7cbe3f26f174217571bf23ce04f629c0e300f[m
Author: apple <<EMAIL>>
Date:   Mon Mar 27 10:29:15 2023 +0500

    Dockerfile added

[33mcommit dbe132080c2eba36b64f901c2c199f8fd2d04154[m
Author: usman <<EMAIL>>
Date:   Thu Mar 23 15:59:13 2023 +0500

    personalized email template prices in template, template hidden

[33mcommit 8c475718ad91a55138d16c44bd5ea638dc3c9a97[m
Author: usman <<EMAIL>>
Date:   Wed Mar 22 14:16:35 2023 +0500

    build fixes

[33mcommit f342ba9ca451cb74b15e903b8884a8c7a268b1ab[m
Author: usman <<EMAIL>>
Date:   Wed Mar 22 11:18:00 2023 +0500

    fixed id concat issue fixed in transactions tble

[33mcommit dac5d4aced5ae8fb1c57d10db3698dbc53924596[m
Author: usman <<EMAIL>>
Date:   Tue Mar 21 18:39:15 2023 +0500

    merchant status change from  detail page

[33mcommit 097525f5f69935afeb8ceb4131df0276b9876f37[m
Author: usman <<EMAIL>>
Date:   Mon Mar 20 18:45:26 2023 +0500

    nav updated

[33mcommit 331c40c78ddc8d8b017f9823cc3e252527622638[m
Author: usman <<EMAIL>>
Date:   Fri Mar 17 15:33:51 2023 +0500

    personalized email issue

[33mcommit 94d22042666924162a46da62cf40e6f0dac25fb9[m
Author: usman <<EMAIL>>
Date:   Wed Mar 15 18:12:01 2023 +0500

    shipping template modifications

[33mcommit 13cf6594feb8c3f721a42f4be6224f9aa8f37306[m
Author: usman <<EMAIL>>
Date:   Tue Mar 14 18:41:54 2023 +0500

    save template

[33mcommit 1e130f7b5402783bdfe110f88394bc5a4fb9a9e5[m
Author: usman <<EMAIL>>
Date:   Thu Mar 9 18:43:39 2023 +0500

    denmark set default country and  disabled in merchant create

[33mcommit 108e1b9bef4364204b6a4b4c5ade32e0cabcb538[m
Author: usman <<EMAIL>>
Date:   Wed Mar 8 11:37:35 2023 +0500

    agreement issues are fixed

[33mcommit 7666d218cbdfc7f78e930a37ad42e5add012e060[m
Author: usman <<EMAIL>>
Date:   Tue Mar 7 17:31:26 2023 +0500

    local changes

[33mcommit 01416fa5ca04b40b0fd4d762fb7d066804822e56[m
Author: usman <<EMAIL>>
Date:   Tue Mar 7 10:57:10 2023 +0500

    price group issue fixed

[33mcommit 53d9b6c7e2f6de9461c1e6a6e4596b1ff475d840[m
Merge: d3f263b b22092b
Author: root <<EMAIL>>
Date:   Fri Mar 3 10:14:59 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit d3f263b0cdb016c1effe8f5931492cbd7070c151[m
Merge: 70b5b9c 0ddaa42
Author: root <<EMAIL>>
Date:   Fri Mar 3 10:14:50 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit b22092b5ee1a3c327fa926421b4c15ddbf103508[m
Merge: 0ddaa42 70b5b9c
Author: root <<EMAIL>>
Date:   Thu Mar 2 07:08:15 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 0ddaa4207000ab0fea6d31691c327fb2e6d98222[m
Author: usman <<EMAIL>>
Date:   Thu Mar 2 11:22:34 2023 +0500

    only one agreement could be added against in price group

[33mcommit 70b5b9c2d4a15b26c647dd32efbea1570120ccd6[m
Merge: fe5fc59 aa535c1
Author: root <<EMAIL>>
Date:   Wed Mar 1 10:04:48 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit aa535c152651ceccf520deefcefb6a71d652e84a[m
Author: usman <<EMAIL>>
Date:   Wed Mar 1 14:06:52 2023 +0500

    fixed:if video removed

[33mcommit 5809ba34961331b6bbe28e0a2ea8c81bf1ad068c[m
Author: usman <<EMAIL>>
Date:   Wed Mar 1 13:06:42 2023 +0500

    dashboardlist date overlfow fixed

[33mcommit 7cfa069377737b686e6c9f0bb27fd4ee5670eb17[m
Author: usman <<EMAIL>>
Date:   Wed Mar 1 13:04:15 2023 +0500

    summary length fixed

[33mcommit fe5fc59fe709d91f98bbd50220113fa30b172035[m
Merge: 4713253 b735566
Author: root <<EMAIL>>
Date:   Wed Mar 1 08:01:12 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit b735566cd51d28326a114f7f7d6c52696ab2231d[m
Author: usman <<EMAIL>>
Date:   Wed Mar 1 13:00:28 2023 +0500

    summary width and font fix

[33mcommit 4713253a88d47bb5f953d6da5b8e3e40aa8d499b[m
Merge: 928b961 276bbb2
Author: root <<EMAIL>>
Date:   Wed Mar 1 06:13:22 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 928b961bed63342c9cd43a5464ab211a978cea63[m
Merge: 5d720c2 77d388f
Author: root <<EMAIL>>
Date:   Wed Mar 1 06:13:17 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 276bbb22ff93b717d5f55d377d57783fb8fef3d2[m
Merge: 5d720c2 ccdabc6
Author: root <<EMAIL>>
Date:   Wed Mar 1 06:12:57 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 77d388facededfdac08837f0d34fa6a0c5315a73[m
Author: usman <<EMAIL>>
Date:   Wed Mar 1 10:50:57 2023 +0500

    local changes

[33mcommit ccdabc6ff08386f78190cb2748e528dbfeb70be9[m
Author: usman <<EMAIL>>
Date:   Tue Feb 28 18:37:23 2023 +0500

    active inacctive filter added

[33mcommit a5ad3bb1176a855e7476aa7cc6abd0472473d0ce[m
Author: usman <<EMAIL>>
Date:   Tue Feb 28 17:07:41 2023 +0500

    merchant status changes

[33mcommit 9ffd425f3b62f3d37b533c907c7c6bcf6659de6b[m
Author: usman <<EMAIL>>
Date:   Tue Feb 28 14:58:58 2023 +0500

    video disabled in onbaroding email

[33mcommit e0d7bcbbbdc2f0a949ac78b0341387800d5996ff[m
Author: usman <<EMAIL>>
Date:   Tue Feb 28 12:38:44 2023 +0500

    video in email templated partial completed

[33mcommit 543524830328fe71ed32cb22f5942f0a442c1d33[m
Author: usman <<EMAIL>>
Date:   Mon Feb 27 14:47:18 2023 +0500

    bug fixes

[33mcommit b04b5c911c37d5219eb8afd10af3b44aa4bb0a9d[m
Author: usman <<EMAIL>>
Date:   Fri Feb 24 17:50:25 2023 +0500

    login page design modified

[33mcommit b2e0dd856651112e8505ba437119c4486cb60378[m
Merge: 024fadf e330771
Author: root <<EMAIL>>
Date:   Thu Feb 23 13:00:26 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit e3307710e5f22022501d878e6fef29240601e1de[m
Author: usman <<EMAIL>>
Date:   Thu Feb 23 17:58:33 2023 +0500

    old agreement fixes

[33mcommit 5d720c2897507b31294b2a026c2bedff590ee53a[m
Merge: 681e89e df1b803
Author: root <<EMAIL>>
Date:   Thu Feb 23 11:32:57 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 024fadf67402cd5a1bec8fcd6fa45c7b22500f72[m
Merge: acbfefa df1b803
Author: root <<EMAIL>>
Date:   Thu Feb 23 11:22:24 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit df1b803b341281de55b1a2846bc611c66a91a87f[m
Author: usman <<EMAIL>>
Date:   Thu Feb 23 16:16:44 2023 +0500

    all removed from checkbox merchant list

[33mcommit 5ec440280dbba89dc71a12cfe4196c9d37a74ac1[m
Author: usman <<EMAIL>>
Date:   Thu Feb 23 16:07:30 2023 +0500

    customer phase1 bug fixes

[33mcommit 681e89ef71e7737011f4d1eb3e0c3b2681bc6388[m
Merge: 32a06bf 7d2baad
Author: root <<EMAIL>>
Date:   Thu Feb 23 09:45:45 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit acbfefa4c862340d1c210ce3eb12aa0f7b8df815[m
Merge: cc07623 0334372
Author: root <<EMAIL>>
Date:   Thu Feb 23 07:42:36 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit cc07623a207239ff73802d42c543297f515d73eb[m
Merge: f87dad0 7d2baad
Author: root <<EMAIL>>
Date:   Thu Feb 23 07:42:32 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 7d2baad9ec18e1d90600fc147eaac0958b272e81[m
Author: usman <<EMAIL>>
Date:   Thu Feb 23 12:40:08 2023 +0500

    bug fixes

[33mcommit 8019550c0ea602716c592934dbca5c9b2d73314d[m
Author: usman <<EMAIL>>
Date:   Thu Feb 23 11:44:08 2023 +0500

    merchant bug fixes

[33mcommit dc101aa669e881e7b76d7d700de3148be90d520f[m
Author: usman <<EMAIL>>
Date:   Wed Feb 22 17:36:38 2023 +0500

    bug fixes

[33mcommit 69be6d4b54153c2dfa95647af6814db8d4526c2d[m
Author: usman <<EMAIL>>
Date:   Tue Feb 21 10:20:27 2023 +0500

    bug fixes

[33mcommit e93203a904900a20990868ec0d2853abd42fff7b[m
Author: usman <<EMAIL>>
Date:   Mon Feb 20 15:13:36 2023 +0500

    login x axis space issue fixed

[33mcommit 0334372a1a86b793ac23d4eee58788f9c6907879[m
Merge: aeea7d9 f87dad0
Author: root <<EMAIL>>
Date:   Fri Feb 17 12:30:28 2023 +0000

    Merge branch 'development-usman'

[33mcommit f87dad0ce5fa887704758d14db997e1c59e4b873[m
Merge: fae670a c18c2b8
Author: root <<EMAIL>>
Date:   Fri Feb 17 12:30:17 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit c18c2b8e1cf81bb03530b2e3e78319fee6a550e7[m
Author: usman <<EMAIL>>
Date:   Fri Feb 17 17:24:47 2023 +0500

    bug fixes

[33mcommit c7053a637f944094f81de68b327552a93d2a1149[m
Author: usman <<EMAIL>>
Date:   Fri Feb 17 12:19:13 2023 +0500

    upload ubsend file 2 fraction in gross profit

[33mcommit b677368926382324932d65dbf929ba22f95ffb35[m
Author: usman <<EMAIL>>
Date:   Fri Feb 17 11:49:40 2023 +0500

    file upload not found issue fixed

[33mcommit 32a06bf2f82fd919602a2d00ee3d37b15eb7e64d[m
Merge: ad19513 d4deb8c
Author: root <<EMAIL>>
Date:   Thu Feb 16 16:10:53 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit d4deb8c61329debe42625ec63076a1692feaab07[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 19:33:16 2023 +0500

    invoice no in invocie tab

[33mcommit ad195134e9348a21749d55a8a8dfd7133732d9b1[m
Merge: 27947c6 ad44121
Author: root <<EMAIL>>
Date:   Thu Feb 16 14:15:38 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit ad44121b88bdec94c1283e6434ff42cc2b5e4a1d[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 19:14:01 2023 +0500

    old agreement detaisl fixed

[33mcommit 27947c67f9fe71693c21e14a3420a4425bca57cc[m
Merge: 5402824 4faaa1c
Author: root <<EMAIL>>
Date:   Thu Feb 16 13:23:00 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 4faaa1c45e6f4af78719fa20bab3a6ccbb7f5afc[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 18:11:39 2023 +0500

    dashaboar dropdown width increased

[33mcommit 540282430ee40cce21b7ef2fcad4ec38fd44d8bf[m
Merge: f3e2b2f 3fa2fc8
Author: root <<EMAIL>>
Date:   Thu Feb 16 13:03:07 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 3fa2fc8da21bfea32bb42634c4a20893804e26a7[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 17:32:59 2023 +0500

    add agreement extra countries added issue fixed

[33mcommit f3e2b2f6f057c2bd8b5c902c1db50d0c78862ee7[m
Merge: 6bf9378 a4d5640
Author: root <<EMAIL>>
Date:   Thu Feb 16 11:30:17 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit a4d5640f4fa7703f45ac6133f2f304ca1d2aed17[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 16:14:41 2023 +0500

    merchant delete

[33mcommit 662eb03aeb12dc3f7ffdccdebf175d017fafd4cd[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 16:02:28 2023 +0500

    delete merchant click

[33mcommit 2e6ddfa3926e376699f2b568ec43f1433df7ecd2[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 14:11:30 2023 +0500

    Role and user issue fixed

[33mcommit 41e1f157aaba508f35512cb9d0da76322a09efb4[m
Author: usman <<EMAIL>>
Date:   Thu Feb 16 12:48:54 2023 +0500

    nav icon changes

[33mcommit aeea7d905640a075965795d01381d18b429357a6[m
Merge: fae670a 035f6e3
Author: root <<EMAIL>>
Date:   Thu Feb 16 07:26:58 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit de9b6bf24c68e7d8675d64a7597f7bdb0d9d8e1d[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 23:05:31 2023 +0500

    custom footer in all templates

[33mcommit 6bf9378b6e4473eac111fa63d5a55f3e6e5dc36a[m
Merge: 6a9ebc1 43f9652
Author: root <<EMAIL>>
Date:   Wed Feb 15 17:43:15 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 6a9ebc1f04b77700f101dc47df8b93f31927f2d0[m
Merge: 214d0b5 035f6e3
Author: root <<EMAIL>>
Date:   Wed Feb 15 17:43:05 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 43f96521540bb6fa784ef882cb86275dda3c856c[m
Merge: 214d0b5 035f6e3
Author: root <<EMAIL>>
Date:   Wed Feb 15 17:42:43 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 035f6e345bc74153e01964cca78ee590fd53d53b[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 22:38:00 2023 +0500

    2 digit fraction in ubsend file

[33mcommit 5b9b942f175c9a213eef9077003dd34593e6cda8[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 22:36:37 2023 +0500

    fixes

[33mcommit afad764c558cb0fad67d6c054a36b6582a7252a5[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 21:51:16 2023 +0500

    currency conversion

[33mcommit 214d0b572e056a78d66cb505c3509b6ef30e8528[m
Merge: 27b06c9 8a7a53a
Author: root <<EMAIL>>
Date:   Wed Feb 15 16:16:55 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 8a7a53a9e7378629004bff7da8d86b42dfb06945[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 21:15:27 2023 +0500

    customer create agreement template

[33mcommit 27b06c94b5e03eef126a7fd4f6e5550aae39cbd8[m
Merge: 4aa1709 b8ea631
Author: root <<EMAIL>>
Date:   Wed Feb 15 16:07:15 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit b8ea631ddd2da3fe42a711f4713f0fd6d1800651[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 21:04:25 2023 +0500

    invoice preview and other fixes

[33mcommit 4aa17092f977d7abc25c8bb011fe90a93fd98b04[m
Merge: d7b5d45 85a33ce
Author: root <<EMAIL>>
Date:   Wed Feb 15 14:34:09 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 85a33cef69625ccefcfa6434dc5d39d350365e97[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 19:32:56 2023 +0500

    transasction and timeline issue fixed

[33mcommit d7b5d45681ba0480cd3a82c0c3dceac55684c599[m
Merge: 8556187 e01f19b
Author: root <<EMAIL>>
Date:   Wed Feb 15 13:56:49 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit e01f19b324cddd0bb471b5a69025d69a53df1beb[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 18:54:15 2023 +0500

    summary and agreement issue fixed

[33mcommit 4f1b85685d1ffbf29e14d625baf1c66547268024[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 18:09:37 2023 +0500

    platofrom issue fixed

[33mcommit f43fce0f7586b76efeaad5f7d522efbe26598169[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 17:49:12 2023 +0500

    price group type disabled

[33mcommit 6cfb8a7a054f854244e0f1a87b1f911c01881f42[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 17:31:46 2023 +0500

    fixes

[33mcommit c2e0a8ec8fff7a2d8abbab560867488425d07504[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 17:14:16 2023 +0500

    agreement related fixes

[33mcommit 8556187350a064a51548f890637739a2880fd330[m
Merge: 9efd839 01dd5da
Author: root <<EMAIL>>
Date:   Wed Feb 15 10:33:17 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit fae670a9ac306be767afd920d1d109d12279d972[m
Merge: 4bf6676 39bba41
Author: root <<EMAIL>>
Date:   Wed Feb 15 10:31:45 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 4bf6676ec9828bb46b63c0f036a7121e60db8e7b[m
Merge: 537f971 01dd5da
Author: root <<EMAIL>>
Date:   Wed Feb 15 10:31:37 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 01dd5da7c73c69a7b1fe020fe3883a9daf5d1518[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 15:28:05 2023 +0500

    bug fixes

[33mcommit 9efd839dd6048adc8309183c2a66026b91f42cbc[m
Merge: 85253e1 5a1cb24
Author: root <<EMAIL>>
Date:   Wed Feb 15 07:30:50 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 85253e1feb2da5c093c0571f9c7f20ebb9d71158[m
Merge: 1bc3070 c4fb8ad
Author: root <<EMAIL>>
Date:   Wed Feb 15 07:30:14 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit c4fb8ad655f88f1aee6ccfe3d8f20bee94ee0356[m
Author: usman <<EMAIL>>
Date:   Wed Feb 15 12:20:54 2023 +0500

    issue fixes

[33mcommit 39bba4152d9a1bc6da1e4b40ee98087a6c854b66[m
Merge: 10222dd 537f971
Author: root <<EMAIL>>
Date:   Tue Feb 14 15:43:40 2023 +0000

    Merge branch 'development-usman'

[33mcommit 537f971b2d1e56d59b880fa4202932699807f996[m
Merge: 8bfec32 a4cffd4
Author: root <<EMAIL>>
Date:   Tue Feb 14 15:43:28 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 10222dde9ac46fc035aec1ed2f719be648becb5d[m
Merge: ba4dfb9 a4cffd4
Author: root <<EMAIL>>
Date:   Tue Feb 14 15:43:14 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 5a1cb24f901ffefd2080a49eab85d68f95ce6343[m
Merge: a112c52 1bc3070
Author: root <<EMAIL>>
Date:   Tue Feb 14 15:42:17 2023 +0000

    Merge branch 'development-usman'

[33mcommit 1bc30706ac16c2dab456c01ced6a4967393cda97[m
Merge: b1c37e5 a4cffd4
Author: root <<EMAIL>>
Date:   Tue Feb 14 15:42:04 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit a112c5299138beed181c598856f4f46cffca2f31[m
Merge: a314b42 a4cffd4
Author: root <<EMAIL>>
Date:   Tue Feb 14 15:41:48 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit a4cffd482fbd108efb91c9461506f0c9c1eefc96[m
Author: usman <<EMAIL>>
Date:   Tue Feb 14 20:31:06 2023 +0500

    one group can be added against one price group

[33mcommit 81fdc0ee2df9cccf8f48aff6146effdff7fea08c[m
Author: usman <<EMAIL>>
Date:   Tue Feb 14 19:20:14 2023 +0500

    counteries multiple, agreement changes on details

[33mcommit 39a8382b0d267c90553b23345edf03e9554b228c[m
Author: usman <<EMAIL>>
Date:   Tue Feb 14 16:30:06 2023 +0500

    add agreement all country add on group add

[33mcommit ba4dfb9c3c733d4062483a9d3ab158fda54e25a4[m
Merge: 7150d33 774f1f8
Author: root <<EMAIL>>
Date:   Tue Feb 14 07:58:56 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 9966373a332d1e375f4cf137b4c0ffbffbc67fe2[m
Author: usman <<EMAIL>>
Date:   Tue Feb 14 12:57:33 2023 +0500

    set password in boarding and updated date fixed

[33mcommit a314b42a752ce405b8d8eea3e86a78d8aa253eb6[m
Merge: 8012117 b1c37e5
Author: root <<EMAIL>>
Date:   Mon Feb 13 15:06:23 2023 +0000

    Merge branch 'development-usman'

[33mcommit b1c37e581939ed1944b6b6b9ed6845d22f8c7312[m
Merge: 438cca4 774f1f8
Author: root <<EMAIL>>
Date:   Mon Feb 13 15:06:09 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 80121174ad3c9c81fa6f35488eaf8b8cfc19a95d[m
Merge: 614af66 774f1f8
Author: root <<EMAIL>>
Date:   Mon Feb 13 15:05:46 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 774f1f83b30f6ea2480491b233f7468634524eb6[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 19:44:13 2023 +0500

    quel agreement width 100%

[33mcommit db9f62f130cefe1bf38309d3c3553126d2a17844[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 19:34:14 2023 +0500

    agreement show on company details

[33mcommit 011035baff93a7b2b88d3ea92099b8552ace7356[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 19:00:39 2023 +0500

    changes

[33mcommit 7c9544c95d56ea4c71cf690f6484c4cbeeb3f7a5[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 18:50:43 2023 +0500

    timeline issue fixed, were not showing

[33mcommit 180134cb36eab3de1e31c4e46d4ea9e6301416c4[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 18:30:09 2023 +0500

    transactions invoice changes

[33mcommit 6eaa640152e98461a4fa816786edfa339d3dc097[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 17:03:51 2023 +0500

    agreement fraction 2 fixes

[33mcommit 7150d33b963ce6ae463bf5762ac195b48bb1e88b[m
Merge: 4c72d07 fc8f019
Author: root <<EMAIL>>
Date:   Mon Feb 13 11:42:09 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 614af66e78c45be80d9e83939a97edf05b8229b3[m
Merge: 441a994 fc8f019
Author: root <<EMAIL>>
Date:   Mon Feb 13 11:14:19 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit fc8f0190fa957d83b6e5e93d260a704261467c2d[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 15:33:25 2023 +0500

    update agreement blur valud issue fixed

[33mcommit 30a0736f4a17f1a77fa217168b771fea3f4b0ada[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 15:00:02 2023 +0500

    discount issue fixed in agreement

[33mcommit 1c895e8d5ac6fd8c8dbb73b95420c10fb5e6d27c[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 12:58:19 2023 +0500

    client details was not showing in summary tab fixed

[33mcommit 547955a04f6c22ad91a6f17fb22b1c4c1fee92ac[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 12:36:46 2023 +0500

    earning and customer spend dates are synced

[33mcommit 3da13771cf16d342367f181689021a378ec31fef[m
Author: usman <<EMAIL>>
Date:   Mon Feb 13 11:50:48 2023 +0500

    sales and carrier column width  modified

[33mcommit 990f5ce4cb9c7b38b6275628b1a93d640fcbb990[m
Author: usman <<EMAIL>>
Date:   Fri Feb 10 18:56:15 2023 +0500

    permissions alignment

[33mcommit 4c72d07c1b9131243beb3bdc0681db54d0a9ed7f[m
Merge: 8bfec32 05cbeda
Author: root <<EMAIL>>
Date:   Fri Feb 10 13:23:59 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 441a994f552ef026bc486bc2d94113ad1aa243ff[m
Merge: 013cac0 05cbeda
Author: root <<EMAIL>>
Date:   Fri Feb 10 13:22:26 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 05cbedac4c98f2049b4ea6b03522ae3a7c183b4f[m
Author: usman <<EMAIL>>
Date:   Fri Feb 10 18:09:59 2023 +0500

    carrier id required and search design fixes

[33mcommit 66e40239ee1476036683450f24120fcf1293ec2a[m
Author: usman <<EMAIL>>
Date:   Fri Feb 10 17:14:08 2023 +0500

    payment receipt in fund section

[33mcommit 035fdab6af4f510357dc30ac9528ad813af36b62[m
Author: usman <<EMAIL>>
Date:   Fri Feb 10 16:41:35 2023 +0500

    payment receipt added

[33mcommit a6f1c565c01a2c05ae35a9b8e3e536e8e0aced49[m
Author: usman <<EMAIL>>
Date:   Fri Feb 10 13:15:28 2023 +0500

    fixes

[33mcommit 5ddf1cd94f8ad0b4262e5cc2f7799a1baac73fbe[m
Author: usman <<EMAIL>>
Date:   Thu Feb 9 18:33:19 2023 +0500

    carrier id removed

[33mcommit 013cac0bdf3606c07297557787c8a181d21ca1b3[m
Merge: a9e4ea5 438cca4
Author: root <<EMAIL>>
Date:   Thu Feb 9 13:08:11 2023 +0000

    Merge branch 'development-usman'

[33mcommit 438cca497fdc0e95998d429586d8e1d39b18a16c[m
Merge: 94ccd32 a4b5fb7
Author: root <<EMAIL>>
Date:   Thu Feb 9 13:08:01 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 8bfec32cb2b425701927a8e76c657ae16b3de8ff[m
Merge: 868bcc0 a4b5fb7
Author: root <<EMAIL>>
Date:   Thu Feb 9 12:43:55 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit a4b5fb762c25c005f5cd526038b85c391c2680c9[m
Author: usman <<EMAIL>>
Date:   Thu Feb 9 17:42:04 2023 +0500

    permissiosn applied on sidebar

[33mcommit 00d864c9e6440b7603650c2b60fb393c70578005[m
Author: usman <<EMAIL>>
Date:   Thu Feb 9 16:51:40 2023 +0500

    templates preview added

[33mcommit 56424a6f7a51118f83fa1afae9709ac67326f908[m
Author: usman <<EMAIL>>
Date:   Thu Feb 9 11:41:41 2023 +0500

    redirect to dashboard after login

[33mcommit a9e4ea5a46c7e3c6297e19d68e65c32ddfdf8098[m
Merge: 4296277 94ccd32
Author: root <<EMAIL>>
Date:   Wed Feb 8 13:52:07 2023 +0000

    server
    ;

[33mcommit 94ccd32dfc5248e82655bd7b35f603bd8dd670b6[m
Merge: b919cbf 0f0d1c9
Author: root <<EMAIL>>
Date:   Wed Feb 8 13:51:13 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 4296277f24050ab1491031bddec1aa03fb6b2636[m
Merge: b919cbf 8f4e7b4
Author: root <<EMAIL>>
Date:   Wed Feb 8 13:50:52 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 0f0d1c91597c0530cfa348ba2e034b9054d10498[m
Merge: 54fc7db 8f4e7b4
Author: usman <<EMAIL>>
Date:   Wed Feb 8 18:50:29 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 868bcc069f47b168032c5a4583373b5707363429[m
Merge: 82e63a3 54fc7db
Author: root <<EMAIL>>
Date:   Wed Feb 8 13:48:49 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 54fc7dbf56bf0e249543b7e934e87ae09cec62df[m
Author: KhalilAsghar544 <<EMAIL>>
Date:   Wed Feb 8 18:45:25 2023 +0500

    Dashboard issue, password reset validation,

[33mcommit 82e63a34cdf9eba09ed3535368d0f25903f56eb5[m
Merge: 659f5ca 0c15b0b
Author: root <<EMAIL>>
Date:   Wed Feb 8 11:10:37 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 0c15b0b09fa1c1fee709486aba903a5ff4c63085[m
Merge: 009247e 8f4e7b4
Author: root <<EMAIL>>
Date:   Wed Feb 8 11:09:56 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 659f5ca68c3fd30ad23fc398884ce177b11eb253[m
Author: usman <<EMAIL>>
Date:   Wed Feb 8 15:47:34 2023 +0500

    dashboard last customers and merchant changes

[33mcommit 774d6cf1121721b4c75a38f788e3b10b09712dfe[m
Author: usman <<EMAIL>>
Date:   Wed Feb 8 12:49:46 2023 +0500

    Carrier image update fix

[33mcommit 3bdf07dfec703f7542fc79e76acb6eeb9d989896[m
Author: KhalilAsghar544 <<EMAIL>>
Date:   Wed Feb 8 12:41:51 2023 +0500

    File upload issue fixed in carrier and some meue hides

[33mcommit 7a2ebe99309e466315a62d315bfa3383b3088d00[m
Author: KhalilAsghar544 <<EMAIL>>
Date:   Wed Feb 8 11:25:27 2023 +0500

    User code and  valid file validation in video image

[33mcommit 8f4e7b4de7f8cccfa9092d676dc183448e6c1554[m[33m ([m[1;31morigin/waqar-dev[m[33m, [m[1;31morigin/MajidChanges[m[33m)[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Wed Feb 8 10:34:04 2023 +0500

    Price Group update

[33mcommit b919cbfcdc1157342f79b940ae04c1168ecff9ac[m
Merge: fbab006 acba2a7
Author: root <<EMAIL>>
Date:   Tue Feb 7 14:13:58 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit acba2a7f1e785fa6a21a5f048b32ce820a072361[m
Merge: 668baa8 a963279
Author: usman <<EMAIL>>
Date:   Tue Feb 7 19:11:14 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 668baa8ca6ced40d50e8252ae5d14accd8c6362d[m
Author: usman <<EMAIL>>
Date:   Tue Feb 7 19:10:42 2023 +0500

    company name shows in customer email

[33mcommit fbab006dd84a4059c4d2c7917bc4c8aad163778e[m
Merge: 16c0252 f26be7c
Author: root <<EMAIL>>
Date:   Tue Feb 7 13:34:54 2023 +0000

    Merge branch 'development-usman' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit f26be7c061e3e04eaee4099e42b230e562d3f5ac[m
Author: usman <<EMAIL>>
Date:   Tue Feb 7 18:30:27 2023 +0500

    token replaced with otp

[33mcommit 16c0252936e0068ae16c409bcc6b2a67bc6db472[m
Merge: 3247247 a963279
Author: root <<EMAIL>>
Date:   Tue Feb 7 13:09:22 2023 +0000

    Merge branch 'main' into development-usman

[33mcommit 8dc52b2ab70497591e2af0119c4c35b2e1d5ee7e[m
Author: root <<EMAIL>>
Date:   Tue Feb 7 17:45:12 2023 +0500

    weight class, and forget password

[33mcommit a9632796140a6725f4e1300a637de2a553bd6e50[m
Merge: 0d0d8fb c4761f1
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Feb 7 17:36:10 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into main

[33mcommit 0d0d8fbab9d79887863484934ecf29d3a387eee0[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Feb 7 17:35:49 2023 +0500

    Fix

[33mcommit c4761f1494f2d7a0284f893ae178c1d2c593d2ac[m
Merge: 44ecc7f 60696e8
Author: root <<EMAIL>>
Date:   Tue Feb 7 10:57:04 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 009247eb28b90f343b4c88169979638bc6225426[m
Merge: 60696e8 3247247
Author: root <<EMAIL>>
Date:   Tue Feb 7 10:55:18 2023 +0000

    Merge branch 'development-usman'

[33mcommit 25132565a085f27ee2050020dcad8b6898858dfe[m
Merge: 3247247 60696e8
Author: root <<EMAIL>>
Date:   Tue Feb 7 15:54:32 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into development-usman

[33mcommit 32472470705670ecd3f86d51894efa2161458387[m
Author: root <<EMAIL>>
Date:   Tue Feb 7 15:40:13 2023 +0500

    fiel drag and drop fixes, file type fixe

[33mcommit 60696e8c15d98a65fa1250bb7fb7ba1c9e98e999[m[33m ([m[1;32mwaqar-dev[m[33m)[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Feb 7 15:35:40 2023 +0500

    Side Menu

[33mcommit c94dcb9d4769129c6ebbd2baee9a17d3e1161c67[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Feb 7 14:56:13 2023 +0500

    Currency

[33mcommit 9e2399fa306d127591c848f190e9cf7eb71df84e[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Feb 7 13:16:20 2023 +0500

    fixed Multiple issue

[33mcommit dedd3ab7b53dc30305e39be9c6166cb51eb028c6[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Feb 7 10:51:16 2023 +0500

    remove delete

[33mcommit 44ecc7ff76a341e30731a521b26d65d71ab3d419[m
Merge: 0c77900 0ac7020
Author: root <<EMAIL>>
Date:   Mon Feb 6 15:30:53 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 0ac7020d56af8a9ea00ae979ded311db34078dbf[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Mon Feb 6 20:29:48 2023 +0500

    Changes

[33mcommit 0c7790075a4e2343200b27dcc483c0d985efdd13[m
Merge: 3bfa2e4 9d7656e
Author: root <<EMAIL>>
Date:   Mon Feb 6 15:21:17 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 9d7656e687ab903a23117aa5244260d939c25dab[m
Merge: b8a9053 fb034d7
Author: Waqar Yazdani <<EMAIL>>
Date:   Mon Feb 6 20:19:28 2023 +0500

    merged code

[33mcommit b8a9053eb02be1329b14c7aaca28970f49065c2a[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Mon Feb 6 20:17:11 2023 +0500

    Agreement Update

[33mcommit fb034d760a31d0ddafdd4372bc75d6c100274127[m
Author: root <<EMAIL>>
Date:   Mon Feb 6 19:13:42 2023 +0500

    login page image replaced, dashboard blank issue, status update api and rich text editor fixed

[33mcommit 3bfa2e42b5a63fbfff86a872e903bd8f3f786fff[m
Merge: 629b21d 33aa068
Author: root <<EMAIL>>
Date:   Mon Feb 6 11:03:17 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 33aa0682af0bc727759c55ddc8cb3f9d8232aadc[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Mon Feb 6 16:00:36 2023 +0500

    CHanges

[33mcommit 6ec943f028415dac240d287992104ef7da8dd680[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Mon Feb 6 15:05:00 2023 +0500

    Fixes

[33mcommit 629b21d294dad17598dd259e1de1154df7796eeb[m
Merge: ac9131b 627a527
Author: root <<EMAIL>>
Date:   Mon Feb 6 09:55:22 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 627a5272c3c3d7ca3f8cd5d04d161461a4f049a6[m
Merge: 5cae3a5 55ab520
Author: Waqar Yazdani <<EMAIL>>
Date:   Mon Feb 6 10:32:33 2023 +0500

    Resolved Conflicts

[33mcommit 55ab5200bbcd924985796fbd9eddca28ba0e2485[m
Author: majid <<EMAIL>>
Date:   Fri Feb 3 15:13:19 2023 +0000

    Add five Tamplate

[33mcommit 5cae3a5e5fd12c6fe3d3cce4ddcd7d426de81491[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Fri Feb 3 17:41:33 2023 +0500

    Changes

[33mcommit bb85ec663d2ba2c6582a739473fb4774cafb36d7[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Fri Feb 3 16:46:34 2023 +0500

    Changes

[33mcommit 0dc414bf47d71daacb034c22e891a18fa6fa0f38[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Fri Feb 3 12:11:29 2023 +0500

    Validation fixed

[33mcommit 813d182f9844a806ecbcb796b47aee6cb611622f[m
Merge: 351ddfb 9c8efee
Author: Waqar Yazdani <<EMAIL>>
Date:   Fri Feb 3 12:03:29 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into waqar-dev

[33mcommit 351ddfbf5f02d48e43a3693d05c3913c1eb8c3b4[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Fri Feb 3 12:03:11 2023 +0500

    Changes Regarding Preview

[33mcommit 9c8efeeaebc0a36660c2f36f56c4beb5c57f1ff1[m
Merge: 157e9dc 5dc39c5
Author: root <<EMAIL>>
Date:   Fri Feb 3 06:30:45 2023 +0000

    Merge branch 'fahad-dev' of https://github.com/akshipvagoo/admin-vue

[33mcommit 5dc39c5bc501447bf4448306c79536a47a1a20c2[m[33m ([m[1;31morigin/fahad-dev[m[33m)[m
Author: fahad4787 <<EMAIL>>
Date:   Fri Feb 3 11:13:58 2023 +0500

    11:15/03-02-2k23

[33mcommit 0632590bda37761c560bad225476c4391ae4a046[m
Author: fahad4787 <<EMAIL>>
Date:   Fri Feb 3 11:08:34 2023 +0500

    11:08am/03-02-2k23

[33mcommit 157e9dc1c98c3e97e07ffde279ecc0e4249f060d[m
Author: majid <<EMAIL>>
Date:   Thu Feb 2 12:29:59 2023 +0000

    Change date and compant name

[33mcommit 63c57d6b90b069b2a56b015e744fa39ad93b18e6[m
Author: majid <<EMAIL>>
Date:   Wed Feb 1 14:19:45 2023 +0000

    Majid Task

[33mcommit ac9131b86bfdd9ef840bee84d16df0dde3451e8b[m
Merge: 8bd5966 62fcbd8
Author: root <<EMAIL>>
Date:   Wed Feb 1 10:36:33 2023 +0000

    server

[33mcommit 62fcbd81d5568f23c91ff2422403c82305cc37e7[m
Merge: 10698fb 8f84b1f
Author: root <<EMAIL>>
Date:   Wed Feb 1 07:40:12 2023 +0000

    Merge branch 'waqar-dev' of https://github.com/akshipvagoo/admin-vue

[33mcommit 8f84b1f21b9fdc33b32c95ffa49e2ed29ca6ac82[m
Merge: 395f6ca 31ef657
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Jan 31 19:57:59 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into waqar-dev

[33mcommit 395f6caffa0a5ab8f440884bf1f726a9d70539a1[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Jan 31 19:57:47 2023 +0500

    Video

[33mcommit 10698fb9b20e7f439d843df6460aae6db31c86a9[m
Author: root <<EMAIL>>
Date:   Tue Jan 31 13:35:47 2023 +0000

    server

[33mcommit 8bd59660b17fa98641408fd76173fc3a87e2d1fb[m
Merge: 9b02c63 31ef657
Author: root <<EMAIL>>
Date:   Tue Jan 31 13:11:41 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit df4e51825ccc5dc0252ca39f279b65ad76caee80[m
Merge: 7bed66b 31ef657
Author: root <<EMAIL>>
Date:   Tue Jan 31 13:09:47 2023 +0000

    server

[33mcommit 31ef6570ab0c214475e88cb289f39b62744d594e[m
Merge: 41f116a 2328edf
Author: wilayatshah <<EMAIL>>
Date:   Tue Jan 31 17:50:12 2023 +0500

    Merge branch 'main' of github.com:akshipvagoo/admin-vue
    
    # Conflicts:
    #       src/views/pages/template/emailTemplate/index.vue

[33mcommit 41f116a6ee0aa8707463fb49f10d2f3d6369e451[m
Author: wilayatshah <<EMAIL>>
Date:   Tue Jan 31 17:47:42 2023 +0500

    Update Agreeement not working

[33mcommit 2328edf0636dc9eb1ca51e93ca377d42d4408e7f[m
Author: Waqar Yazdani <<EMAIL>>
Date:   Tue Jan 31 16:16:37 2023 +0500

    Video Listing

[33mcommit 8d3c1c671c3d22caeb6c35398a9e3d28397e7311[m
Author: majid <<EMAIL>>
Date:   Tue Jan 31 07:34:23 2023 +0000

    Privew Page data set

[33mcommit 9b02c635405061bbc9b0991da558f1109b8b1f52[m
Merge: cea1f7d 49f8a80
Author: root <<EMAIL>>
Date:   Tue Jan 31 06:19:19 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 7bed66b0f1a18bac0340215b66d80e2ef6860f1f[m
Merge: 058ec00 49f8a80
Author: root <<EMAIL>>
Date:   Tue Jan 31 06:17:40 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 49f8a806b221a24e5ecc82988d82c90e6d24eeef[m
Author: majid <<EMAIL>>
Date:   Mon Jan 30 13:59:41 2023 +0000

    Changing

[33mcommit cea1f7de5f8c321731d17e377d57f87bc1dd0244[m
Merge: 8abeeaa a30dc38
Author: root <<EMAIL>>
Date:   Mon Jan 30 09:42:01 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 058ec00ad484ac3cba391bba919fdf055974e6e3[m
Merge: da1c59a a30dc38
Author: root <<EMAIL>>
Date:   Mon Jan 30 09:40:25 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit a30dc38bfdc2d14a559749489157d379d265c5a5[m
Author: wilayatshah <<EMAIL>>
Date:   Mon Jan 30 11:14:15 2023 +0500

    - in listing of customer if user have not agreement then on hovering icon of agreement show text as "Agreement is not yet created. Please create agreement"

[33mcommit 43692b2aeaafe33e4849ffcf298d199e64367b5a[m
Author: wilayatshah <<EMAIL>>
Date:   Mon Jan 30 10:31:07 2023 +0500

    - in listing of customer if user have not agreement then on hovering icon of agreement show text as "Agreement is not yet created. Please create agreement"

[33mcommit d025fc6c4536017874d0c088e22e91f08df175b3[m
Author: wilayatshah <<EMAIL>>
Date:   Mon Jan 30 10:29:28 2023 +0500

    - in listing of customer if user have not agreement then on hovering icon of agreement show text as "Agreement is not yet created. Please create agreement"

[33mcommit da1c59a687b6ce7a7d8c06bc21549a222a5e8cc1[m
Merge: 171e18c c8cd433
Author: root <<EMAIL>>
Date:   Mon Jan 30 05:28:45 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit c8cd433e58ade82d3beaac3a3348da8ace0a9a34[m
Author: wilayatshah <<EMAIL>>
Date:   Mon Jan 30 10:22:06 2023 +0500

    Customer - Customer List - New/Edit
    - Add format validation on email
    - Add format validation of Platform URL and Company URL
    - Add duplication validation in Old agreement Details on country. User should not b able to add two rows with same Country from and country to

[33mcommit ebc9c3ba9335ad88a15ce1205828c665f9f25ee3[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 22:39:57 2023 +0500

    UBsend File Upload done

[33mcommit 30d5b314c550c14cb5fbd6c866b10a72dbd37d05[m
Merge: 4dae9c5 06a1f98
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 20:03:59 2023 +0500

    Merge branch 'main' of github.com:akshipvagoo/admin-vue

[33mcommit 4dae9c542eb0fffed878aaa5437240d854d87271[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 20:03:49 2023 +0500

    image added to carrier

[33mcommit 06a1f981064b9ec41395a1359429ce63fff2055d[m
Merge: 72c77ce d4987d7
Author: khawarjavaid <<EMAIL>>
Date:   Sat Jan 28 16:09:45 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 72c77ce7e67b00481eef0d3490a2350cdd158694[m
Author: khawarjavaid <<EMAIL>>
Date:   Sat Jan 28 16:09:42 2023 +0500

    Update SystemUserList.vue

[33mcommit 171e18cdc507b80af256b9ce65b10ad73578079f[m
Merge: 9acd2cc d4987d7
Author: root <<EMAIL>>
Date:   Sat Jan 28 10:22:31 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 8abeeaab4619f77950125be5928f2e17a15a1df8[m
Merge: 41e76e8 d4987d7
Author: root <<EMAIL>>
Date:   Sat Jan 28 10:21:29 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit d4987d74df164eab71fd09dff3b1cbe40fa72c28[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 14:12:25 2023 +0500

    image added to carrier

[33mcommit 3a1f93a8a779eccf6bfb5605200993f561094a34[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 14:08:57 2023 +0500

    image added to carrier

[33mcommit 354591e82ee90e91c114c35b36f1ae3b2f0b32d9[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 13:59:24 2023 +0500

    image added to carrier

[33mcommit 9acd2cc7e3f71b32eab9394f99a077ed95842b24[m
Merge: 7524fdb e48e9b6
Author: root <<EMAIL>>
Date:   Sat Jan 28 07:31:03 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 41e76e8f1c74b8883a90d8ae1ab41a05d55cada7[m
Merge: f58363a e48e9b6
Author: root <<EMAIL>>
Date:   Sat Jan 28 07:29:58 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit e48e9b665a397a655e4fd754da15941b7984f991[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 12:25:55 2023 +0500

    Scroll fixed

[33mcommit 81069ab54764a4b4b16ef43eecec7ca16ac9e722[m
Author: wilayatshah <<EMAIL>>
Date:   Sat Jan 28 12:07:43 2023 +0500

    Sales Tab
    -Vertical Total
    -Summary field : remove margin/padding

[33mcommit 7524fdb99781170b3f17c38a8fca5f8521edc6e5[m
Merge: 5a23f5d 435aeb5
Author: root <<EMAIL>>
Date:   Fri Jan 27 14:25:54 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit f58363a8d795d96364d3f38f02e9c78407400bcb[m
Merge: 1865274 435aeb5
Author: root <<EMAIL>>
Date:   Fri Jan 27 14:25:09 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 435aeb5999cf69aa94e088f246e87015653c3c8e[m
Merge: 8e4021b 4b0a977
Author: majid <<EMAIL>>
Date:   Fri Jan 27 14:20:39 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue into main

[33mcommit 8e4021b1ff8c512ee0c650159e03b995565bb606[m
Author: majid <<EMAIL>>
Date:   Fri Jan 27 14:19:52 2023 +0000

    Crud Weight Classes

[33mcommit 18652745b6051849d54c66f08d41db9785f4f511[m
Merge: 0211d30 4b0a977
Author: root <<EMAIL>>
Date:   Fri Jan 27 11:09:22 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 5a23f5dc83e6fa5fc914ecffebf1383cc276b737[m
Merge: 6932ad0 4b0a977
Author: root <<EMAIL>>
Date:   Thu Jan 26 17:48:37 2023 +0000

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue

[33mcommit 4b0a977aba515464b81d765493f42303f37fc716[m
Merge: c7dff9e c5d85f8
Author: khawarjavaid <<EMAIL>>
Date:   Thu Jan 26 21:27:06 2023 +0500

    Merge branch 'main' of https://github.com/akshipvagoo/admin-vue
